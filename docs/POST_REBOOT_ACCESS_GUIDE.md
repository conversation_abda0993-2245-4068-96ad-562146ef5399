# 🔄 Post-Reboot OpenStack Horizon Access Guide

## ⚠️ **IMPORTANT: Port Forwarding is NOT Persistent**

The `oc port-forward` command is **temporary** and will be lost when:
- ✅ Laptop reboots
- ✅ Terminal session closes  
- ✅ Network disconnects
- ✅ Process is killed
- ✅ OpenShift connection drops

---

## 🚀 **QUICK RESTORATION METHODS**

### **Method 1: Automated Script (Recommended)**
```bash
cd /Users/<USER>/openshift-virt
./scripts/restore_horizon_access.sh
```
This script will:
- ✅ Check and restore hosts entry
- ✅ Verify cluster connectivity
- ✅ Start port forwarding automatically
- ✅ Provide status updates

### **Method 2: Manual Steps**
```bash
# 1. Restore hosts entry (if removed)
sudo sh -c 'echo "127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local" >> /etc/hosts'

# 2. Navigate to project directory
cd /Users/<USER>/openshift-virt

# 3. Set kubeconfig
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"

# 4. Start port forwarding
./bin/oc port-forward svc/horizon 9443:443 -n openstack &
```

### **Method 3: Background Service (Advanced)**
For automatic restoration on boot, you can install a LaunchAgent:

```bash
# Copy the LaunchAgent file
cp /Users/<USER>/openshift-virt/config/com.user.openstack.horizon.plist ~/Library/LaunchAgents/

# Load the service
launchctl load ~/Library/LaunchAgents/com.user.openstack.horizon.plist

# To unload later:
# launchctl unload ~/Library/LaunchAgents/com.user.openstack.horizon.plist
```

---

## 🔧 **TROUBLESHOOTING AFTER REBOOT**

### **Check Current Status**
```bash
# Verify hosts entry
grep horizon /etc/hosts

# Check if port forwarding is active
lsof -i :9443

# Test cluster connectivity
cd /Users/<USER>/openshift-virt
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"
./bin/oc get pods -n openstack
```

### **Common Issues & Solutions**

#### **1. "Connection Refused" Error**
```bash
# Restart port forwarding
pkill -f "port-forward.*horizon"
cd /Users/<USER>/openshift-virt
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"
./bin/oc port-forward svc/horizon 9443:443 -n openstack &
```

#### **2. "Host Not Found" Error**
```bash
# Re-add hosts entry
sudo sh -c 'echo "127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local" >> /etc/hosts'
```

#### **3. "Cluster Unreachable" Error**
```bash
# Check if OpenShift cluster is running
# Ensure VMs/cluster nodes are powered on
# Verify network connectivity
```

---

## 📋 **POST-REBOOT CHECKLIST**

After laptop reboot, verify these components:

### **1. OpenShift Cluster Status**
- [ ] Cluster nodes are running
- [ ] OpenShift API is accessible
- [ ] OpenStack namespace is healthy

### **2. Network Configuration**
- [ ] Hosts entry exists: `grep horizon /etc/hosts`
- [ ] No port conflicts: `lsof -i :9443`
- [ ] Network connectivity to cluster

### **3. OpenStack Services**
- [ ] Control plane is ready
- [ ] Horizon pod is running
- [ ] Service endpoints are available

### **4. Access Restoration**
- [ ] Port forwarding active
- [ ] Dashboard accessible in browser
- [ ] Login credentials working

---

## 🎯 **BOOKMARK THESE COMMANDS**

```bash
# Quick status check
cd /Users/<USER>/openshift-virt && ./scripts/restore_horizon_access.sh

# Manual port forward restart
pkill -f "port-forward.*horizon" && cd /Users/<USER>/openshift-virt && export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress" && ./bin/oc port-forward svc/horizon 9443:443 -n openstack &

# Test dashboard access
curl -k https://horizon-openstack.apps.sno-rhoso.lab.local:9443/dashboard/
```

---

## 🌐 **ACCESS INFORMATION (Always Current)**

**Dashboard URL:** `https://horizon-openstack.apps.sno-rhoso.lab.local:9443/dashboard/`

**Login Credentials:**
- **Username:** `admin`
- **Password:** `OrtL0EKtnzCpWFAjhWD08MW77`
- **Domain:** `Default`

---

## 🚨 **WHY PORT FORWARDING ISN'T PERSISTENT**

Port forwarding via `oc port-forward` is designed as a **development/debugging tool**, not a permanent solution. It:

- Runs as a **foreground/background process** in your terminal
- Depends on **active session** and **network connectivity**
- Is **terminated** when the parent process ends
- Requires **continuous connection** to the Kubernetes API

For production environments, you would typically use:
- **Ingress controllers** with proper DNS
- **LoadBalancer services** with external IPs
- **NodePort services** with direct node access
- **VPN connections** to the cluster network

---

## 💡 **TIP: Create an Alias**

Add this to your `~/.zshrc` for quick access:

```bash
alias restore-horizon='cd /Users/<USER>/openshift-virt && ./scripts/restore_horizon_access.sh'
```

Then just run: `restore-horizon` after any reboot!

---

**Remember: Always run the restoration script after laptop reboots to regain access to your OpenStack dashboard.**
