# Deployment Plan: Red Hat OpenStack Services on OpenShift 18.0 (Single Node OpenShift on Proxmox)

## 1. Prepare the Proxmox VM for Single Node OpenShift (SNO)
- **Create a VM on Proxmox** (host `pve01`) with at least **8 vCPUs** and **120 GB** of disk storage (these are the minimum requirements for a single-node OpenShift cluster). Allocate **16 GB RAM** (32 GB recommended). Ensure the VM’s firmware is UEFI and enable nested virtualization (VT-x/AMD-V).
- **Network Interfaces:** Attach at least one network interface to the VM bridged to your lab network.  
- **VM BIOS and CPU:** In Proxmox, set the VM CPU type to **host** or add the **VMX** flag. Enable UEFI boot.
- **Storage Layout:** Use a disk of at least 120 GB for `/var` and container storage.

## 2. Install Single Node OpenShift 4.18 on the VM (Assisted Installer Method)
1. **Obtain Pull Secret:** From Red Hat OpenShift Cluster Manager.
2. **Launch Assisted Installer:** In Cluster Manager, “Create New Cluster” → Installer-Provisioned → Single Node OpenShift (SNO).
3. **Download Discovery ISO:** Boot the Proxmox VM from this ISO; it installs RHCOS and registers with the installer.
4. **Complete via Web UI:** Approve discovered host, provide networking details, and start installation.
5. **Monitor Progress:** Wait until status is “Installed” and web console is reachable.
6. **Access Cluster:** Use `oc login https://api.<cluster>.<domain>:6443 -u kubeadmin -p <password>` and verify `oc get nodes`.

## 3. Register and Configure Red Hat Subscriptions
- **Cluster Subscription:** Handled by pull secret.
- **Data Plane Nodes:**
  - Create `subscription-manager` secret in `openstack` namespace:
    ```yaml
    apiVersion: v1
    kind: Secret
    metadata:
      name: subscription-manager
      namespace: openstack
    data:
      username: <base64_user>
      password: <base64_pass>
    ```
  - Enable RHOSO and required repos via the operator (no manual steps).
- **Registry Credentials:** Create `redhat-registry` secret in `openstack`:
  ```bash
  oc create secret generic redhat-registry     --from-literal edpm_container_registry_logins='{"registry.redhat.io": {"<user>": "<pass>"}}'     -n openstack
  ```

## 4. Install the OpenStack Operator
1. **OperatorHub → OpenStack Operator** (provider: Red Hat).
2. **Install** in namespace `openstack-operators`.
3. **Create OpenStack CR:** In `openstack-operators`, click “Create OpenStack” to instantiate.
4. **Wait for Ready** status on the operator CR.

## 5. Configure OpenShift for OpenStack Services
### 5.1 OpenStack Namespace & Security
```bash
oc new-project openstack
oc label namespace openstack pod-security.kubernetes.io/enforce=privileged --overwrite
oc label namespace openstack security.openshift.io/scc.podSecurityLabelSync=false --overwrite
```

### 5.2 Persistent Storage
- Install Local Storage (LVM) Operator → create LVMCluster & StorageClass (e.g., `local-storage`).
- Mark default if needed:
  ```bash
  oc patch storageclass local-storage -p '{"metadata":{"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'
  ```

### 5.3 Networking (MetalLB & Multus)
- **Install NMState Operator** (if needed) and **MetalLB Operator**.
- **IPAddressPool** for `ctlplane` and `internalapi`:
  ```yaml
  apiVersion: metallb.io/v1beta1
  kind: IPAddressPool
  metadata:
    name: ctlplane
    namespace: metallb-system
  spec:
    addresses:
    - **************-**************
  ---
  kind: IPAddressPool
  metadata:
    name: internalapi
    namespace: metallb-system
  spec:
    addresses:
    - ***********-***********
  ```
- **L2Advertisement** for each pool.
- Validate with `oc get ipaddresspools -n metallb-system`.

### 5.4 OpenStack Service Passwords Secret
Generate and base64-encode service passwords:
```bash
PASSWORD=$(tr -dc 'A-Za-z0-9' < /dev/urandom | head -c32)
echo -n $PASSWORD | base64
```
Create `openstack_service_secret.yaml` with:
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: osp-secret
  namespace: openstack
data:
  AdminPassword: <base64>
  DatabasePassword: <base64>
  # ... other keys ...
```
Apply:
```bash
oc create -f openstack_service_secret.yaml -n openstack
```

## 6. Deploy the OpenStack Control Plane
Create `openstack_control_plane.yaml`:
```yaml
apiVersion: core.openstack.org/v1beta1
kind: OpenStackControlPlane
metadata:
  name: openstack-control-plane
  namespace: openstack
spec:
  secret: osp-secret
  storageClass: local-storage
```
Apply:
```bash
oc create -f openstack_control_plane.yaml -n openstack
```
Monitor:
```bash
oc get pods -n openstack -w
oc get openstackcontrolplane openstack-control-plane -n openstack -o yaml
```

## 7. Deploy the Data Plane (Compute Node)
### 7.1 Prepare RHEL Compute Node
- VM with RHEL 9, user `cloud-admin` with passwordless sudo.
- SSH public key from `ansible-ssh-key.pub` in `/home/<USER>/.ssh/authorized_keys`.

### 7.2 Create Secrets
```bash
oc create secret generic dataplane-ansible-ssh-private-key-secret   --from-file=ssh-privatekey=ansible-ssh-key   --from-file=ssh-publickey=ansible-ssh-key.pub   -n openstack

oc create secret generic nova-migration-ssh-key   --from-file=ssh-privatekey=nova-migration-ssh-key   --from-file=ssh-publickey=nova-migration-ssh-key.pub   -n openstack

# libvirt secret
oc apply -f - <<EOF
apiVersion: v1
kind: Secret
metadata:
  name: libvirt-secret
  namespace: openstack
data:
  LibvirtPassword: <base64_password>
EOF
```

### 7.3 NodeSet CR
`openstack_preprovisioned_node_set.yaml`:
```yaml
apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneNodeSet
metadata:
  name: openstack-data-plane
  namespace: openstack
spec:
  preProvisioned: true
  networkAttachments:
    - ctlplane
  nodeTemplate:
    ansibleSSHPrivateKeySecret: dataplane-ansible-ssh-private-key-secret
    managementNetwork: ctlplane
    ansible:
      ansibleUser: cloud-admin
      ansiblePort: 22
      ansibleVarsFrom:
        - prefix: subscription_manager_
          secretRef:
            name: subscription-manager
        - secretRef:
            name: redhat-registry
      ansibleVars:
        edpm_bootstrap_command: |
          subscription-manager register --username {{ subscription_manager_username }} --password {{ subscription_manager_password }}
          subscription-manager attach --pool=<pool_id>
          subscription-manager release --set=9.4
          subscription-manager repos --disable=*             --enable=rhel-9-for-x86_64-baseos-eus-rpms             --enable=rhel-9-for-x86_64-appstream-eus-rpms             --enable=rhel-9-for-x86_64-highavailability-eus-rpms             --enable=fast-datapath-for-rhel-9-x86_64-rpms             --enable=rhoso-18.0-for-rhel-9-x86_64-rpms             --enable=rhceph-7-tools-for-rhel-9-x86_64-rpms
  nodes:
    - name: compute-0
      ansibleHost: <compute_node_IP>
      managementNetwork: ctlplane
```
Apply:
```bash
oc apply -f openstack_preprovisioned_node_set.yaml -n openstack
```

### 7.4 DataPlaneDeployment CR
`openstack_data_plane_deploy.yaml`:
```yaml
apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneDeployment
metadata:
  name: data-plane-deploy
  namespace: openstack
spec:
  nodeSets:
    - openstack-data-plane
```
Apply and monitor:
```bash
oc create -f openstack_data_plane_deploy.yaml -n openstack
oc get pods -n openstack -l app=openstackansibleee -w
```

- After success, register compute host:
```bash
oc rsh nova-cell0-conductor-0 nova-manage cell_v2 discover_hosts --verbose
```

## 8. Verify Deployment
- `oc rsh -n openstack openstackclient`
- `openstack hypervisor list`
- `openstack compute service list`
- Check PVCs: `oc get pvc -n openstack`
- Test VM launch (optional)

**End of Plan**