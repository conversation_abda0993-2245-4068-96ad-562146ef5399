# OpenStack Horizon Dashboard Access Guide

## 🎉 SUCCESS: Port Forwarding Active!

The OpenStack Horizon dashboard is now accessible from your laptop via port forwarding.

### 🌐 Access Information

**URL:** https://localhost:8443

**Credentials:**
- **Username:** `admin`
- **Password:** `OrtL0EKtnzCpWFAjhWD08MW77`
- **Domain:** `Default`

### 🔒 Important Notes

1. **SSL Certificate Warning**: Your browser will show a security warning because the dashboard uses a self-signed certificate. This is normal for development environments.

2. **To Accept the Certificate:**
   - Click "Advanced" or "Show Details"
   - Click "Proceed to localhost (unsafe)" or "Accept Risk and Continue"

3. **Port Forward Status**: The port forwarding is currently running in the background (PID: process visible with `lsof -i :8443`)

### 🚀 Next Steps

1. **Open your web browser**
2. **Navigate to:** https://localhost:8443
3. **Accept the SSL certificate warning**
4. **Login with the credentials above**

### 🛠️ Managing Port Forward

**To stop port forwarding:**
```bash
# Find the process
lsof -i :8443
# Kill the process (replace PID with actual process ID)
kill <PID>
```

**To restart port forwarding:**
```bash
cd /Users/<USER>/openshift-virt
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"
./bin/oc port-forward svc/horizon 8443:443 -n openstack &
```

### 📱 Alternative Access Methods

If port forwarding doesn't work, you can also:

1. **OpenStack CLI Access:**
   ```bash
   ./bin/oc exec -it openstackclient -n openstack -- bash
   ```

2. **Direct API Access:**
   All OpenStack APIs are available via their individual routes (though they use the same .lab.local domain issue)

### 🎯 What You Can Do in Horizon

Once logged in, you'll be able to:
- **View OpenStack services** status
- **Manage projects and users**
- **Create and manage networks**
- **Upload and manage images**
- **Create volumes** (when storage is configured)
- **Launch instances** (when compute nodes are added)

---

**Status:** ✅ OpenStack Control Plane Fully Deployed and Accessible!
