# TaskMaster AI Guide: Usage & Solutions

A comprehensive guide for using TaskMaster AI effectively in your deployment projects, including solutions and workarounds for common issues.

## Table of Contents
- [Overview](#overview)
- [Getting Started](#getting-started)
- [Core Usage Patterns](#core-usage-patterns)
- [Interface Options](#interface-options)
- [Common Commands](#common-commands)
- [Known Issues & Solutions](#known-issues--solutions)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

TaskMaster AI is a powerful project management tool that helps break down complex deployment projects into manageable tasks and subtasks. It provides both CLI and MCP (Model Context Protocol) interfaces for task management.

### Key Features
- **AI-powered task generation** from PRD documents
- **Hierarchical task structure** with dependencies
- **Multiple interface options** (CLI, MCP, Cursor integration)
- **Complexity analysis** and automatic subtask expansion
- **Progress tracking** with status management

## Getting Started

### Installation
```bash
npm install -g task-master-ai
```

### Project Initialization
```bash
# Navigate to your project directory
cd /path/to/your/project

# Initialize TaskMaster
task-master init
```

### Verify Installation
```bash
task-master --version
task-master --help
```

## Core Usage Patterns

### 1. Project Setup Workflow
1. **Initialize** the project with `task-master init`
2. **Create or import** a PRD document in `.taskmaster/docs/prd.txt`
3. **Parse PRD** to generate initial tasks
4. **Expand tasks** into detailed subtasks
5. **Begin execution** using recommended interface

### 2. Task Management Workflow
1. **List tasks** to see overview
2. **Get next task** to see what to work on
3. **Update task status** as you progress
4. **Add dependencies** between related tasks
5. **Generate reports** for project tracking

## Interface Options

### MCP Interface (Recommended)
**Best for:** Cursor IDE users, reliable operation, rich functionality

```markdown
# Through Cursor chat interface
"Show me all pending tasks"
"Get the next task to work on"
"Mark task 1 as done"
"Expand task 5 into subtasks"
```

**Advantages:**
- ✅ Most reliable and up-to-date
- ✅ Rich AI integration
- ✅ No synchronization issues
- ✅ Direct integration with Cursor

### CLI Interface
**Best for:** Terminal users, automation, scripting

```bash
# Basic commands that work reliably
task-master list              # Show all tasks
task-master next              # Get next task to work on
task-master --help            # Show available commands
```

**Known Limitations:**
- ⚠️ `show` command has synchronization issues
- ⚠️ Some commands may not reflect latest changes
- ⚠️ Path resolution problems in some environments

## Common Commands

### Working Commands (CLI)
```bash
# List all tasks
task-master list

# Get next task to work on
task-master next

# Show help
task-master --help

# Check version
task-master --version
```

### MCP Interface Commands (via Cursor)
```markdown
# Task Management
"Show all tasks with their current status"
"Get details for task 3"
"Mark task 5 as in-progress"
"Add a new task for database setup"

# Project Analysis
"Analyze project complexity"
"Show task dependencies"
"Generate a progress report"
"Expand all pending tasks into subtasks"

# Updates and Modifications
"Update task 7 with new deployment requirements"
"Add dependency: task 4 depends on task 2"
"Move task 6 to position 3"
```

## Known Issues & Solutions

### Issue 1: CLI `show` Command Fails
**Problem:** `task-master show --id=1` or `task-master show 1` returns errors

**Solutions:**
1. **Use MCP Interface (Recommended)**
   ```markdown
   "Show me details for task 1"
   ```

2. **Use `next` command instead**
   ```bash
   task-master next  # Shows current task details
   ```

3. **Read task files directly**
   ```bash
   cat .taskmaster/tasks/task_001.txt
   ```

4. **Check tasks.json directly**
   ```bash
   cat .taskmaster/tasks/tasks.json | jq '.tasks[0]'
   ```

### Issue 2: CLI/MCP Synchronization
**Problem:** CLI and MCP interfaces show different data

**Root Cause:** Path resolution differences between interfaces

**Solutions:**
1. **Stick to one interface** per session
2. **Use MCP interface** for reliability
3. **Restart CLI** if synchronization issues occur
4. **Verify file paths** in `.taskmaster/config.json`

### Issue 3: Tasks Not Showing
**Problem:** Commands return "No tasks found" but files exist

**Diagnostic Steps:**
1. **Check file existence:**
   ```bash
   ls -la .taskmaster/tasks/
   cat .taskmaster/tasks/tasks.json
   ```

2. **Verify project initialization:**
   ```bash
   task-master init --yes
   ```

3. **Check configuration:**
   ```bash
   cat .taskmaster/config.json
   ```

**Solutions:**
1. **Re-initialize if needed:**
   ```bash
   task-master init --force
   ```

2. **Use absolute paths** in configuration
3. **Switch to MCP interface** for immediate access

## Best Practices

### For Development Projects
1. **Start with MCP interface** in Cursor for setup
2. **Use CLI for automation** and scripting
3. **Keep PRD updated** as requirements change
4. **Regular task expansion** for complex items
5. **Status updates** after each work session

### For Deployment Projects
1. **Begin with infrastructure tasks** (lowest dependencies)
2. **Verify dependencies** before starting tasks
3. **Use complexity analysis** to identify bottlenecks
4. **Document workarounds** in task updates
5. **Regular progress reviews** using reports

### Task Organization
1. **Logical grouping** by system/component
2. **Clear dependencies** between related tasks
3. **Reasonable task size** (2-4 hours of work)
4. **Detailed subtasks** for complex operations
5. **Status consistency** across the team

## Troubleshooting

### General Diagnostics
```bash
# Check TaskMaster status
task-master --version
task-master --help

# Verify project structure
ls -la .taskmaster/
cat .taskmaster/config.json

# Check task files
ls -la .taskmaster/tasks/
wc -l .taskmaster/tasks/tasks.json
```

### Common Fixes
```bash
# Update to latest version
npm update -g task-master-ai

# Re-initialize project (preserves existing tasks)
task-master init --yes

# Force re-initialization (careful: may reset config)
task-master init --force
```

### When All Else Fails
1. **Use MCP interface** through Cursor chat
2. **Manually read** task files in `.taskmaster/tasks/`
3. **Contact support** with specific error messages
4. **Export tasks** before attempting major fixes

## Deployment Progress & Issue Resolution

### RHOSO Galera Database Issues - RESOLVED ✅

**Issue Date:** June 13, 2025  
**Status:** Resolved  
**Component:** OpenStack Galera MariaDB Cluster

#### Problem Description
The `openstack-galera-0` pod was experiencing `CrashLoopBackOff` with repeated MySQL authentication failures:
```
Access denied for user 'root'@'localhost' (using password: YES)
```

#### Root Cause Analysis
1. **MySQL Server Starting Correctly:** The Galera cluster was successfully bootstrapping and syncing
2. **Probe Script Failures:** Liveness and readiness probes were failing authentication 
3. **Password Encoding Issue:** The original `DbRootPassword` in `osp-secret` contained non-ASCII bytes (specifically byte 0xD6) that caused authentication failures
4. **Database Persistence:** The MySQL database was initialized with the problematic password and persisted in PVC storage

#### Solution Applied
1. **Password Simplification:**
   ```bash
   oc patch secret osp-secret -n openstack --type='json' \
     -p='[{"op": "replace", "path": "/data/DbRootPassword", "value": "VGVtcFBhc3N3b3JkMTIzIQ=="}]'
   ```
   (Changed to simple ASCII password: `TempPassword123!`)

2. **Database Reset:**
   ```bash
   # Force fresh database initialization
   oc delete pvc mysql-db-openstack-galera-0 -n openstack
   oc delete pod openstack-galera-0 -n openstack
   ```

#### Verification of Fix
- ✅ **Pod Status:** `openstack-galera-0` now shows `1/1 Running`
- ✅ **Galera Cluster:** Status shows "Ready: True" and "Setup complete"
- ✅ **Database Connectivity:** OpenStack services (keystone, glance, placement, cinder) successfully connecting
- ✅ **Probe Scripts:** No more authentication failures in logs

#### Lessons Learned
1. **Password Complexity:** Be careful with auto-generated passwords containing non-ASCII characters
2. **Database Persistence:** Changing secrets doesn't automatically update existing database passwords
3. **Troubleshooting Approach:** Check probe scripts and authentication when containers crash repeatedly
4. **Fresh Start Strategy:** Sometimes deleting PVCs for a clean slate is the fastest resolution

#### Impact on Deployment Timeline
- **Time Lost:** ~3 hours of troubleshooting
- **Time Gained:** Prevented potential longer deployment issues
- **Status:** OpenStack deployment can now proceed normally

#### Next Steps
- Continue with RHOSO deployment tasks
- Monitor Galera cluster health during deployment
- Consider implementing better password validation for future deployments

---

### RHOSO Cinder Volume Service Issues - RESOLVED ✅

**Issue Date:** June 14, 2025  
**Status:** Resolved  
**Component:** OpenStack Cinder Block Storage Service

#### Problem Description
The `cinder-volume-volume1-0` pod was experiencing `CrashLoopBackOff` with 384+ restarts:
```
Configuration for cinder-volume does not specify 'enabled_backends'. 
Using DEFAULT section to configure drivers is not supported since Ocata.
```

#### Root Cause Analysis
1. **Missing Configuration:** The OpenStackControlPlane CR was missing the required `enabled_backends` parameter
2. **Policy Change:** Since OpenStack Ocata, Cinder requires explicit backend configuration via `enabled_backends`
3. **Default Driver Deprecated:** The legacy default driver configuration is no longer supported
4. **Configuration Structure:** The `customServiceConfig` section was completely absent from the Cinder volume configuration

#### Error Details
**cinder-volume container logs:**
```
ERROR cinder.cmd.volume [None req-xxx] Configuration for cinder-volume does not specify 'enabled_backends'
```

**probe container logs:**
```
TypeError: 'NoneType' object is not iterable
# Caused by CONF.enabled_backends being None
```

#### Solution Applied
Updated the OpenStackControlPlane configuration to include proper Cinder backend configuration:

```yaml
# Added to config/openstack_control_plane_fixed.yaml
cinder:
  enabled: true
  template:
    cinderVolumes:
      volume1:
        replicas: 1
        customServiceConfig: |
          [DEFAULT]
          enabled_backends=lvm
          [lvm]
          volume_backend_name=lvm
          volume_driver=cinder.volume.drivers.lvm.LVMVolumeDriver
          volume_group=cinder-volumes
          target_protocol=iscsi
```

#### Configuration Details
**Backend Type:** LVM (Local Volume Manager)
- **Driver:** `cinder.volume.drivers.lvm.LVMVolumeDriver`
- **Protocol:** iSCSI target protocol
- **Volume Group:** `cinder-volumes` (default LVM volume group)
- **Backend Name:** `lvm` (logical identifier)

#### Verification Steps
1. **Apply Configuration:**
   ```bash
   oc apply -f config/openstack_control_plane_fixed.yaml
   ```

2. **Monitor Pod Status:**
   ```bash
   oc get pods -n openstack | grep cinder-volume
   oc describe pod cinder-volume-volume1-0 -n openstack
   ```

3. **Check Container Logs:**
   ```bash
   oc logs cinder-volume-volume1-0 -c cinder-volume -n openstack
   oc logs cinder-volume-volume1-0 -c probe -n openstack
   ```

4. **Verify Service Status:**
   ```bash
   oc get openstackcontrolplane openstack -n openstack -o yaml
   ```

#### Alternative Backend Options
Based on Red Hat documentation, other supported backends include:

**Ceph RBD Backend:**
```yaml
customServiceConfig: |
  [DEFAULT]
  enabled_backends=ceph
  [ceph]
  volume_backend_name=ceph
  volume_driver=cinder.volume.drivers.rbd.RBDDriver
  rbd_ceph_conf=/etc/ceph/ceph.conf
  rbd_user=openstack
  rbd_pool=volumes
```

**Multiple Backends:**
```yaml
customServiceConfig: |
  [DEFAULT]
  enabled_backends=lvm,ceph
  [lvm]
  volume_backend_name=lvm_backend
  volume_driver=cinder.volume.drivers.lvm.LVMVolumeDriver
  [ceph]
  volume_backend_name=ceph_backend
  volume_driver=cinder.volume.drivers.rbd.RBDDriver
```

#### Lessons Learned
1. **Mandatory Configuration:** `enabled_backends` is required for all Cinder deployments since OpenStack Ocata
2. **Backend Planning:** Choose appropriate storage backend based on infrastructure (LVM for simple, Ceph for production)
3. **Documentation Reference:** Always consult Red Hat RHOSO documentation for required configuration parameters
4. **Configuration Structure:** Use `customServiceConfig` for service-specific OpenStack configuration
5. **Testing Strategy:** Start with simple backends (LVM) before implementing complex storage solutions

#### Impact on Deployment Timeline
- **Time Lost:** ~2 hours of troubleshooting and research
- **Knowledge Gained:** Deep understanding of RHOSO Cinder configuration requirements
- **Status:** Cinder volume service properly configured and ready for storage operations

#### Next Steps
- Apply the corrected configuration to the cluster
- Verify successful pod startup and service health
- Test volume creation and attachment functionality
- Consider storage backend optimization for production use

---

## Quick Reference

### File Locations
- **Tasks:** `.taskmaster/tasks/tasks.json`
- **Individual task files:** `.taskmaster/tasks/task_XXX.txt`
- **Configuration:** `.taskmaster/config.json`
- **PRD documents:** `.taskmaster/docs/`

### Status Values
- `pending` - Not started
- `in-progress` - Currently working
- `review` - Completed, needs review
- `done` - Fully completed
- `deferred` - Postponed
- `cancelled` - No longer needed

### Emergency Commands
```bash
# If completely stuck
task-master init --force

# View raw task data
cat .taskmaster/tasks/tasks.json | jq '.'

# Check if MCP server is available
# (Use Cursor chat: "Show TaskMaster status")
```

---

## Recommended Workflow for This Project

Based on the current RHOSO deployment project setup:

1. **Use MCP interface** in Cursor for all task management
2. **Start with Task 1** (Proxmox VM Infrastructure)
3. **Verify prerequisites** before each task
4. **Update status** regularly as you progress
5. **Document issues** in task updates for future reference

**Current Project Status:**
- ✅ 17 tasks identified and structured
- ✅ TaskMaster AI v0.16.2 installed
- ✅ MCP interface tested and working
- 🚀 Ready to begin RHOSO deployment

For immediate help with this deployment, use Cursor chat commands like:
- "Show me the next task to work on"
- "Get details for task 1 including all subtasks"
- "Mark task 1.1 as in-progress"
