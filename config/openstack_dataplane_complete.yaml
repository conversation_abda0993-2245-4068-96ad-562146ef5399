---
# OpenStack Data Plane Node Set Configuration
# This configures compute nodes for the OpenStack deployment

apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneNodeSet
metadata:
  name: openstack-edpm-nodes
  namespace: openstack
spec:
  # For pre-provisioned (existing) compute nodes
  preProvisioned: true
  
  # Services to deploy on compute nodes
  services:
    - bootstrap
    - download-cache
    - configure-network
    - validate-network
    - install-os
    - configure-os
    - ssh-known-hosts
    - run-os
    - reboot-os
    - install-certs
    - ovn
    - neutron-metadata
    - libvirt
    - nova
    
  # Node template with common configuration
  nodeTemplate:
    ansibleSSHPrivateKeySecret: dataplane-ansible-ssh-private-key-secret
    ansible:
      ansibleUser: cloud-admin
      ansiblePort: 22
      ansibleVarsFrom:
        - prefix: subscription_manager_
          secretRef:
            name: subscription-manager
        - secretRef:
            name: redhat-registry
      ansibleVars:
        # Configure subscription manager for RHEL nodes
        edpm_bootstrap_command: |
          subscription-manager register --username {{ subscription_manager_username }} --password {{ subscription_manager_password }}
          subscription-manager release --set=9.4
          subscription-manager repos --disable=* \
            --enable=rhel-9-for-x86_64-baseos-eus-rpms \
            --enable=rhel-9-for-x86_64-appstream-eus-rpms \
            --enable=rhel-9-for-x86_64-highavailability-eus-rpms \
            --enable=fast-datapath-for-rhel-9-x86_64-rpms \
            --enable=rhoso-18.0-for-rhel-9-x86_64-rpms \
            --enable=rhceph-7-tools-for-rhel-9-x86_64-rpms
        
        # Network configuration
        edpm_network_config_template: |
          network_config:
            - type: ovs_bridge
              name: br-ctlplane
              use_dhcp: false
              addresses:
                - ip_netmask: {{ ctlplane_ip }}/{{ ctlplane_cidr }}
              routes:
                - ip_netmask: 0.0.0.0/0
                  next_hop: {{ ctlplane_gateway }}
              members:
                - type: interface
                  name: {{ ansible_default_ipv4_interface }}
                  primary: true
        
        # Enable necessary services
        edpm_sshd_configure_firewall: true
        edpm_sshd_allowed_ranges:
          - 10.0.0.0/8
          - ***********/16
          - **********/12
  
  # Define the compute nodes
  # NOTE: Replace with actual compute node IPs when available
  nodes:
    - name: compute-0
      ansibleHost: "REPLACE_WITH_COMPUTE_NODE_IP"
      # For demonstration - typically this would be a separate RHEL 9 VM
      # Example: ansibleHost: "**********" 

---
apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneDeployment
metadata:
  name: edpm-deployment
  namespace: openstack
spec:
  nodeSets:
    - openstack-edpm-nodes
