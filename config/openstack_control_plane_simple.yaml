apiVersion: core.openstack.org/v1beta1
kind: OpenStackControlPlane
metadata:
  name: openstack
  namespace: openstack
spec:
  secret: osp-secret
  storageClass: hostpath-csi
  
  # DNS Configuration
  dns:
    template:
      options:
        - key: server
          values:
          - *******
        - key: server
          values:
          - *******
  
  # Database Configuration
  galera:
    enabled: true
    templates:
      openstack:
        secret: osp-secret
        storageRequest: 500M
        storageClass: hostpath-csi
        replicas: 1
    
  # Message Queue Configuration  
  rabbitmq:
    templates:
      rabbitmq:
        storageRequest: 500M
        storageClass: hostpath-csi
        replicas: 1

  # Memory Cache Configuration
  memcached:
    enabled: true
    templates:
      memcached:
        replicas: 1

  # Identity Service (Keystone)
  keystone:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      replicas: 1

  # Placement API
  placement:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      replicas: 1

  # Image Service (Glance)
  glance:
    enabled: true
    apiOverrides:
      glance:
        route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      storage:
        storageRequest: "10G"
        storageClass: hostpath-csi
      glanceAPIs:
        glance:
          replicas: 1
          type: single

  # Block Storage Service (Cinder)  
  cinder:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      cinderAPI:
        replicas: 1
      cinderScheduler:
        replicas: 1
      cinderBackup:
        replicas: 0
      cinderVolumes:
        volume1:
          replicas: 1

  # Compute Service (Nova)
  nova:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      apiServiceTemplate:
        replicas: 1
      metadataServiceTemplate:
        replicas: 1
      schedulerServiceTemplate:
        replicas: 1
      conductorServiceTemplate:
        replicas: 1

  # Networking Service (Neutron)
  neutron:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret
      neutronAPI:
        replicas: 1

  # Dashboard (Horizon)
  horizon:
    enabled: true
    apiOverride:
      route: {}
    template:
      secret: osp-secret
      replicas: 1

  # Minimal service configuration for Single Node OpenShift
  ovn:
    enabled: true
    template:
      ovnDBCluster:
        ovndbcluster-nb:
          replicas: 1
          storageRequest: 500M
        ovndbcluster-sb:  
          replicas: 1
          storageRequest: 500M
      ovnNorthd:
        replicas: 1
      ovnController:
        external-ids:
          system-id: "random"
          ovn-bridge: "br-int"
          ovn-encap-type: "geneve"
