# OpenStack Secrets Configuration
# This file contains templates for creating Red Hat subscription and registry access secrets

# Template for Subscription Manager Secret
# Replace <username>, <password>, and <org-id> with actual Red Hat credentials
apiVersion: v1
kind: Secret
metadata:
  name: subscription-manager
  namespace: openstack
type: Opaque
data:
  rhsm-username: <base64-encoded-username>
  rhsm-password: <base64-encoded-password>
  rhsm-org: <base64-encoded-org-id>

---
# Template for Red Hat Registry Secret
# This will use the same pull secret from OpenShift installation
apiVersion: v1
kind: Secret
metadata:
  name: redhat-registry
  namespace: openstack
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <base64-encoded-pull-secret>

---
# Template for RHOSO Container Registry Secret
apiVersion: v1
kind: Secret
metadata:
  name: registry-redhat-io
  namespace: openstack
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <base64-encoded-pull-secret>
