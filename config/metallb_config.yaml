# MetalLB Configuration for OpenStack
# This configuration sets up IP address pools for OpenStack services

---
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
  name: ctlplane
  namespace: metallb-system
spec:
  addresses:
  - **********-**********
  autoAssign: false

---
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
  name: internalapi
  namespace: metallb-system
spec:
  addresses:
  - ***********-***********
  autoAssign: false

---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
  name: ctlplane
  namespace: metallb-system
spec:
  ipAddressPools:
  - ctlplane
  interfaces:
  - br-ex

---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
  name: internalapi
  namespace: metallb-system
spec:
  ipAddressPools:
  - internalapi
  interfaces:
  - br-ex
