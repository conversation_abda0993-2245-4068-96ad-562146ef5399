# OpenStack Data Plane Configuration
# Create this after Cinder volume service is working

apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneNodeSet
metadata:
  name: openstack-compute
  namespace: openstack
spec:
  preProvisioned: true
  networkAttachments:
    - ctlplane
  nodeTemplate:
    ansibleSSHPrivateKeySecret: dataplane-ansible-ssh-private-key-secret
    managementNetwork: ctlplane
    ansible:
      ansibleUser: cloud-admin
      ansiblePort: 22
      ansibleVarsFrom:
        - prefix: subscription_manager_
          secretRef:
            name: subscription-manager
        - secretRef:
            name: redhat-registry
      ansibleVars:
        edpm_bootstrap_command: |
          subscription-manager register --username {{ subscription_manager_username }} --password {{ subscription_manager_password }}
          subscription-manager attach --pool=<pool_id>
          subscription-manager repos --disable=* \
            --enable=rhel-9-for-x86_64-baseos-eus-rpms \
            --enable=rhel-9-for-x86_64-appstream-eus-rpms \
            --enable=rhoso-18.0-for-rhel-9-x86_64-rpms
  nodes:
    - name: compute-0
      ansibleHost: <COMPUTE_NODE_IP>  # Replace with actual IP
      managementNetwork: ctlplane

---
apiVersion: dataplane.openstack.org/v1beta1
kind: OpenStackDataPlaneDeployment
metadata:
  name: openstack-compute-deploy
  namespace: openstack
spec:
  nodeSets:
    - openstack-compute
