# Final Cinder Volume Service Configuration - HostPath Solution
# Apply this to complete the OpenStack 18 deployment

# Simple hostPath backend for lab environment
# This uses local storage on the OpenShift node for Cinder volumes

apiVersion: core.openstack.org/v1beta1
kind: OpenStackControlPlane
metadata:
  name: openstack-control-plane
  namespace: openstack
spec:
  cinder:
    enabled: true
    template:
      cinderVolumes:
        volume1:
          replicas: 1  # Enable the volume service
          customServiceConfig: |
            [DEFAULT]
            enabled_backends=hostpath
            default_volume_type=hostpath
            [hostpath]
            volume_backend_name=hostpath
            volume_driver=cinder.volume.drivers.lvm.LVMVolumeDriver
            volume_group=cinder-volumes
            target_protocol=iscsi
            target_helper=lioadm
