apiVersion: machineconfiguration.openshift.io/v1
kind: MachineConfig
metadata:
  labels:
    machineconfiguration.openshift.io/role: master
  name: 99-master-var-partition
spec:
  config:
    ignition:
      version: 3.2.0
    storage:
      filesystems:
        - device: /dev/disk/by-label/root
          format: xfs
          path: /var
          wipeFilesystem: false
          mountOptions:
            - defaults
            - prjquota
    systemd:
      units:
        - name: var-lib-containers-expand.service
          enabled: true
          contents: |
            [Unit]
            Description=Expand /var/lib/containers
            After=local-fs.target
            Before=crio.service
            ConditionPathExists=!/var/lib/rpm-ostree/.var-lib-containers-expanded
            
            [Service]
            Type=oneshot
            ExecStart=/bin/bash -c 'if [[ $(df --output=avail /var | tail -1) -lt 51200000 ]]; then echo "Expanding /var partition"; growpart /dev/vda 3; xfs_growfs /var; fi'
            ExecStart=/bin/touch /var/lib/rpm-ostree/.var-lib-containers-expanded
            RemainAfterExit=yes
            
            [Install]
            WantedBy=multi-user.target
