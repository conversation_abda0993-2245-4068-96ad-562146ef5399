apiVersion: core.openstack.org/v1beta1
kind: OpenStackControlPlane
metadata:
  name: openstack
  namespace: openstack
spec:
  cinder:
    enabled: true
    template:
      cinderVolumes:
        volume1:
          replicas: 1  # Enable the volume service (was 0)
          customServiceConfig: |
            [DEFAULT]
            enabled_backends=hostpath
            default_volume_type=hostpath
            [hostpath]
            volume_backend_name=hostpath
            volume_driver=cinder.volume.drivers.lvm.LVMVolumeDriver
            volume_group=cinder-volumes
            target_protocol=iscsi
            target_helper=lioadm
