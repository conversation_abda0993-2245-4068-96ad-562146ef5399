variant: openshift
version: 4.17.0
metadata:
  name: disk-partition
  labels:
    machineconfiguration.openshift.io/role: master
openshift:
  kernel_arguments:
    - rd.multipath=0
    - rd.neednet=1
    - coreos.inst.install_dev=/dev/vda
    - coreos.inst.ignition_url=data:text/plain;base64,ewogICJpZ25pdGlvbiI6IHsKICAgICJ2ZXJzaW9uIjogIjMuMi4wIgogIH0sCiAgInN0b3JhZ2UiOiB7CiAgICAiZGlza3MiOiBbCiAgICAgIHsKICAgICAgICAiZGV2aWNlIjogIi9kZXYvdmRhIiwKICAgICAgICAid2lwZVRhYmxlIjogdHJ1ZSwKICAgICAgICAicGFydGl0aW9ucyI6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgImxhYmVsIjogImJpb3MiLAogICAgICAgICAgICAic2l6ZU1pQiI6IDEsCiAgICAgICAgICAgICJ0eXBlR3VpZCI6ICIyMTY4NjE0OC02ZjY0LTQ3OWEtYjRlYi04ZTM2OGI3MTM2NTEiCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICAibGFiZWwiOiAiYm9vdCIsCiAgICAgICAgICAgICJzaXplTWlCIjogMzg0LAogICAgICAgICAgICAidHlwZUd1aWQiOiAiYzEyYTczMjgtZjgxZi0xMWQyLWJhNGItMDBhMGM5M2VjOTNiIgogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgImxhYmVsIjogInJvb3QiLAogICAgICAgICAgICAic2l6ZU1pQiI6IDgxOTIwLAogICAgICAgICAgICAidHlwZUd1aWQiOiAiNGYxNTc4OGEtYjU4Yy00MzNhLTgyNDItZjEyMDJmYWM3OTI1IgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfQogICAgXSwKICAgICJmaWxlc3lzdGVtcyI6IFsKICAgICAgewogICAgICAgICJkZXZpY2UiOiAiL2Rldi9kaXNrL2J5LWxhYmVsL3Jvb3QiLAogICAgICAgICJmb3JtYXQiOiAieGZzIiwKICAgICAgICAicGF0aCI6ICIvIiwKICAgICAgICAid2lwZUZpbGVzeXN0ZW0iOiB0cnVlCiAgICAgIH0sCiAgICAgIHsKICAgICAgICAiZGV2aWNlIjogIi9kZXYvZGlzay9ieS1sYWJlbC9ib290IiwKICAgICAgICAiZm9ybWF0IjogImV4dDQiLAogICAgICAgICJwYXRoIjogIi9ib290IiwKICAgICAgICAid2lwZUZpbGVzeXN0ZW0iOiB0cnVlCiAgICAgIH0KICAgIF0KICB9Cn0=
storage:
  disks:
    - device: /dev/vda
      wipe_table: true
      partitions:
        - number: 1
          label: bios
          size_mib: 1
          type_guid: 21686148-6f64-479a-b4eb-8e368b713651
        - number: 2  
          label: boot
          size_mib: 384
          type_guid: c12a7328-f81f-11d2-ba4b-00a0c93ec93b
        - number: 3
          label: root  
          size_mib: 0  # Use remaining space
          type_guid: 4f1578aa-b58c-433a-8242-f1202fac7925
  filesystems:
    - device: /dev/disk/by-label/root
      format: xfs
      path: /
      wipe_filesystem: true
      with_mount_unit: true
    - device: /dev/disk/by-label/boot  
      format: ext4
      path: /boot
      wipe_filesystem: true
      with_mount_unit: true
systemd:
  units:
    - name: var-lib-containers.mount
      enabled: true
      contents: |
        [Unit]
        Description=Mount /var/lib/containers
        Before=local-fs.target
        
        [Mount]
        What=/dev/disk/by-label/root
        Where=/var/lib/containers
        Type=xfs
        Options=defaults,prjquota,bind
        
        [Install]
        WantedBy=local-fs.target
