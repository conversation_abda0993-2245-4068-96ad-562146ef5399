apiVersion: v1
baseDomain: lab.local
compute:
- name: worker
  replicas: 0
controlPlane:
  name: master
  replicas: 1
metadata:
  name: sno
networking:
  clusterNetwork:
  - cidr: **********/14
    hostPrefix: 23
  machineNetwork:
  - cidr: 10.0.0.0/24
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
bootstrapInPlace:
  installationDisk: /dev/vda  # Main VM disk - ensure at least 200GB for adequate /var space
pullSecret: '{"auths":{"cloud.openshift.com":{"auth":"********************************************************************************************************************************************************************************","email":"<EMAIL>"},"quay.io":{"auth":"********************************************************************************************************************************************************************************","email":"<EMAIL>"},"registry.connect.redhat.com":{"auth":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","email":"<EMAIL>"},"registry.redhat.io":{"auth":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","email":"<EMAIL>"}}}'
sshKey: |
  ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCurg448sD+MZPZo5LMTyDRQ/CdlFDS/x5wRFqoFCdFatcYhl/14Vy+W9WW6+yHo4yWYCIyQQdQajgfxybLJTB3Dq8owd+R4UpFjaZGrAuFPJqRYpcpbrR9zDiujgZEdADRX3nuSXJs5x7FbNtu7cfI+vwSw0FYMOXmO2HTQfPTaLT71EzTCcbk6zPAtEKnE5mTNQMNRsoFtJsa2xp/ayjX9FGq8CR3uuSgYOED7UfBzKXCSsHaiRCsYtvfOEVRKYel8DHEx+MvmemJjWwdq95YMoRMGZYaUUK6Sz3BdKK/4wqmayMFMkLeZu+HgJ34xvjuezksZMppfzEbF+T/S4gxwwled40oX2gW1IlG6mutqnbgu1cTfV9ezu68ccfSue9SQvf2Dy+IJvydHONuWd8x8H1eqRzJSySYNxtOta/0cMdtZbmkEBR3m94wkwAS+2zao1IWcBXOz/BeiMeB7fnL7pKuqybtprobt03A8f3sJwkYchlCfSq5cTXT2Bzc/I1BGxnqMRoXkJxKmTwZIV/0DuJOXjKV8G8aPUovfP5POyS0PmshVIt2srVLHZ2eInrcJilPzckxVN8XYM2syUx0p3eVUs308E7DtnRZgPF/7Jb+GKLryfKVin5EgAkLI15pOKPfs4ajXerV+/IctXnRFFEggze/1UR5OnjGA1GdYw== eduardo@MacBook-Pro
