apiVersion: core.openstack.org/v1beta1
kind: OpenStackControlPlane
metadata:
  name: openstack-control-plane
  namespace: openstack
spec:
  secret: osp-secret
  storageClass: hostpath-csi
  
  # Database Configuration
  galera:
    enabled: true
    storage:
      storageRequest: 500M
      storageClass: hostpath-csi
    
  # Message Queue Configuration  
  rabbitmq:
    templates:
      rabbitmq:
        storageRequest: 500M
        storageClass: hostpath-csi

  # Identity Service (Keystone)
  keystone:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      secret: osp-secret

  # Image Service (Glance)
  glance:
    enabled: true
    apiOverrides:
      default:
        route: {}
    template:
      databaseInstance: openstack
      storage:
        storageRequest: "10G"
        storageClass: hostpath-csi
      glanceAPIs:
        default:
          replicas: 1

  # Block Storage Service (Cinder)
  cinder:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      cinderAPI:
        replicas: 1
      cinderScheduler:
        replicas: 1
      cinderBackup:
        replicas: 0 # Disabled for minimal deployment
      cinderVolumes:
        volume1:
          replicas: 1

  # Compute Service (Nova)
  nova:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      apiServiceTemplate:
        replicas: 1
      metadataServiceTemplate:
        replicas: 1
      schedulerServiceTemplate:
        replicas: 1
      conductorServiceTemplate:
        replicas: 1

  # Networking Service (Neutron)
  neutron:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack
      networkAttachments:
        - ctlplane
      neutronAPI:
        replicas: 1

  # Orchestration Service (Heat)
  heat:
    enabled: true
    apiOverride:
      route: {}
    cnf:
      enabled: false
    template:
      databaseInstance: openstack
      heatAPI:
        replicas: 1
      heatEngine:
        replicas: 1

  # Dashboard (Horizon)
  horizon:
    enabled: true
    apiOverride:
      route: {}
    template:
      replicas: 1

  # Placement Service
  placement:
    enabled: true
    apiOverride:
      route: {}
    template:
      databaseInstance: openstack

  # DNS Service (Designate) - Optional
  designate:
    enabled: false
    
  # Load Balancing Service (Octavia) - Optional
  octavia:
    enabled: false

  # Bare Metal Service (Ironic) - Optional
  ironic:
    enabled: false

  # Networking Configuration
  ovn:
    enabled: true
    template:
      ovnDBCluster:
        ovndbcluster-nb:
          replicas: 1
          storage:
            storageRequest: 10G
            storageClass: local-storage
        ovndbcluster-sb:
          replicas: 1
          storage:
            storageRequest: 10G
            storageClass: local-storage
      ovnNorthd:
        replicas: 1
      ovnController:
        networkAttachment: tenant
