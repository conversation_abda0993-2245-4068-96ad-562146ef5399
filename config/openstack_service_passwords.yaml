# OpenStack Service Passwords Secret Template
# Generate secure passwords for all OpenStack services

apiVersion: v1
kind: Secret
metadata:
  name: osp-secret
  namespace: openstack
type: Opaque
data:
  # Core Infrastructure Passwords
  AdminPassword: <base64-encoded-admin-password>
  DatabasePassword: <base64-encoded-database-password>
  RabbitPassword: <base64-encoded-rabbit-password>
  ServicePassword: <base64-encoded-service-password>
  
  # Identity and Authentication
  KeystonePassword: <base64-encoded-keystone-password>
  
  # Compute Services
  NovaPassword: <base64-encoded-nova-password>
  PlacementPassword: <base64-encoded-placement-password>
  
  # Networking
  NeutronPassword: <base64-encoded-neutron-password>
  
  # Storage and Image Services
  CinderPassword: <base64-encoded-cinder-password>
  GlancePassword: <base64-encoded-glance-password>
  SwiftPassword: <base64-encoded-swift-password>
  
  # Orchestration and Telemetry
  HeatPassword: <base64-encoded-heat-password>
  CeilometerPassword: <base64-encoded-ceilometer-password>
  
  # Additional Service Passwords
  ManiliaPassword: <base64-encoded-manilia-password>
  BarbicanPassword: <base64-encoded-barbican-password>
  OctaviaPassword: <base64-encoded-octavia-password>
