# Ignore large ISO files
*.iso

# Ignore binary files
/bin/
/kubectl
/oc
/openshift-install

# Ignore compressed archives
*.tar.gz
*.zip
*.gz

# Ignore sensitive information
pull-secret.txt
**/pull-secret.txt

# Ignore temporary files
*.tmp
*.temp
.DS_Store

# Ignore installation temporary files
/auth/
.openshift_install*

# Ignore MacOS specific files
.DS_Store
._*

# Keep symbolic links to ISOs in the iso directory
!iso/

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 