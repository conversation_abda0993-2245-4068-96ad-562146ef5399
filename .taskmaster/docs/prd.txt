# Product Requirements Document: Red Hat OpenStack Services on OpenShift 18.0 Deployment

## Project Overview
Deploy Red Hat OpenStack Services (RHOSO) on OpenShift 18.0 using a Single Node OpenShift (SNO) cluster running on Proxmox virtualization platform. This deployment will provide a complete OpenStack control plane on OpenShift with compute nodes for virtualized workloads.

## Technical Scope
- Single Node OpenShift 4.18 installation on Proxmox VM
- Red Hat OpenStack Services 18.0 deployment
- Complete OpenStack control plane (Nova, Neutron, Cinder, Glance, Keystone, etc.)
- Data plane compute nodes with RHEL 9
- Networking configuration with MetalLB and Multus
- Storage configuration with Local Storage Operator
- Security and subscription management

## Infrastructure Requirements
- Proxmox host (pve01) with adequate resources
- VM with minimum 8 vCPUs, 16GB RAM (32GB recommended), 120GB disk
- UEFI firmware with nested virtualization enabled
- Network interfaces bridged to lab network
- RHEL 9 compute nodes with cloud-admin user

## Core Components to Deploy
1. Proxmox VM preparation and configuration
2. Single Node OpenShift installation using Assisted Installer
3. Red Hat subscription and registry configuration
4. OpenStack Operator installation and configuration
5. OpenShift platform preparation (networking, storage, security)
6. OpenStack Control Plane deployment
7. Data Plane compute node deployment and configuration
8. System verification and testing

## Success Criteria
- SNO cluster operational and accessible
- OpenStack control plane services running
- Compute nodes registered and functional
- Ability to create and manage OpenStack resources
- All components properly configured with subscriptions
- Networking and storage working correctly

## Technical Specifications
- OpenShift 4.18 (Single Node deployment)
- Red Hat OpenStack Services 18.0
- RHEL 9.4 for compute nodes
- MetalLB for load balancing
- Local Storage Operator for persistent storage
- Multus CNI for multiple network interfaces
- Subscription Manager for RHEL registration

## Deliverables
- Functional OpenStack cloud platform
- Complete documentation of deployment process
- Verification of all services and components
- Troubleshooting procedures and validation steps