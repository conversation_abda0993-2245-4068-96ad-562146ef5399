# Task ID: 10
# Title: Prepare RHEL Compute Node Infrastructure
# Status: pending
# Dependencies: None
# Priority: high
# Description: Set up the RHEL 9 compute node(s) that will serve as OpenStack data plane nodes.
# Details:
Deploy RHEL 9 VM for compute node with adequate resources. Create cloud-admin user with passwordless sudo access. Configure SSH key authentication for Ansible access. Generate and distribute SSH keys (ansible-ssh-key, nova-migration-ssh-key). Prepare node networking and basic system configuration for OpenStack data plane deployment.

# Test Strategy:


# Subtasks:
## 10.1. Create RHEL 9 Compute Node VM [pending]
### Dependencies: None
### Description: Deploy RHEL 9 virtual machine for OpenStack compute services
### Details:
Create new VM in Proxmox with minimum 4 vCPUs, 8GB RAM, 100GB disk. Configure nested virtualization support. Install RHEL 9 from ISO. Set hostname (e.g., compute01.lab.local). Configure static IP address for management network.

## 10.2. Configure System User and Access [pending]
### Dependencies: None
### Description: Set up cloud-admin user with proper privileges
### Details:
Create cloud-admin user with 'useradd -m -s /bin/bash cloud-admin'. Configure passwordless sudo with 'echo "cloud-admin ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/cloud-admin'. Set up SSH directory and permissions.

## 10.3. Generate and Configure SSH Keys [pending]
### Dependencies: None
### Description: Create SSH keys for Ansible and Nova migration access
### Details:
Generate ansible-ssh-key with 'ssh-keygen -t rsa -b 4096 -f ansible-ssh-key -N ""'. Generate nova-migration-ssh-key for live migration. Copy public keys to cloud-admin authorized_keys. Verify SSH key authentication works.

## 10.4. Configure Network Interfaces [pending]
### Dependencies: None
### Description: Set up multiple network interfaces for OpenStack networking
### Details:
Configure management network interface with static IP. Prepare additional interfaces for OpenStack networks (ctlplane, storage, tenant). Configure network bonding if required. Verify network connectivity to OpenShift cluster.

## 10.5. Basic System Configuration and Validation [pending]
### Dependencies: None
### Description: Complete basic system setup and verify readiness
### Details:
Configure DNS resolution for lab environment. Set up NTP synchronization. Configure firewall rules for OpenStack services. Verify system meets OpenStack compute node requirements. Test connectivity to OpenShift cluster and registry.

