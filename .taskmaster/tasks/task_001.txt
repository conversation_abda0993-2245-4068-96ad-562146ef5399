# Task ID: 1
# Title: Prepare Proxmox VM Infrastructure
# Status: pending
# Dependencies: None
# Priority: high
# Description: Set up the Proxmox virtual machine that will host the Single Node OpenShift cluster with proper specifications and configuration.
# Details:
Create VM on Proxmox host (pve01) using SSH commands with minimum 8 vCPUs, 16GB RAM (32GB recommended), 120GB disk. Use 'ssh root@pve01' to execute Proxmox CLI commands including 'qm create', 'qm set' for VM configuration. Ensure storage is SSD-backed for performance. Network interface must support VLAN trunking if required. Document VM resource allocation and host mapping for troubleshooting. Configure VM with UEFI firmware and enable nested virtualization (VT-x/AMD-V) via CLI. Set CPU type to 'host' or add VMX flag remotely. Attach network interface bridged to lab network using command line tools. Configure storage layout for container storage requirements.

# Test Strategy:


# Subtasks:
## 1.1. Create Base VM in Proxmox [pending]
### Dependencies: None
### Description: Create a new virtual machine with minimum specifications
### Details:
Use SSH to connect to Proxmox host and create new VM with CLI commands: 'ssh root@pve01 "qm create 200 --name sno-openshift --memory 16384 --cores 8 --net0 virtio,bridge=vmbr0 --ide2 local:cloudinit --boot c --bootdisk scsi0 --scsi0 local-lvm:120"'. Set VM ID (e.g., 200) and name appropriately. Verify VM creation with 'ssh root@pve01 "qm config 200"'.

## 1.2. Configure UEFI and Nested Virtualization [pending]
### Dependencies: None
### Description: Enable UEFI boot and nested virtualization support
### Details:
Configure VM firmware and CPU settings via SSH: 'ssh root@pve01 "qm set 200 --bios ovmf --efidisk0 local-lvm:1,format=raw"' for UEFI firmware. Enable nested virtualization with 'ssh root@pve01 "qm set 200 --cpu host --args '-cpu host,+vmx'"'. Verify settings with 'ssh root@pve01 "qm config 200 | grep -E '(bios|cpu|args)'"'.

## 1.3. Configure VM Network Interface [pending]
### Dependencies: None
### Description: Set up network connectivity for the VM
### Details:
Configure network interface via SSH: 'ssh root@pve01 "qm set 200 --net0 virtio,bridge=vmbr0,firewall=1"' to attach VM to lab network bridge. If VLAN is required, add vlan tag: 'ssh root@pve01 "qm set 200 --net0 virtio,bridge=vmbr0,tag=100"'. Verify network configuration with 'ssh root@pve01 "qm config 200 | grep net0"'. Test network connectivity after VM boot.

## 1.4. Optimize Storage Configuration [pending]
### Dependencies: None
### Description: Configure storage layout for optimal OpenShift performance
### Details:
Optimize storage configuration via SSH: 'ssh root@pve01 "qm set 200 --scsi0 local-lvm:120,cache=writeback,discard=on,ssd=1"' for SSD optimization. Configure additional storage if needed with 'ssh root@pve01 "qm set 200 --scsi1 local-lvm:50"' for /var partition. Verify storage settings with 'ssh root@pve01 "qm config 200 | grep scsi"'. Check available storage pools with 'ssh root@pve01 "pvesm status"'.

## 1.5. Verify VM Configuration [pending]
### Dependencies: None
### Description: Validate all VM settings before OpenShift installation
### Details:
Start VM and verify configuration via SSH: 'ssh root@pve01 "qm start 200"' to boot the VM. Check VM status with 'ssh root@pve01 "qm status 200"'. Verify UEFI boot by checking VM console: 'ssh root@pve01 "qm monitor 200"'. Test nested virtualization capability after OS installation. Verify network connectivity and storage accessibility. Document final VM configuration with 'ssh root@pve01 "qm config 200"' output.

