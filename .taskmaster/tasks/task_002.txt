# Task ID: 2
# Title: Install Single Node OpenShift 4.18
# Status: done
# Dependencies: None
# Priority: high
# Description: Deploy OpenShift 4.18 on the prepared VM using the Assisted Installer method.
# Details:
Obtain pull secret from Red Hat OpenShift Cluster Manager. Launch Assisted Installer in Cluster Manager. Create new cluster with Single Node OpenShift (SNO) option. Download discovery ISO and boot Proxmox VM from it. Complete installation via web UI with networking details. Monitor installation progress until completion. Verify cluster access with oc login and oc get nodes.

# Test Strategy:


# Subtasks:
## 2.1. Configure Pull Secret and Cluster Manager Access [done]
### Dependencies: None
### Description: Set up authentication and access to Red Hat OpenShift Cluster Manager
### Details:
Access console.redhat.com/openshift and obtain pull secret. Save pull secret to /Users/<USER>/openshift-virt/config/pull_secret.txt. Verify Red Hat account has OpenShift entitlements.

## 2.2. Create SNO Cluster in Assisted Installer [done]
### Dependencies: None
### Description: Initialize new Single Node OpenShift cluster configuration
### Details:
Navigate to Assisted Installer in OpenShift Cluster Manager. Create new cluster with Single Node OpenShift option. Configure cluster name (e.g., 'sno-rhoso'), OpenShift version 4.18, and base domain.

## 2.3. Configure Cluster Networking and Host Discovery [done]
### Dependencies: None
### Description: Set up network configuration and prepare for host discovery
### Details:
Configure cluster network CIDR (**********/14), service network CIDR (**********/16), and machine network CIDR (*************/24). Set API VIP and Ingress VIP addresses. Upload SSH public key for host access.

## 2.4. Download and Configure Discovery ISO [done]
### Dependencies: None
### Description: Download the discovery ISO and prepare VM boot configuration
### Details:
Download the discovery ISO from Assisted Installer. Upload ISO to Proxmox storage or copy to /iso/ directory. Configure Proxmox VM to boot from the discovery ISO.

## 2.5. Boot VM and Complete Host Discovery [done]
### Dependencies: None
### Description: Start VM from discovery ISO and register with Assisted Installer
### Details:
Start the Proxmox VM from discovery ISO. Monitor VM console for network connectivity. Verify host appears in Assisted Installer web interface. Validate hardware requirements are met.

## 2.6. Execute OpenShift Installation [done]
### Dependencies: None
### Description: Start and monitor the OpenShift installation process
### Details:
Review installation settings in Assisted Installer. Start the installation process. Monitor progress through web interface and VM console. Wait for installation completion (typically 30-45 minutes).

## 2.7. Verify Cluster Access and Basic Functionality [done]
### Dependencies: None
### Description: Validate successful OpenShift deployment and configure access
### Details:
Download kubeconfig from Assisted Installer. Configure oc CLI with kubeconfig. Run 'oc get nodes' to verify cluster is ready. Check cluster operators with 'oc get co'. Verify cluster version with 'oc get clusterversion'.

## 2.8. Rollback Procedure for SNO Installation [pending]
### Dependencies: None
### Description: Document and execute rollback steps if SNO installation fails
### Details:
If installation fails, delete failed cluster from Assisted Installer, wipe VM disks, re-upload ISO, and restart installation. Document failure reason and corrective actions taken.

