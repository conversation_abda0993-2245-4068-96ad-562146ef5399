# Task ID: 3
# Title: Configure Red Hat Subscriptions and Registry Access
# Status: pending
# Dependencies: None
# Priority: high
# Description: Set up proper Red Hat subscriptions and registry access for OpenStack components.
# Details:
Create subscription-manager secret in openstack namespace with base64 encoded credentials. Create redhat-registry secret for container registry access. Configure registry authentication for RHOSO containers. Verify registry access for OpenStack container images.

# Test Strategy:


# Subtasks:
## 3.1. Create Subscription Manager Secret [pending]
### Dependencies: None
### Description: Configure Red Hat subscription credentials for OpenStack namespace
### Details:
Base64 encode Red Hat subscription credentials. Create subscription-manager secret with username, password, and org ID in openstack namespace using 'oc create secret generic subscription-manager --from-literal=rhsm-username=<username> --from-literal=rhsm-password=<password> --from-literal=rhsm-org=<org-id> -n openstack'.

## 3.2. Configure Red Hat Registry Access Secret [pending]
### Dependencies: None
### Description: Set up container registry authentication for OpenStack images
### Details:
Create redhat-registry secret using pull secret credentials. Use 'oc create secret generic redhat-registry --from-file=.dockerconfigjson=<pull-secret-file> --type=kubernetes.io/dockerconfigjson -n openstack'. Verify secret contains registry.redhat.io authentication.

## 3.3. Configure RHOSO Container Registry Access [pending]
### Dependencies: None
### Description: Set up specific RHOSO container registry authentication
### Details:
Create registry-redhat-io secret for RHOSO specific containers. Configure authentication for registry.redhat.io/rhosp-rhel9 and registry.redhat.io/ubi8 repositories. Verify registry access with test pull commands.

## 3.4. Verify Registry Authentication and Access [pending]
### Dependencies: None
### Description: Test registry access and validate authentication configuration
### Details:
Test registry authentication by pulling a sample OpenStack container image. Verify all secrets are properly created in openstack namespace. Check secret data encoding and format. Document registry access configuration.

