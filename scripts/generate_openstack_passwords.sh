#!/bin/bash

# OpenStack Password Generator Script
# This script generates secure random passwords for all OpenStack services

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$(dirname "$SCRIPT_DIR")/config"

echo "🔐 Generating OpenStack Service Passwords..."

# Function to generate a secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to base64 encode a password
base64_encode() {
    echo -n "$1" | base64
}

# Generate all passwords
ADMIN_PASSWORD=$(generate_password)
DATABASE_PASSWORD=$(generate_password)
RABBIT_PASSWORD=$(generate_password)
SERVICE_PASSWORD=$(generate_password)
KEYSTONE_PASSWORD=$(generate_password)
NOVA_PASSWORD=$(generate_password)
PLACEMENT_PASSWORD=$(generate_password)
NEUTRON_PASSWORD=$(generate_password)
CINDER_PASSWORD=$(generate_password)
GLANCE_PASSWORD=$(generate_password)
SWIFT_PASSWORD=$(generate_password)
HEAT_PASSWORD=$(generate_password)
CEILOMETER_PASSWORD=$(generate_password)
MANILIA_PASSWORD=$(generate_password)
BARBICAN_PASSWORD=$(generate_password)
OCTAVIA_PASSWORD=$(generate_password)

# Create the OpenStack secrets YAML file
cat > "$CONFIG_DIR/osp-secret.yaml" << EOF
apiVersion: v1
kind: Secret
metadata:
  name: osp-secret
  namespace: openstack
type: Opaque
data:
  # Core Infrastructure Passwords
  AdminPassword: $(base64_encode "$ADMIN_PASSWORD")
  DatabasePassword: $(base64_encode "$DATABASE_PASSWORD")
  RabbitPassword: $(base64_encode "$RABBIT_PASSWORD")
  ServicePassword: $(base64_encode "$SERVICE_PASSWORD")
  
  # Identity and Authentication
  KeystonePassword: $(base64_encode "$KEYSTONE_PASSWORD")
  
  # Compute Services
  NovaPassword: $(base64_encode "$NOVA_PASSWORD")
  PlacementPassword: $(base64_encode "$PLACEMENT_PASSWORD")
  
  # Networking
  NeutronPassword: $(base64_encode "$NEUTRON_PASSWORD")
  
  # Storage and Image Services
  CinderPassword: $(base64_encode "$CINDER_PASSWORD")
  GlancePassword: $(base64_encode "$GLANCE_PASSWORD")
  SwiftPassword: $(base64_encode "$SWIFT_PASSWORD")
  
  # Orchestration and Telemetry
  HeatPassword: $(base64_encode "$HEAT_PASSWORD")
  CeilometerPassword: $(base64_encode "$CEILOMETER_PASSWORD")
  
  # Additional Service Passwords
  ManiliaPassword: $(base64_encode "$MANILIA_PASSWORD")
  BarbicanPassword: $(base64_encode "$BARBICAN_PASSWORD")
  OctaviaPassword: $(base64_encode "$OCTAVIA_PASSWORD")
EOF

# Create a plaintext passwords file for reference
cat > "$CONFIG_DIR/passwords.txt" << EOF
# OpenStack Service Passwords (KEEP SECURE!)
# Generated: $(date)

AdminPassword=$ADMIN_PASSWORD
DatabasePassword=$DATABASE_PASSWORD
RabbitPassword=$RABBIT_PASSWORD
ServicePassword=$SERVICE_PASSWORD
KeystonePassword=$KEYSTONE_PASSWORD
NovaPassword=$NOVA_PASSWORD
PlacementPassword=$PLACEMENT_PASSWORD
NeutronPassword=$NEUTRON_PASSWORD
CinderPassword=$CINDER_PASSWORD
GlancePassword=$GLANCE_PASSWORD
SwiftPassword=$SWIFT_PASSWORD
HeatPassword=$HEAT_PASSWORD
CeilometerPassword=$CEILOMETER_PASSWORD
ManiliaPassword=$MANILIA_PASSWORD
BarbicanPassword=$BARBICAN_PASSWORD
OctaviaPassword=$OCTAVIA_PASSWORD
EOF

echo "✅ Generated OpenStack service passwords:"
echo "   - osp-secret.yaml (Kubernetes secret)"
echo "   - passwords.txt (plaintext reference - keep secure!)"
echo ""
echo "🔑 Admin password: $ADMIN_PASSWORD"
echo ""
echo "Next steps:"
echo "1. Review the generated secrets"
echo "2. Apply the secret to OpenShift: oc apply -f $CONFIG_DIR/osp-secret.yaml"
echo "3. Secure the passwords.txt file: chmod 600 $CONFIG_DIR/passwords.txt"
