#!/bin/bash
# Simple notification script for OpenStack Horizon access restoration
# This runs automatically on login to remind you to restore access

SCRIPT_PATH="/Users/<USER>/openshift-virt/scripts/restore_horizon_access.sh"
LOG_FILE="/Users/<USER>/openshift-virt/logs/horizon_notification.log"

echo "$(date): Horizon access notification triggered" >> "$LOG_FILE"

# Check if port forwarding is already active
if lsof -i :9443 >/dev/null 2>&1; then
    echo "$(date): Port forwarding already active" >> "$LOG_FILE"
    exit 0
fi

# Display notification to user
osascript -e 'display notification "Run: restore_horizon_access.sh to access OpenStack Dashboard" with title "OpenStack Horizon" subtitle "Port forwarding needs to be restored"'

echo "$(date): Notification displayed to user" >> "$LOG_FILE"
