#!/bin/bash

# Fresh OpenShift + OpenStack Deployment Script
# Complete automation based on proven RHOSO deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Project directory
PROJECT_DIR="/Volumes/T7/projects/openshift-virt"
cd "$PROJECT_DIR"

echo -e "${GREEN}🚀 Fresh OpenShift + OpenStack Deployment${NC}"
echo "=============================================="
echo ""

# Check prerequisites
log "Checking prerequisites..."

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    error "This script is designed for macOS. Current OS: $OSTYPE"
fi

# Check required files
required_files=(
    "config/install_config.yaml"
    "config/pull_secret.txt"
    "bin/openshift-install"
    "bin/oc"
)

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        error "Required file missing: $file"
    fi
done

success "All prerequisite files found"

echo ""
echo -e "${YELLOW}📋 DEPLOYMENT SPECIFICATIONS:${NC}"
echo ""
echo "🖥️  Proxmox VM Requirements:"
echo "   • 8+ vCPUs (16 recommended)"
echo "   • 32 GB RAM (required for OpenStack)"
echo "   • 200 GB disk (required for /var partition)"
echo "   • UEFI firmware with nested virtualization"
echo "   • Bridge network interface"
echo ""
echo "🎯 Target IP Range: 10.0.0.x/24"
echo "📦 OpenShift Version: 4.18"
echo "☁️  OpenStack Version: 18.0 (RHOSO)"
echo ""

read -p "Have you configured the Proxmox VM with the above specifications? (y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo ""
    echo -e "${YELLOW}📖 Proxmox VM Setup Instructions:${NC}"
    echo ""
    echo "1. Create new VM in Proxmox:"
    echo "   • Name: openshift-rhoso"
    echo "   • OS Type: Linux"
    echo "   • System: UEFI, Enable Qemu Agent"
    echo ""
    echo "2. Hardware Configuration:"
    echo "   • Processors: 8 cores (16 recommended)"
    echo "   • Memory: 32768 MB (32 GB)"
    echo "   • Hard Disk: 200 GB (SCSI)"
    echo "   • Network: Bridge interface"
    echo ""
    echo "3. Advanced Settings:"
    echo "   • CPU: Enable nested virtualization"
    echo "   • Options: Start at boot = Yes"
    echo ""
    echo "4. Don't start the VM yet - we'll boot from ISO"
    echo ""
    exit 0
fi

echo ""
log "Starting deployment process..."

# Phase 1: Prepare Installation Files
echo ""
echo -e "${BLUE}🔧 Phase 1: Preparing Installation Files${NC}"
echo "========================================="

# Clean previous installation attempts
if [[ -d "ocp" ]]; then
    warning "Removing previous installation directory"
    rm -rf ocp
fi

# Create fresh installation directory
mkdir -p ocp

# Copy install config
log "Copying installation configuration..."
cp config/install_config.yaml ocp/
success "Install config copied"

# Backup the original config (installer consumes it)
cp config/install_config.yaml ocp/install-config-backup.yaml

# Generate ignition files
log "Generating ignition configuration..."
cd ocp
../bin/openshift-install create single-node-ignition-config --dir=.
success "Ignition configuration generated"

# Check generated files
if [[ ! -f "bootstrap-in-place-for-live-iso.ign" ]]; then
    error "Failed to generate ignition file"
fi

# Download RHCOS ISO (if not already available)
log "Checking for RHCOS ISO..."
RHCOS_ISO_URL="https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/4.18/4.18.0/rhcos-4.18.0-x86_64-live.x86_64.iso"
ISO_DIR="../iso/current"
mkdir -p "$ISO_DIR"

if [[ ! -f "$ISO_DIR/rhcos-live.iso" ]]; then
    log "Downloading RHCOS ISO..."
    curl -L "$RHCOS_ISO_URL" -o "$ISO_DIR/rhcos-live.iso"
    success "RHCOS ISO downloaded"
else
    success "RHCOS ISO already available"
fi

# Create bootable ISO with ignition
log "Creating bootable ISO with ignition data..."
ISO_OUTPUT="../iso/current/rhcos-openshift-sno.iso"

# Use genisoimage/mkisofs to embed ignition
if command -v genisoimage >/dev/null 2>&1; then
    MKISO="genisoimage"
elif command -v mkisofs >/dev/null 2>&1; then
    MKISO="mkisofs"
else
    error "Neither genisoimage nor mkisofs found. Install with: brew install cdrtools"
fi

# Create temporary directory for ISO modification
TMP_ISO_DIR=$(mktemp -d)
log "Mounting and modifying ISO..."

# Mount the original ISO (macOS method)
hdiutil mount "$ISO_DIR/rhcos-live.iso" -mountpoint "$TMP_ISO_DIR" -readonly

# Copy ISO contents to writable location
ISO_WORK_DIR=$(mktemp -d)
cp -R "$TMP_ISO_DIR/"* "$ISO_WORK_DIR/"
chmod -R u+w "$ISO_WORK_DIR"

# Unmount original ISO
hdiutil unmount "$TMP_ISO_DIR"

# Copy ignition file to ISO
cp bootstrap-in-place-for-live-iso.ign "$ISO_WORK_DIR/"

# Create new ISO with ignition data
$MKISO -o "$ISO_OUTPUT" \
    -b isolinux/isolinux.bin \
    -c isolinux/boot.cat \
    -no-emul-boot \
    -boot-load-size 4 \
    -boot-info-table \
    -R -J -V "RHCOS-SNO" \
    "$ISO_WORK_DIR"

# Cleanup
rm -rf "$TMP_ISO_DIR" "$ISO_WORK_DIR"

success "Bootable ISO created: $ISO_OUTPUT"

cd ..

echo ""
echo -e "${GREEN}✅ Phase 1 Complete: Installation files prepared${NC}"
echo ""
echo -e "${YELLOW}📀 Next Steps:${NC}"
echo ""
echo "1. Upload ISO to Proxmox:"
echo "   • File: $PROJECT_DIR/iso/current/rhcos-openshift-sno.iso"
echo "   • Upload to Proxmox ISO storage"
echo ""
echo "2. Boot VM from ISO:"
echo "   • Attach ISO to VM CD/DVD drive"
echo "   • Start the VM"
echo "   • Wait for automatic installation (30-45 minutes)"
echo ""
echo "3. Monitor installation:"
echo "   • VM console will show installation progress"
echo "   • System will reboot several times"
echo "   • Final reboot indicates completion"
echo ""
echo "4. Run Phase 2 when installation completes:"
echo "   ./scripts/deploy_phase2_verify.sh"
echo ""

# Create Phase 2 script
cat > scripts/deploy_phase2_verify.sh << 'EOF'
#!/bin/bash

# Phase 2: Verify OpenShift Installation and Prepare for OpenStack

PROJECT_DIR="/Volumes/T7/projects/openshift-virt"
cd "$PROJECT_DIR"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 Phase 2: Verifying OpenShift Installation${NC}"
echo "=============================================="

# Set kubeconfig
export KUBECONFIG="$PROJECT_DIR/ocp/auth/kubeconfig"

# Wait for API server
echo "Waiting for API server to be ready..."
timeout 300 bash -c 'until ./bin/oc get nodes >/dev/null 2>&1; do sleep 10; done'

# Check cluster status
echo -e "${GREEN}✅ Checking cluster status...${NC}"
./bin/oc get nodes
./bin/oc get clusteroperators

echo ""
echo -e "${GREEN}✅ Phase 2 Complete: Cluster verified${NC}"
echo ""
echo "Next: Run Phase 3 for OpenStack deployment"
echo "   ./scripts/deploy_phase3_openstack.sh"

EOF

chmod +x scripts/deploy_phase2_verify.sh

success "Deployment Phase 1 completed successfully!"
success "Phase 2 script created for post-installation verification"

echo ""
echo -e "${BLUE}🎯 Summary:${NC}"
echo "• Ignition configuration generated"
echo "• Bootable ISO created with embedded config"
echo "• Ready for VM deployment"
echo ""
echo -e "${YELLOW}⏭️  Next: Boot your Proxmox VM from the generated ISO${NC}"
