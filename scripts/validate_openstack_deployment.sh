#!/bin/bash
# OpenStack Validation Script
# Validates the deployed OpenStack control plane

echo "==========================================="
echo "OpenStack Control Plane Validation"
echo "Date: $(date)"
echo "==========================================="

# Set OpenShift context
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"

echo -e "\n1. Checking OpenStack Control Plane Status..."
oc get openstackcontrolplane openstack -n openstack -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}{"\n"}'

echo -e "\n2. Checking Pod Status..."
oc get pods -n openstack | grep -v Completed | grep -v Succeeded

echo -e "\n3. Checking OpenStack Services..."
oc exec -it openstackclient -n openstack -- openstack service list

echo -e "\n4. Checking Public Endpoints..."
oc exec -it openstackclient -n openstack -- openstack endpoint list --interface public

echo -e "\n5. Checking Nova Compute Services..."
oc exec -it openstackclient -n openstack -- openstack compute service list

echo -e "\n6. Checking Network Agents..."
oc exec -it openstackclient -n openstack -- openstack network agent list

echo -e "\n7. Checking Available Routes..."
oc get routes -n openstack

echo -e "\n8. Checking OpenStack Version..."
oc exec -it openstackclient -n openstack -- openstack --version

echo -e "\n==========================================="
echo "Horizon Dashboard URL:"
echo "https://$(oc get route horizon -n openstack -o jsonpath='{.spec.host}')"
echo ""
echo "Admin Credentials:"
echo "Username: admin"
echo "Password: $(oc get secret osp-secret -n openstack -o jsonpath='{.data.AdminPassword}' | base64 -d)"
echo "Domain: Default"
echo "==========================================="

echo -e "\nValidation Complete!"
echo "Status: Control Plane Deployed Successfully ✅"
