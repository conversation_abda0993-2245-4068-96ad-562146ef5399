#!/bin/bash

# Red Hat OpenShift + OpenStack Deployment Options
# Multiple approaches to suit different preferences

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

clear

echo -e "${GREEN}🎯 OpenShift + OpenStack Deployment Options${NC}"
echo "============================================="
echo ""
echo -e "${BLUE}Choose your preferred deployment approach:${NC}"
echo ""

echo -e "${PURPLE}Option 1: 🌐 Red Hat Assisted Installer (RECOMMENDED)${NC}"
echo "----------------------------------------------------"
echo "✅ Modern web-based deployment using console.redhat.com"
echo "✅ Step-by-step guided process"
echo "✅ Automatic hardware validation"
echo "✅ Progress monitoring dashboard"
echo "✅ Built-in troubleshooting"
echo ""
echo "📋 Run: ./scripts/deploy_assisted_installer_guide.sh"
echo ""

echo -e "${PURPLE}Option 2: 🔧 Semi-Automated Local Deployment${NC}"
echo "--------------------------------------------"
echo "✅ Uses local OpenShift installer"
echo "✅ Automated script execution"
echo "✅ Manual intervention points"
echo "✅ Complete control over process"
echo ""
echo "📋 Run: ./scripts/deploy_phase1_prepare.sh"
echo ""

echo -e "${PURPLE}Option 3: 📋 Manual Step-by-Step Process${NC}"
echo "-------------------------------------------"
echo "✅ Individual phase scripts"
echo "✅ Checkpoint validation"
echo "✅ Educational approach"
echo "✅ Troubleshooting flexibility"
echo ""
echo "📋 Run each phase individually:"
echo "   • Phase 1: ./scripts/deploy_phase1_prepare.sh"
echo "   • Phase 2: ./scripts/deploy_phase2_verify.sh"
echo "   • Phase 3: ./scripts/deploy_phase3_openstack.sh"
echo "   • Phase 4: ./scripts/deploy_phase4_dashboard.sh"
echo ""

echo -e "${YELLOW}💡 Recommendations:${NC}"
echo ""
echo "🌟 First-time deployment → Use Option 1 (Assisted Installer)"
echo "🔧 Experienced users → Use Option 2 (Semi-Automated)"
echo "📚 Learning/Troubleshooting → Use Option 3 (Manual)"
echo ""

echo -e "${BLUE}📋 All approaches result in:${NC}"
echo "• Single Node OpenShift 4.18 cluster"
echo "• Red Hat OpenStack Services 18.0 (RHOSO)"
echo "• Horizon web dashboard"
echo "• Automated post-reboot restoration"
echo ""

echo -e "${GREEN}🚀 Quick Start:${NC}"
echo "Run: ./scripts/deploy_assisted_installer_guide.sh"
echo ""

read -p "Press Enter to see full deployment plan..." 
./scripts/new_cluster_deployment_plan.sh
