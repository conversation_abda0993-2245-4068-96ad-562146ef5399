#!/bin/bash
# OpenStack Horizon LaunchAgent Installation Script

PLIST_FILE="com.user.openstack.horizon.plist"
SOURCE_PATH="/Users/<USER>/openshift-virt/config/$PLIST_FILE"
DEST_PATH="$HOME/Library/LaunchAgents/$PLIST_FILE"

echo "=========================================================="
echo "🚀 INSTALLING OPENSTACK HORIZON LAUNCHAGENT"
echo "Date: $(date)"
echo "=========================================================="

# Check if source file exists
if [ ! -f "$SOURCE_PATH" ]; then
    echo "❌ Source file not found: $SOURCE_PATH"
    exit 1
fi

echo "📋 INSTALLATION STEPS:"
echo "======================"

# Step 1: Copy plist file
echo "1️⃣ Copying LaunchAgent plist file..."
cp "$SOURCE_PATH" "$DEST_PATH"
if [ $? -eq 0 ]; then
    echo "✅ File copied successfully"
else
    echo "❌ Failed to copy file"
    exit 1
fi

# Step 2: Set proper permissions
echo "2️⃣ Setting permissions..."
chmod 644 "$DEST_PATH"
echo "✅ Permissions set"

# Step 3: Unload existing agent (if any)
echo "3️⃣ Unloading any existing agent..."
launchctl unload "$DEST_PATH" 2>/dev/null
echo "✅ Existing agent unloaded (if any)"

# Step 4: Load the new agent
echo "4️⃣ Loading new LaunchAgent..."
launchctl load "$DEST_PATH"
if [ $? -eq 0 ]; then
    echo "✅ LaunchAgent loaded successfully"
else
    echo "❌ Failed to load LaunchAgent"
    exit 1
fi

# Step 5: Verify installation
echo "5️⃣ Verifying installation..."
sleep 2
if launchctl list | grep -q "com.user.openstack.horizon"; then
    echo "✅ LaunchAgent is running"
    AGENT_INFO=$(launchctl list | grep "com.user.openstack.horizon")
    echo "   Status: $AGENT_INFO"
else
    echo "❌ LaunchAgent is not running"
    exit 1
fi

echo -e "\n📋 WHAT THIS LAUNCHAGENT DOES:"
echo "==============================="
echo "• Runs automatically when you login"
echo "• Checks every 5 minutes if port forwarding is active"
echo "• Shows a notification if Horizon access needs restoration"
echo "• Does NOT automatically start port forwarding (security reasons)"
echo "• Logs activity to: /Users/<USER>/openshift-virt/logs/"

echo -e "\n🔧 MANUAL COMMANDS:"
echo "=================="
echo "View status:     launchctl list | grep openstack"
echo "Unload agent:    launchctl unload $DEST_PATH"
echo "Load agent:      launchctl load $DEST_PATH"
echo "View logs:       tail -f /Users/<USER>/openshift-virt/logs/horizon_notification.log"

echo -e "\n🎯 AFTER REBOOT:"
echo "================"
echo "1. You'll see a notification about restoring Horizon access"
echo "2. Run: cd /Users/<USER>/openshift-virt && ./scripts/restore_horizon_access.sh"
echo "3. Or run the alias: restore-horizon (if you set it up)"

echo -e "\n✅ INSTALLATION COMPLETE!"
echo "The LaunchAgent will now monitor and notify you about Horizon access."
echo "=========================================================="
