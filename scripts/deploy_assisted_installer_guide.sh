#!/bin/bash

# Red Hat Assisted Installer Deployment Guide
# Complete workflow using console.redhat.com

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

clear

echo -e "${GREEN}🌐 Red Hat Assisted Installer Deployment Guide${NC}"
echo "==============================================="
echo ""
echo -e "${BLUE}Using Red Hat Hybrid Cloud Console for OpenShift deployment${NC}"
echo "This is the modern, recommended approach for Single Node OpenShift"
echo ""

echo -e "${PURPLE}📋 Prerequisites Checklist:${NC}"
echo ""
echo "✅ Red Hat Customer Portal account"
echo "✅ Valid OpenShift subscription"
echo "✅ Proxmox VM configured:"
echo "   • 8+ vCPUs (16 recommended)"
echo "   • 32 GB RAM (required for OpenStack)"
echo "   • 200 GB disk (required for /var partition)"
echo "   • UEFI firmware with nested virtualization"
echo "   • Bridge network interface"
echo ""

read -p "Are all prerequisites ready? (y/N): " prereq_ready
if [[ ! "$prereq_ready" =~ ^[Yy]$ ]]; then
    echo ""
    echo -e "${YELLOW}Please complete prerequisites first, then run this script again.${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🚀 Step-by-Step Deployment Process${NC}"
echo "==================================="

echo ""
echo -e "${GREEN}Step 1: Access Red Hat Hybrid Cloud Console${NC}"
echo "-------------------------------------------"
echo ""
echo "Opening Red Hat Console in your browser..."
sleep 2
open "https://console.redhat.com/openshift/create/datacenter"
echo ""
echo "✅ Browser opened to: https://console.redhat.com/openshift/create/datacenter"
echo ""
echo "📋 In the browser:"
echo "1. Login with your Red Hat account"
echo "2. Click 'Create cluster'"
echo "3. Select 'Datacenter' → 'Single Node OpenShift'"
echo ""

read -p "Press Enter when you've completed Step 1..."

echo ""
echo -e "${GREEN}Step 2: Configure Cluster Details${NC}"
echo "--------------------------------"
echo ""
echo "📋 Cluster Configuration:"
echo ""
echo "• Cluster name: sno-rhoso"
echo "• Base domain: lab.local"
echo "• OpenShift version: 4.18.x (latest stable)"
echo "• Network type: OVNKubernetes"
echo "• CPU architecture: x86_64"
echo ""
echo "📋 Pull Secret:"
echo "• Upload pull secret from: $(pwd)/config/pull_secret.txt"
echo "• If missing, download from: https://console.redhat.com/openshift/install/pull-secret"
echo ""

read -p "Press Enter when you've completed Step 2..."

echo ""
echo -e "${GREEN}Step 3: Host Discovery${NC}"
echo "--------------------"
echo ""
echo "📋 In Red Hat Console:"
echo "1. Click 'Add hosts'"
echo "2. Select 'Minimal ISO' (recommended for bare metal/VM)"
echo "3. Configure SSH public key (optional but recommended)"
echo "4. Click 'Generate Discovery ISO'"
echo "5. Download the Discovery ISO"
echo ""
echo "💾 Save the Discovery ISO to a known location"
echo "📁 Suggested: ~/Downloads/discovery_image_sno-rhoso.iso"
echo ""

read -p "Press Enter when Discovery ISO is downloaded..."

echo ""
echo -e "${GREEN}Step 4: Boot VM from Discovery ISO${NC}"
echo "---------------------------------"
echo ""
echo "📋 Proxmox VM Setup:"
echo "1. Upload Discovery ISO to Proxmox storage"
echo "2. Attach ISO to your VM's CD/DVD drive"
echo "3. Ensure VM has correct specs:"
echo "   • 32 GB RAM"
echo "   • 200 GB disk"
echo "   • 8+ CPU cores"
echo "   • Bridge network"
echo "4. Start the VM"
echo "5. Boot from CD/DVD (Discovery ISO)"
echo ""
echo "⏱️  The VM will boot into a discovery environment"
echo "🌐 It will automatically register with Red Hat Console"
echo ""

read -p "Press Enter when VM is booted and running..."

echo ""
echo -e "${GREEN}Step 5: Configure Host in Red Hat Console${NC}"
echo "----------------------------------------"
echo ""
echo "📋 Wait for host to appear in Red Hat Console"
echo "(This may take 2-5 minutes after VM boot)"
echo ""
echo "📋 When host appears, configure:"
echo ""
echo "🌐 Network Configuration:"
echo "• IP Address: *********"
echo "• Subnet Mask: ************* (/24)"
echo "• Gateway: ********"
echo "• DNS Server: ********"
echo ""
echo "🖥️  Host Configuration:"
echo "• Hostname: sno-rhoso.lab.local"
echo "• Role: Control plane node (Single Node)"
echo ""
echo "✅ Validate Requirements:"
echo "• CPU: Should show 8+ cores"
echo "• Memory: Should show 32+ GB"
echo "• Disk: Should show 200+ GB"
echo ""

read -p "Press Enter when host is configured and validated..."

echo ""
echo -e "${GREEN}Step 6: Start Installation${NC}"
echo "----------------------------"
echo ""
echo "📋 Final Installation Steps:"
echo "1. Review cluster configuration"
echo "2. Verify host meets all requirements"
echo "3. Click 'Install cluster'"
echo "4. Monitor installation progress"
echo ""
echo "⏱️  Installation Timeline:"
echo "• Bootstrap: 10-15 minutes"
echo "• Control plane: 20-30 minutes"  
echo "• Finalizing: 10-15 minutes"
echo "• Total: 45-60 minutes"
echo ""
echo "📊 Monitor progress in the Red Hat Console dashboard"
echo ""

read -p "Press Enter when installation is started..."

echo ""
echo -e "${GREEN}Step 7: Download Credentials${NC}"
echo "----------------------------"
echo ""
echo "📋 When installation completes:"
echo "1. Download kubeconfig file"
echo "2. Save admin password"
echo "3. Get cluster console URL"
echo ""
echo "💾 Save files to:"
echo "• Kubeconfig: $(pwd)/ocp/auth/kubeconfig"
echo "• Password: $(pwd)/ocp/auth/kubeadmin-password"
echo ""

# Create directory structure
mkdir -p ocp/auth assisted-installer/downloads

echo "📁 Created directory structure:"
echo "• $(pwd)/ocp/auth/ (for credentials)"
echo "• $(pwd)/assisted-installer/downloads/ (for ISOs)"
echo ""

read -p "Press Enter when credentials are downloaded and saved..."

echo ""
echo -e "${GREEN}✅ Assisted Installer Setup Complete!${NC}"
echo "======================================"
echo ""
echo -e "${BLUE}🎯 Next Steps:${NC}"
echo ""
echo "1. 🔍 Verify cluster access:"
echo "   ./scripts/deploy_phase2_verify.sh"
echo ""
echo "2. 🏗️  Deploy OpenStack (Phase 3):"
echo "   ./scripts/deploy_phase3_openstack.sh"
echo ""
echo "3. 🌐 Configure Horizon Dashboard (Phase 4):"
echo "   ./scripts/deploy_phase4_dashboard.sh"
echo ""
echo -e "${PURPLE}📚 Resources:${NC}"
echo "• Red Hat Console: https://console.redhat.com/openshift"
echo "• Documentation: https://docs.openshift.com/container-platform/4.18/installing/installing_sno/install-sno-installing-sno.html"
echo "• Your cluster console: https://console-openshift-console.apps.sno-rhoso.lab.local"
echo ""
echo -e "${GREEN}🎉 Your OpenShift cluster is ready for OpenStack deployment!${NC}"
