#!/bin/bash

# Phase 3: OpenStack Deployment
# Deploy Red Hat OpenStack Services (RHOSO) on OpenShift

set -e

PROJECT_DIR="/Volumes/T7/projects/openshift-virt"
cd "$PROJECT_DIR"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

echo -e "${BLUE}☁️  Phase 3: OpenStack Deployment${NC}"
echo "=================================="

# Set kubeconfig
export KUBECONFIG="$PROJECT_DIR/ocp/auth/kubeconfig"

# Verify cluster is ready
log "Verifying cluster readiness..."
if ! ./bin/oc get nodes >/dev/null 2>&1; then
    error "OpenShift cluster not accessible. Run Phase 2 first."
fi

# Install required operators
log "Installing prerequisite operators..."

# 1. MetalLB Operator
log "Installing MetalLB operator..."
./bin/oc apply -f config/metallb_operator.yaml
./bin/oc wait --for=condition=Available=true deployment/metallb-operator-controller-manager -n metallb-system --timeout=300s

# 2. LVM Storage Operator  
log "Installing LVM Storage operator..."
./bin/oc apply -f config/lvms_operator.yaml
./bin/oc wait --for=condition=Available=true deployment/lvms-operator -n openshift-storage --timeout=300s

# 3. Configure storage
log "Configuring LVM storage..."
./bin/oc apply -f config/lvmcluster.yaml
./bin/oc wait --for=condition=Ready=true lvmcluster/odf-lvm-vg1 -n openshift-storage --timeout=600s

# 4. Configure MetalLB
log "Configuring MetalLB..."
./bin/oc apply -f config/metallb_config.yaml

# Create OpenStack namespace
log "Creating OpenStack namespace..."
./bin/oc create namespace openstack --dry-run=client -o yaml | ./bin/oc apply -f -

# Install OpenStack operator
log "Installing OpenStack operator..."
./bin/oc apply -f config/openstack_operator.yaml

# Wait for operator to be ready
log "Waiting for OpenStack operator..."
./bin/oc wait --for=condition=Available=true deployment/openstack-operator-controller-manager -n openstack-operators --timeout=600s

# Create service secrets
log "Creating OpenStack service secrets..."
./bin/oc apply -f config/openstack_secrets.yaml -n openstack

# Deploy OpenStack control plane
log "Deploying OpenStack control plane..."
./bin/oc apply -f config/openstack_control_plane.yaml -n openstack

# Monitor deployment
log "Monitoring OpenStack deployment (this may take 20-30 minutes)..."
echo "You can monitor progress with:"
echo "  ./bin/oc get pods -n openstack"
echo "  ./bin/oc get openstackcontrolplane -n openstack"

# Wait for basic pods to start
sleep 60

# Check initial status
./bin/oc get pods -n openstack
./bin/oc get openstackcontrolplane -n openstack

success "OpenStack deployment initiated"

echo ""
echo -e "${YELLOW}⏳ OpenStack is deploying in the background${NC}"
echo "Monitor with: ./bin/oc get pods -n openstack -w"
echo ""
echo "When ready, run Phase 4:"
echo "  ./scripts/deploy_phase4_dashboard.sh"

EOF
