#!/bin/bash
# Complete OpenStack Access Guide
# Use this script for all OpenStack access methods

echo "=========================================================="
echo "🎉 COMPLETE OPENSTACK ACCESS GUIDE 🎉"
echo "Red Hat OpenStack Services on OpenShift (RHOSO)"
echo "Date: $(date)"
echo "=========================================================="

# Set OpenShift context
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"

echo -e "\n✅ OPENSTACK DEPLOYMENT STATUS"
echo "=============================="
CONTROL_PLANE_STATUS=$(cd /Users/<USER>/openshift-virt && ./bin/oc get openstackcontrolplane openstack -n openstack -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null)
if [ "$CONTROL_PLANE_STATUS" = "True" ]; then
    echo "🟢 Control Plane: READY"
else
    echo "🔴 Control Plane: NOT READY"
fi

PODS_RUNNING=$(cd /Users/<USER>/openshift-virt && ./bin/oc get pods -n openstack --no-headers 2>/dev/null | grep -c "Running")
echo "🟢 Running Pods: $PODS_RUNNING"

echo -e "\n🌐 WEB DASHBOARD ACCESS"
echo "======================"
echo "URL: https://horizon-openstack.apps.sno-rhoso.lab.local:8443"
echo "Username: admin"
echo "Password: OrtL0EKtnzCpWFAjhWD08MW77"
echo "Domain: Default"
echo ""

# Check port forwarding status
if lsof -i :8443 >/dev/null 2>&1; then
    echo "🟢 Port Forwarding: ACTIVE"
else
    echo "🔴 Port Forwarding: NOT ACTIVE"
    echo "   Run: cd /Users/<USER>/openshift-virt && ./bin/oc port-forward svc/horizon 8443:443 -n openstack &"
fi

# Check hosts entry
if grep -q "horizon-openstack.apps.sno-rhoso.lab.local" /etc/hosts 2>/dev/null; then
    echo "🟢 Hosts Entry: CONFIGURED"
else
    echo "🔴 Hosts Entry: MISSING"
    echo "   Run: sudo sh -c 'echo \"127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local\" >> /etc/hosts'"
fi

echo -e "\n💻 COMMAND LINE ACCESS"
echo "====================="
echo "OpenStack CLI:"
echo "cd /Users/<USER>/openshift-virt"
echo "export KUBECONFIG=\"/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress\""
echo "./bin/oc exec -it openstackclient -n openstack -- bash"
echo ""

echo -e "\n🔧 OPENSTACK SERVICES STATUS"
echo "============================"
cd /Users/<USER>/openshift-virt && ./bin/oc exec -it openstackclient -n openstack -- openstack service list 2>/dev/null || echo "Could not connect to OpenStack CLI"

echo -e "\n📡 API ENDPOINTS"
echo "==============="
echo "All OpenStack APIs are available via internal routes:"
cd /Users/<USER>/openshift-virt && ./bin/oc get routes -n openstack --no-headers 2>/dev/null | awk '{print $1 ": " $2}' || echo "Could not list routes"

echo -e "\n🚀 QUICK ACTIONS"
echo "================"
echo "1. Access Dashboard: Open https://horizon-openstack.apps.sno-rhoso.lab.local:8443"
echo "2. Check Services: ./bin/oc exec -it openstackclient -n openstack -- openstack service list"
echo "3. View Pods: ./bin/oc get pods -n openstack"
echo "4. Check Logs: ./bin/oc logs -f <pod-name> -n openstack"

echo -e "\n🧹 CLEANUP COMMANDS"
echo "=================="
echo "Stop Port Forward: pkill -f 'port-forward.*horizon'"
echo "Remove Hosts Entry: sudo sed -i '' '/horizon-openstack.apps.sno-rhoso.lab.local/d' /etc/hosts"

echo -e "\n=========================================================="
echo "🎊 CONGRATULATIONS! Your OpenStack Cloud is Ready! 🎊"
echo "=========================================================="
