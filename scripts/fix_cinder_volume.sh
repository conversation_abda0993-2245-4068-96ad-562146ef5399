#!/bin/bash
#
# RHOSO Cinder Fix Deployment Script
# Resolves CrashLoopBackOff issue with cinder-volume pod
#
# Issue: Missing 'enabled_backends' configuration causing pod crashes
# Solution: Apply corrected OpenStackControlPlane with LVM backend configuration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"
BIN_DIR="$PROJECT_ROOT/bin"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== RHOSO Cinder Volume Service Fix ===${NC}"
echo "Project Root: $PROJECT_ROOT"
echo "Config Directory: $CONFIG_DIR"
echo

# Check if OpenShift CLI is available
if [[ -f "$BIN_DIR/oc" ]]; then
    OC_CMD="$BIN_DIR/oc"
    echo -e "${GREEN}✓${NC} Using OpenShift CLI: $OC_CMD"
else
    OC_CMD="oc"
    echo -e "${YELLOW}⚠${NC} Using system OpenShift CLI (oc command)"
fi

# Verify cluster connectivity
echo
echo -e "${YELLOW}Checking cluster connectivity...${NC}"
if ! $OC_CMD whoami &>/dev/null; then
    echo -e "${RED}✗${NC} Not connected to OpenShift cluster"
    echo "Please log in with: $OC_CMD login <cluster-url>"
    exit 1
fi

CURRENT_USER=$($OC_CMD whoami)
CURRENT_PROJECT=$($OC_CMD project -q 2>/dev/null || echo "none")
echo -e "${GREEN}✓${NC} Connected as: $CURRENT_USER"
echo -e "${GREEN}✓${NC} Current project: $CURRENT_PROJECT"

# Switch to openstack namespace
echo
echo -e "${YELLOW}Switching to openstack namespace...${NC}"
if ! $OC_CMD project openstack &>/dev/null; then
    echo -e "${RED}✗${NC} Failed to switch to openstack namespace"
    echo "Please ensure the openstack namespace exists"
    exit 1
fi
echo -e "${GREEN}✓${NC} Switched to openstack namespace"

# Check current pod status
echo
echo -e "${YELLOW}Checking current cinder-volume pod status...${NC}"
if $OC_CMD get pod cinder-volume-volume1-0 &>/dev/null; then
    POD_STATUS=$($OC_CMD get pod cinder-volume-volume1-0 -o jsonpath='{.status.phase}')
    RESTART_COUNT=$($OC_CMD get pod cinder-volume-volume1-0 -o jsonpath='{.status.containerStatuses[0].restartCount}')
    echo -e "${YELLOW}Current Status:${NC} $POD_STATUS"
    echo -e "${YELLOW}Restart Count:${NC} $RESTART_COUNT"
    
    if [[ "$RESTART_COUNT" -gt 10 ]]; then
        echo -e "${RED}⚠${NC} High restart count detected - this fix should resolve the issue"
    fi
else
    echo -e "${YELLOW}⚠${NC} cinder-volume-volume1-0 pod not found (may not be created yet)"
fi

# Backup current configuration
echo
echo -e "${YELLOW}Creating backup of current configuration...${NC}"
BACKUP_FILE="$CONFIG_DIR/openstack_control_plane_backup_$(date +%Y%m%d_%H%M%S).yaml"
if $OC_CMD get openstackcontrolplane openstack-control-plane -o yaml > "$BACKUP_FILE" 2>/dev/null; then
    echo -e "${GREEN}✓${NC} Backup saved to: $BACKUP_FILE"
else
    echo -e "${YELLOW}⚠${NC} Could not create backup (OpenStackControlPlane may not exist yet)"
fi

# Apply the fixed configuration
echo
echo -e "${YELLOW}Applying fixed OpenStackControlPlane configuration...${NC}"
FIXED_CONFIG="$CONFIG_DIR/openstack_control_plane_fixed.yaml"

if [[ ! -f "$FIXED_CONFIG" ]]; then
    echo -e "${RED}✗${NC} Fixed configuration file not found: $FIXED_CONFIG"
    exit 1
fi

echo "Configuration file: $FIXED_CONFIG"
echo -e "${YELLOW}Key changes being applied:${NC}"
echo "  • Added customServiceConfig to cinder.template.cinderVolumes.volume1"
echo "  • Configured enabled_backends=lvm"
echo "  • Set up LVM volume driver with iSCSI protocol"
echo

read -p "Proceed with applying the configuration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Deployment cancelled by user${NC}"
    exit 0
fi

echo -e "${YELLOW}Applying configuration...${NC}"
if $OC_CMD apply -f "$FIXED_CONFIG"; then
    echo -e "${GREEN}✓${NC} Configuration applied successfully"
else
    echo -e "${RED}✗${NC} Failed to apply configuration"
    exit 1
fi

# Monitor deployment progress
echo
echo -e "${YELLOW}Monitoring deployment progress...${NC}"
echo "This may take a few minutes for the pod to restart and stabilize..."
echo

# Wait for pod restart
for i in {1..30}; do
    sleep 10
    if $OC_CMD get pod cinder-volume-volume1-0 &>/dev/null; then
        NEW_STATUS=$($OC_CMD get pod cinder-volume-volume1-0 -o jsonpath='{.status.phase}')
        NEW_RESTART_COUNT=$($OC_CMD get pod cinder-volume-volume1-0 -o jsonpath='{.status.containerStatuses[0].restartCount}')
        
        echo -e "${YELLOW}Check $i/30:${NC} Status=$NEW_STATUS, Restarts=$NEW_RESTART_COUNT"
        
        if [[ "$NEW_STATUS" == "Running" ]] && [[ "$NEW_RESTART_COUNT" == "$RESTART_COUNT" ]]; then
            echo -e "${GREEN}✓${NC} Pod is running stable!"
            break
        elif [[ "$i" -eq 30 ]]; then
            echo -e "${YELLOW}⚠${NC} Monitoring timeout reached. Check pod status manually."
        fi
    else
        echo -e "${YELLOW}Check $i/30:${NC} Pod not found (may be recreating)"
    fi
done

# Final status check
echo
echo -e "${GREEN}=== Final Status Check ===${NC}"
if $OC_CMD get pod cinder-volume-volume1-0 &>/dev/null; then
    echo -e "${YELLOW}Pod Status:${NC}"
    $OC_CMD get pod cinder-volume-volume1-0 -o wide
    
    echo
    echo -e "${YELLOW}Recent Events:${NC}"
    $OC_CMD get events --sort-by='.lastTimestamp' | grep cinder-volume | tail -5
    
    echo
    echo -e "${YELLOW}Container Logs (last 10 lines):${NC}"
    echo "=== cinder-volume container ==="
    $OC_CMD logs cinder-volume-volume1-0 -c cinder-volume --tail=10 | grep -E "(ERROR|enabled_backends|successfully|Started)" || echo "No relevant log entries found"
    
    echo
    echo "=== probe container ==="
    $OC_CMD logs cinder-volume-volume1-0 -c probe --tail=10 | grep -E "(ERROR|TypeError|successfully)" || echo "No relevant log entries found"
else
    echo -e "${RED}✗${NC} cinder-volume-volume1-0 pod not found"
fi

echo
echo -e "${GREEN}=== Deployment Complete ===${NC}"
echo "Next steps:"
echo "1. Monitor pod stability for several minutes"
echo "2. Check OpenStack service status with: $OC_CMD get openstackcontrolplane"
echo "3. Test volume creation once all services are ready"
echo "4. Update documentation with deployment results"

echo
echo -e "${GREEN}Fix Summary:${NC}"
echo "  Issue: Missing 'enabled_backends' configuration"
echo "  Solution: Added LVM backend configuration to cinder volumes"
echo "  Status: Configuration applied - monitoring for stability"
