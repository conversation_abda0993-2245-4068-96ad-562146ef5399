#!/bin/bash
# Horizon Dashboard Access Solution
# This script provides multiple methods to access the OpenStack Horizon dashboard

echo "==========================================="
echo "OpenStack Horizon Dashboard Access Fix"
echo "Date: $(date)"
echo "==========================================="

echo -e "\n🔍 PROBLEM IDENTIFIED:"
echo "Horizon redirects to internal cluster domain 'horizon-openstack.apps.sno-rhoso.lab.local'"
echo "Your browser cannot resolve this internal hostname."

echo -e "\n💡 SOLUTION OPTIONS:\n"

echo "OPTION 1: Host File Mapping (Recommended)"
echo "=========================================="
echo "1. Add this line to your /etc/hosts file:"
echo "127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local"
echo ""
echo "2. Run this command to add it (requires sudo password):"
echo "sudo sh -c 'echo \"127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local\" >> /etc/hosts'"
echo ""
echo "3. Then access: https://horizon-openstack.apps.sno-rhoso.lab.local:8443"
echo ""

echo "OPTION 2: Direct curl access (Testing)"
echo "======================================"
echo "curl -k -L -H \"Host: horizon-openstack.apps.sno-rhoso.lab.local\" https://localhost:8443/dashboard/"
echo ""

echo "OPTION 3: Browser with host override"
echo "===================================="
echo "Use a browser extension or dev tools to set Host header to:"
echo "horizon-openstack.apps.sno-rhoso.lab.local"
echo ""

echo "OPTION 4: OpenStack CLI Dashboard"
echo "================================="
echo "Access OpenStack directly via command line:"
echo "export KUBECONFIG=\"/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress\""
echo "./bin/oc exec -it openstackclient -n openstack -- bash"
echo ""

echo -e "\n🚀 RECOMMENDED STEPS:"
echo "1. Run the sudo command above to add the hosts entry"
echo "2. Access https://horizon-openstack.apps.sno-rhoso.lab.local:8443"
echo "3. Login with admin / OrtL0EKtnzCpWFAjhWD08MW77"

echo -e "\n⚠️  NOTE: Remember to remove the hosts entry later with:"
echo "sudo sed -i '' '/horizon-openstack.apps.sno-rhoso.lab.local/d' /etc/hosts"

echo -e "\n==========================================="
