#!/bin/bash
# Persistent OpenStack Horizon Access Script
# This script provides multiple methods to restore access after reboots

SCRIPT_DIR="/Users/<USER>/openshift-virt"
KUBECONFIG_PATH="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"
HORIZON_PORT="9443"
HOSTS_ENTRY="127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local"

echo "=========================================================="
echo "🔄 RESTORING OPENSTACK HORIZON ACCESS"
echo "Date: $(date)"
echo "=========================================================="

# Function to check if port forwarding is active
check_port_forward() {
    if lsof -i :$HORIZON_PORT >/dev/null 2>&1; then
        echo "✅ Port forwarding is active on port $HORIZON_PORT"
        return 0
    else
        echo "❌ Port forwarding is not active on port $HORIZON_PORT"
        return 1
    fi
}

# Function to check hosts entry
check_hosts_entry() {
    if grep -q "horizon-openstack.apps.sno-rhoso.lab.local" /etc/hosts 2>/dev/null; then
        echo "✅ Hosts entry is configured"
        return 0
    else
        echo "❌ Hosts entry is missing"
        return 1
    fi
}

# Function to start port forwarding
start_port_forward() {
    echo "🚀 Starting port forwarding..."
    cd "$SCRIPT_DIR"
    export KUBECONFIG="$KUBECONFIG_PATH"
    
    # Kill any existing port forwards
    pkill -f "port-forward.*horizon" 2>/dev/null || true
    
    # Start new port forward in background
    ./bin/oc port-forward svc/horizon $HORIZON_PORT:443 -n openstack &
    
    # Wait a moment and check if it started
    sleep 3
    if check_port_forward; then
        echo "✅ Port forwarding started successfully"
        echo "📝 Process ID: $(pgrep -f "port-forward.*horizon")"
    else
        echo "❌ Failed to start port forwarding"
        return 1
    fi
}

# Function to add hosts entry
add_hosts_entry() {
    echo "🔧 Adding hosts entry..."
    if ! check_hosts_entry; then
        echo "Adding: $HOSTS_ENTRY"
        sudo sh -c "echo '$HOSTS_ENTRY' >> /etc/hosts"
        echo "✅ Hosts entry added"
    fi
}

# Function to check OpenStack cluster connectivity
check_cluster() {
    echo "🔍 Checking OpenStack cluster status..."
    cd "$SCRIPT_DIR"
    export KUBECONFIG="$KUBECONFIG_PATH"
    
    if ./bin/oc get openstackcontrolplane openstack -n openstack -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' 2>/dev/null | grep -q "True"; then
        echo "✅ OpenStack control plane is ready"
        return 0
    else
        echo "❌ OpenStack control plane is not ready or cluster unreachable"
        return 1
    fi
}

# Main restoration process
echo -e "\n🔍 CHECKING CURRENT STATUS"
echo "=========================="

check_hosts_entry
check_port_forward
check_cluster

echo -e "\n🛠️ RESTORATION ACTIONS"
echo "====================="

# Add hosts entry if missing
if ! check_hosts_entry; then
    add_hosts_entry
fi

# Start port forwarding if not active and cluster is reachable
if ! check_port_forward && check_cluster; then
    start_port_forward
elif ! check_cluster; then
    echo "⚠️  Cannot start port forwarding - cluster not reachable"
    echo "   Please ensure your OpenShift cluster is running and accessible"
fi

echo -e "\n🌐 ACCESS INFORMATION"
echo "===================="
echo "Dashboard URL: https://horizon-openstack.apps.sno-rhoso.lab.local:$HORIZON_PORT/dashboard/"
echo "Username: admin"
echo "Password: OrtL0EKtnzCpWFAjhWD08MW77"
echo "Domain: Default"

echo -e "\n📋 CURRENT STATUS"
echo "================"
check_hosts_entry
check_port_forward

if check_port_forward; then
    echo "🎉 Horizon dashboard should now be accessible!"
    echo "🌐 Open: https://horizon-openstack.apps.sno-rhoso.lab.local:$HORIZON_PORT/dashboard/"
else
    echo "⚠️  Port forwarding is not active. Check cluster connectivity."
fi

echo -e "\n🔄 TO RUN AFTER REBOOT"
echo "====================="
echo "Run this script: $0"
echo "Or manually execute:"
echo "1. sudo sh -c 'echo \"$HOSTS_ENTRY\" >> /etc/hosts'"
echo "2. cd $SCRIPT_DIR && export KUBECONFIG=\"$KUBECONFIG_PATH\""
echo "3. ./bin/oc port-forward svc/horizon $HORIZON_PORT:443 -n openstack &"

echo -e "\n=========================================================="
