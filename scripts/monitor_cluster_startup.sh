#!/bin/zsh

# Monitor OpenShift cluster startup
# Script for macOS/zsh

echo "🔍 Starting OpenShift cluster startup monitoring..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Counter for attempts
attempt=1
start_time=$(date +%s)

while true; do
    current_time=$(date)
    echo "[$attempt] $current_time: Testing API server..."
    
    # Test API server health endpoint
    if curl -k -m 5 --connect-timeout 3 https://api.sno-rhoso.lab.local:6443/healthz &>/dev/null; then
        echo "✅ API server is responding!"
        echo ""
        echo "🎉 Cluster is starting up successfully!"
        
        # Calculate elapsed time
        end_time=$(date +%s)
        elapsed=$((end_time - start_time))
        minutes=$((elapsed / 60))
        seconds=$((elapsed % 60))
        echo "⏱️  Total startup time: ${minutes}m ${seconds}s"
        
        echo ""
        echo "🔗 You can now try accessing:"
        echo "   • API: https://api.sno-rhoso.lab.local:6443"
        echo "   • Console: https://console-openshift-console.apps.sno-rhoso.lab.local"
        echo "   • Username: kubeadmin"
        echo "   • Password: gPzV2-uCSaa-W7SqS-xuy2g"
        
        break
    else
        echo "⏳ API server not ready yet (attempt $attempt)"
        
        # Test basic connectivity
        if ping -c 1 -W 1000 10.0.0.94 &>/dev/null; then
            echo "   ✅ VM is responding to ping"
        else
            echo "   ❌ VM is not responding to ping"
        fi
        
        # Test SSH (optional)
        if nc -z -w 2 10.0.0.94 22 &>/dev/null; then
            echo "   ✅ SSH port is open"
        else
            echo "   ⏳ SSH port not ready"
        fi
    fi
    
    echo ""
    attempt=$((attempt + 1))
    
    # Wait 30 seconds before next check
    sleep 30
done
