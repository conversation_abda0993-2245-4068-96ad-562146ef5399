#!/bin/bash

# Phase 4: Dashboard Configuration & Automation
# Set up Horizon dashboard access and post-reboot automation

set -e

PROJECT_DIR="/Volumes/T7/projects/openshift-virt"
cd "$PROJECT_DIR"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo -e "${BLUE}🌐 Phase 4: Dashboard Configuration${NC}"
echo "==================================="

# Set kubeconfig
export KUBECONFIG="$PROJECT_DIR/ocp/auth/kubeconfig"

# Verify OpenStack is ready
log "Verifying OpenStack deployment..."
if ! ./bin/oc get openstackcontrolplane -n openstack >/dev/null 2>&1; then
    echo "❌ OpenStack not deployed. Run Phase 3 first."
    exit 1
fi

# Wait for OpenStack to be ready
log "Waiting for OpenStack services to be ready..."
./bin/oc wait --for=condition=Ready openstackcontrolplane/openstack -n openstack --timeout=1800s

# Get admin password
log "Retrieving admin password..."
ADMIN_PASSWORD=$(./bin/oc get secret openstack-config -n openstack -o jsonpath='{.data.AdminPassword}' | base64 -d)

# Set up port forwarding for Horizon
log "Setting up Horizon dashboard access..."

# Create port forwarding script
cat > scripts/setup_horizon_access.sh << 'EOF'
#!/bin/bash

PROJECT_DIR="/Volumes/T7/projects/openshift-virt"
export KUBECONFIG="$PROJECT_DIR/ocp/auth/kubeconfig"

# Kill existing port forwarding
pkill -f "port-forward.*horizon" || true

# Set up new port forwarding
echo "Setting up Horizon port forwarding..."
./bin/oc port-forward service/horizon 9443:443 -n openstack &

# Add hosts entry
HOSTS_ENTRY="127.0.0.1 horizon-openstack.apps.sno-rhoso.lab.local"
if ! grep -q "horizon-openstack.apps.sno-rhoso.lab.local" /etc/hosts; then
    echo "Adding hosts entry..."
    echo "$HOSTS_ENTRY" | sudo tee -a /etc/hosts
fi

echo "✅ Horizon dashboard accessible at:"
echo "   https://horizon-openstack.apps.sno-rhoso.lab.local:9443/dashboard/"

EOF

chmod +x scripts/setup_horizon_access.sh

# Install LaunchAgent for automatic restoration
log "Installing LaunchAgent for post-reboot automation..."
./scripts/install_horizon_launchagent.sh

# Run initial setup
log "Configuring initial dashboard access..."
./scripts/setup_horizon_access.sh

# Create restoration alias
log "Creating restoration command alias..."
if ! grep -q "alias restore-horizon" ~/.zshrc; then
    echo "alias restore-horizon='cd $PROJECT_DIR && ./scripts/restore_horizon_access.sh'" >> ~/.zshrc
fi

success "Dashboard configuration complete!"

echo ""
echo -e "${GREEN}🎉 DEPLOYMENT COMPLETE!${NC}"
echo "========================"
echo ""
echo -e "${BLUE}🌐 OpenStack Dashboard Access:${NC}"
echo "URL: https://horizon-openstack.apps.sno-rhoso.lab.local:9443/dashboard/"
echo "Username: admin"
echo "Password: $ADMIN_PASSWORD"
echo "Domain: Default"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "• restore-horizon              # Restore access after reboots"
echo "• ./bin/oc get pods -n openstack  # Check OpenStack status"
echo "• ./scripts/validate_openstack_deployment.sh  # Full validation"
echo ""
echo -e "${BLUE}📚 Documentation Created:${NC}"
echo "• Complete success guide in docs/"
echo "• All configuration files preserved"
echo "• LaunchAgent installed for automation"
echo ""
echo -e "${GREEN}✅ Your enterprise OpenStack cloud is ready!${NC}"

EOF
