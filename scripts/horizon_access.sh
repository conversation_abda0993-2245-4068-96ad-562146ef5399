#!/bin/bash
# OpenStack Horizon Dashboard Access Script
# Provides multiple ways to access the Horizon dashboard when cluster domains are not externally accessible

echo "==========================================="
echo "OpenStack Horizon Dashboard Access"
echo "Date: $(date)"
echo "==========================================="

# Set OpenShift context
export KUBECONFIG="/Users/<USER>/openshift-virt/ocp/auth/kubeconfig-noingress"

echo -e "\n🔐 Getting OpenStack Admin Credentials..."
ADMIN_PASSWORD=$(oc get secret osp-secret -n openstack -o jsonpath='{.data.AdminPassword}' | base64 -d)

echo "Username: admin"
echo "Password: $ADMIN_PASSWORD"
echo "Domain: Default"

echo -e "\n🌐 Access Methods:\n"

echo "METHOD 1: Port Forward (Recommended)"
echo "======================="
echo "Run this command in a separate terminal:"
echo "oc port-forward svc/horizon 8443:443 -n openstack"
echo ""
echo "Then access: https://localhost:8443"
echo "⚠️  Accept the SSL certificate warning in your browser"
echo ""

echo "METHOD 2: Direct Pod Port Forward"
echo "================================="
POD_NAME=$(oc get pod -n openstack -l app=horizon -o jsonpath='{.items[0].metadata.name}')
if [ -n "$POD_NAME" ]; then
    echo "Run this command in a separate terminal:"
    echo "oc port-forward pod/$POD_NAME 8080:80 -n openstack"
    echo ""
    echo "Then access: http://localhost:8080"
else
    echo "No Horizon pod found"
fi

echo -e "\nMETHOD 3: OpenStack CLI Access"
echo "=============================="
echo "Access OpenStack via CLI:"
echo "oc exec -it openstackclient -n openstack -- bash"
echo ""

echo -e "\nMETHOD 4: Service Information"
echo "============================="
echo "Horizon Service Details:"
oc get svc horizon -n openstack
echo ""

echo -e "Routes (Internal cluster access only):"
oc get route horizon -n openstack

echo -e "\n🚀 Quick Access Command:"
echo "========================="
echo "To start port forwarding immediately, run:"
echo "oc port-forward svc/horizon 8443:443 -n openstack &"
echo ""
echo "This will run in the background. Access via: https://localhost:8443"

echo -e "\n==========================================="
