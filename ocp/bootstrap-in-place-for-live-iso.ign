{"ignition": {"version": "3.2.0"}, "passwd": {"users": [{"name": "core", "sshAuthorizedKeys": ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCurg448sD+MZ<PERSON>Zo5LMTyDRQ/CdlFDS/x5wRFqoFCdFatcYhl/14Vy+W9WW6+yHo4yWYCIyQQdQajgfxybLJTB3Dq8owd+R4UpFjaZGrAuFPJqRYpcpbrR9zDiujgZEdADRX3nuSXJs5x7FbNtu7cfI+vwSw0FYMOXmO2HTQfPTaLT71EzTCcbk6zPAtEKnE5mTNQMNRsoFtJsa2xp/ayjX9FGq8CR3uuSgYOED7UfBzKXCSsHaiRCsYtvfOEVRKYel8DHEx+MvmemJjWwdq95YMoRMGZYaUUK6Sz3BdKK/4wqmayMFMkLeZu+HgJ34xvjuezksZMppfzEbF+T/S4gxwwled40oX2gW1IlG6mutqnbgu1cTfV9ezu68ccfSue9SQvf2Dy+IJvydHONuWd8x8H1eqRzJSySYNxtOta/0cMdtZbmkEBR3m94wkwAS+2zao1IWcBXOz/BeiMeB7fnL7pKuqybtprobt03A8f3sJwkYchlCfSq5cTXT2Bzc/I1BGxnqMRoXkJxKmTwZIV/0DuJOXjKV8G8aPUovfP5POyS0PmshVIt2srVLHZ2eInrcJilPzckxVN8XYM2syUx0p3eVUs308E7DtnRZgPF/7Jb+GKLryfKVin5EgAkLI15pOKPfs4ajXerV+/IctXnRFFEggze/1UR5OnjGA1GdYw== eduardo@MacBook-Pro\n", "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC9y+SBgrtCR+EN8UdHDmT6ehrthLloszZ+ad93ohVpehnb/cgLy4vuP2XbadpK6qo7i1FRoHG8/T5aao+417pD50+B6rNP8bLSxolkTldRX5c4aByuKU5NelrWXKjKz85zzlq5NpOjIKIA7p9lwMzvGzj8S6xNk2ZG1NN76qYw2mSRfmx2OjONGsFfXIFU2Plrn60fXk7lwARjMqoPaQ6wA8yDfu8vGhd3Q9rkdmHJ279R8EWpAAtRXDzLkT82G4w0DwCo0ZVbVrRbpYU1YQvjHhOV98fL6RMlqyxou/D1FMacVYfrwJHB5plNc8Ii1qfN6G8rIr12VTI+iwfg0TJz\n"]}]}, "storage": {"files": [{"overwrite": true, "path": "/etc/containers/registries.conf", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/ignition-machine-config-encapsulated.json", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,ewogICJtZXRhZGF0YSI6IHsKICAgICJuYW1lIjogImJvb3RzdHJhcC1maXBzIgogIH0sCiAgInNwZWMiOiB7CiAgICAiY29uZmlnIjogewogICAgICAiaWduaXRpb24iOiB7CiAgICAgICAgInZlcnNpb24iOiAiMy4xLjAiCiAgICAgIH0KICAgIH0sCiAgICAia2VybmVsQXJndW1lbnRzIjogW10sCiAgICAiZmlwcyI6IGZhbHNlCiAgfQp9Cg=="}, "mode": 384}, {"overwrite": false, "path": "/etc/motd", "user": {"name": "root"}, "append": [{"source": "data:text/plain;charset=utf-8;base64,VGhpcyBpcyB0aGUgYm9vdHN0cmFwIG5vZGU7IGl0IHdpbGwgYmUgZGVzdHJveWVkIHdoZW4gdGhlIG1hc3RlciBpcyBmdWxseSB1cC4KClRoZSBwcmltYXJ5IHNlcnZpY2VzIGFyZSByZWxlYXNlLWltYWdlLnNlcnZpY2UgZm9sbG93ZWQgYnkgYm9vdGt1YmUuc2VydmljZS4gVG8gd2F0Y2ggdGhlaXIgc3RhdHVzLCBydW4gZS5nLgoKICBqb3VybmFsY3RsIC1iIC1mIC11IHJlbGVhc2UtaW1hZ2Uuc2VydmljZSAtdSBib290a3ViZS5zZXJ2aWNlCg=="}], "mode": 420}, {"overwrite": true, "path": "/etc/pki/ca-trust/source/anchors/ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/profile.d/proxy.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/systemd/system.conf.d/10-default-env.conf", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,W01hbmFnZXJdCg=="}, "mode": 384}, {"overwrite": true, "path": "/root/.docker/config.json", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/usr/local/bin/approve-csr.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MxMDkxICAjIHVzaW5nIHBhdGggb24gYm9vdHN0cmFwIG1hY2hpbmUKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCktVQkVDT05GSUc9IiR7MX0iCgplY2hvICJBcHByb3ZpbmcgYWxsIENTUiByZXF1ZXN0cyB1bnRpbCBib290c3RyYXBwaW5nIGlzIGNvbXBsZXRlLi4uIgp3aGlsZSBbICEgLWYgL29wdC9vcGVuc2hpZnQvLmJvb3RrdWJlLmRvbmUgXQpkbwogICAgb2MgLS1rdWJlY29uZmlnPSIkS1VCRUNPTkZJRyIgZ2V0IGNzciAtLW5vLWhlYWRlcnMgfCBncmVwIFBlbmRpbmcgfCBcCiAgICAgICAgYXdrICd7cHJpbnQgJDF9JyB8IFwKICAgICAgICB4YXJncyAtLW5vLXJ1bi1pZi1lbXB0eSBvYyAtLWt1YmVjb25maWc9IiRLVUJFQ09ORklHIiBhZG0gY2VydGlmaWNhdGUgYXBwcm92ZQoJc2xlZXAgMjAKZG9uZQo="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootkube.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-cluster-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKQVBJX1NFUlZFUl9BUlRJRkFDVFNfRElSPSIvdG1wL2FydGlmYWN0cy1hcGktc2VydmVyLXRlbXAiCmZ1bmN0aW9uIHF1ZXVlKCkgewogICAgbG9jYWwgVEFSR0VUPSIke0FSVElGQUNUU19URU1QfS8kezF9IgogICAgc2hpZnQKICAgICMgc2hlbGxjaGVjayBkaXNhYmxlPVNDMjE1NQogICAgbG9jYWwgTElWRT0iJChqb2JzIHwgd2MgLWwpIgogICAgd2hpbGUgW1sgIiR7TElWRX0iIC1nZSA0NSBdXTsgZG8KICAgICAgICBzbGVlcCAxCiAgICAgICAgTElWRT0iJChqb2JzIHwgd2MgLWwpIgogICAgZG9uZQogICAgaWYgW1sgLW4gIiR7RklMVEVSLX0iIF1dOyB0aGVuCiAgICAgICAgIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MyMDI0CiAgICAgICAgc3VkbyBLVUJFQ09ORklHPSIke0dBVEhFUl9LVUJFQ09ORklHfSIgIiR7QH0iIHwgIiR7RklMVEVSfSIgPiIke1RBUkdFVH0iICYKICAgIGVsc2UKICAgICAgICAjIHNoZWxsY2hlY2sgZGlzYWJsZT1TQzIwMjQKICAgICAgICBzdWRvIEtVQkVDT05GSUc9IiR7R0FUSEVSX0tVQkVDT05GSUd9IiAiJHtAfSIgPiIke1RBUkdFVH0iICYKICAgIGZpCn0KCmZ1bmN0aW9uIGNsdXN0ZXJfYm9vdHN0cmFwX2dhdGhlcigpIHsKICAgIEdBVEhFUl9LVUJFQ09ORklHPSIvb3B0L29wZW5zaGlmdC9hdXRoL2t1YmVjb25maWciCgogICAgQUxURVJOQVRJVkVfS1VCRUNPTkZJRz0iL2V0Yy9rdWJlcm5ldGVzL2Jvb3RzdHJhcC1zZWNyZXRzL2t1YmVjb25maWciCiAgICBpZiBbWyAtZiAke0FMVEVSTkFUSVZFX0tVQkVDT05GSUd9IF1dOyB0aGVuCiAgICAgICAgR0FUSEVSX0tVQkVDT05GSUc9JHtBTFRFUk5BVElWRV9LVUJFQ09ORklHfQogICAgZmkKCiAgICBlY2hvICJVc2luZyAke0dBVEhFUl9LVUJFQ09ORklHfSBhcyBLVUJFQ09ORklHIgoKICAgIEFSVElGQUNUU19URU1QPSIkKG1rdGVtcCAtZCkiCgogICAgbWtkaXIgLXAgIiR7QVJUSUZBQ1RTX1RFTVB9L3Jlc291cmNlcyIKCiAgICBlY2hvICJHYXRoZXJpbmcgY2x1c3RlciByZXNvdXJjZXMgLi4uIgogICAgcXVldWUgcmVzb3VyY2VzL25vZGVzLmxpc3Qgb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IG5vZGVzIC1vIGpzb25wYXRoIC0tdGVtcGxhdGUgJ3tyYW5nZSAuaXRlbXNbKl19ey5tZXRhZGF0YS5uYW1lfXsiXG4ifXtlbmR9JwogICAgcXVldWUgcmVzb3VyY2VzL21hc3RlcnMubGlzdCBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgbm9kZXMgLW8ganNvbnBhdGggLWwgJ25vZGUtcm9sZS5rdWJlcm5ldGVzLmlvL21hc3RlcicgLS10ZW1wbGF0ZSAne3JhbmdlIC5pdGVtc1sqXX17Lm1ldGFkYXRhLm5hbWV9eyJcbiJ9e2VuZH0nCiAgICAjIFNoZWxsQ2hlY2sgZG9lc24ndCByZWFsaXplIHRoYXQgJG5zIGlzIGZvciB0aGUgR28gdGVtcGxhdGUsIG5vdCBzb21ldGhpbmcgd2UncmUgdHJ5aW5nIHRvIGV4cGFuZCBpbiB0aGUgc2hlbGwKICAgICMgc2hlbGxjaGVjayBkaXNhYmxlPTIwMTYKICAgIHF1ZXVlIHJlc291cmNlcy9jb250YWluZXJzIG9jIC0tcmVxdWVzdC10aW1lb3V0PTVzIGdldCBwb2RzIC0tYWxsLW5hbWVzcGFjZXMgLS10ZW1wbGF0ZSAne3sgcmFuZ2UgLml0ZW1zIH19e3sgJG5hbWUgOj0gLm1ldGFkYXRhLm5hbWUgfX17eyAkbnMgOj0gLm1ldGFkYXRhLm5hbWVzcGFjZSB9fXt7IHJhbmdlIC5zcGVjLmNvbnRhaW5lcnMgfX0tbiB7eyAkbnMgfX0ge3sgJG5hbWUgfX0gLWMge3sgLm5hbWUgfX17eyAiXG4iIH19e3sgZW5kIH19e3sgcmFuZ2UgLnNwZWMuaW5pdENvbnRhaW5lcnMgfX0tbiB7eyAkbnMgfX0ge3sgJG5hbWUgfX0gLWMge3sgLm5hbWUgfX17eyAiXG4iIH19e3sgZW5kIH19e3sgZW5kIH19JwogICAgcXVldWUgcmVzb3VyY2VzL2FwaS1wb2RzIG9jIC0tcmVxdWVzdC10aW1lb3V0PTVzIGdldCBwb2RzIC1sIGFwaXNlcnZlcj10cnVlIC0tYWxsLW5hbWVzcGFjZXMgLS10ZW1wbGF0ZSAne3sgcmFuZ2UgLml0ZW1zIH19LW4ge3sgLm1ldGFkYXRhLm5hbWVzcGFjZSB9fSB7eyAubWV0YWRhdGEubmFtZSB9fXt7ICJcbiIgfX17eyBlbmQgfX0nCgogICAgcXVldWUgcmVzb3VyY2VzL2FwaXNlcnZpY2VzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGFwaXNlcnZpY2VzIC1vIGpzb24KICAgIHF1ZXVlIHJlc291cmNlcy9jbHVzdGVyb3BlcmF0b3JzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGNsdXN0ZXJvcGVyYXRvcnMgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL2NsdXN0ZXJ2ZXJzaW9uLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGNsdXN0ZXJ2ZXJzaW9uIC1vIGpzb24KICAgIHF1ZXVlIHJlc291cmNlcy9jb25maWdtYXBzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGNvbmZpZ21hcHMgLS1hbGwtbmFtZXNwYWNlcyAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvY3NyLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGNzciAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvZW5kcG9pbnRzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGVuZHBvaW50cyAtLWFsbC1uYW1lc3BhY2VzIC1vIGpzb24KICAgIHF1ZXVlIHJlc291cmNlcy9ldmVudHMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgZXZlbnRzIC0tYWxsLW5hbWVzcGFjZXMgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL2t1YmVhcGlzZXJ2ZXIuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQga3ViZWFwaXNlcnZlciAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMva3ViZWNvbnRyb2xsZXJtYW5hZ2VyLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IGt1YmVjb250cm9sbGVybWFuYWdlciAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvbWFjaGluZXMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgbWFjaGluZXMgLS1hbGwtbmFtZXNwYWNlcyAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvbWFjaGluZWNvbmZpZ3Bvb2xzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IG1hY2hpbmVjb25maWdwb29scyAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvbWFjaGluZWNvbmZpZ3MuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgbWFjaGluZWNvbmZpZ3MgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL25hbWVzcGFjZXMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgbmFtZXNwYWNlcyAtbyBqc29uCiAgICBxdWV1ZSByZXNvdXJjZXMvbm9kZXMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgbm9kZXMgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL29wZW5zaGlmdGFwaXNlcnZlci5qc29uIG9jIC0tcmVxdWVzdC10aW1lb3V0PTVzIGdldCBvcGVuc2hpZnRhcGlzZXJ2ZXIgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL3BvZHMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgcG9kcyAtLWFsbC1uYW1lc3BhY2VzIC1vIGpzb24KICAgIHF1ZXVlIHJlc291cmNlcy9yb2xlYmluZGluZ3MuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgcm9sZWJpbmRpbmdzIC0tYWxsLW5hbWVzcGFjZXMgLW8ganNvbgogICAgcXVldWUgcmVzb3VyY2VzL3JvbGVzLmpzb24gb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IHJvbGVzIC0tYWxsLW5hbWVzcGFjZXMgLW8ganNvbgogICAgIyB0aGlzIGp1c3QgbGlzdHMgbmFtZXMgYW5kIG51bWJlciBvZiBrZXlzCiAgICBxdWV1ZSByZXNvdXJjZXMvc2VjcmV0cy1uYW1lcy50eHQgb2MgLS1yZXF1ZXN0LXRpbWVvdXQ9NXMgZ2V0IHNlY3JldHMgLS1hbGwtbmFtZXNwYWNlcwogICAgIyB0aGlzIGFkZHMgYW5ub3RhdGlvbnMsIGJ1dCBzdHJpcHMgb3V0IHRoZSBTQSB0b2tlbnMgYW5kIGRvY2tlcmNmZyBzZWNyZXRzIHdoaWNoIGFyZSBub2lzeSBhbmQgbWF5IGNvbnRhaW4gc2VjcmV0cyBpbiB0aGUgYW5ub3RhdGlvbnMKICAgIHF1ZXVlIHJlc291cmNlcy9zZWNyZXRzLW5hbWVzLXdpdGgtYW5ub3RhdGlvbnMudHh0IG9jIC0tcmVxdWVzdC10aW1lb3V0PTVzIGdldCBzZWNyZXRzIC0tYWxsLW5hbWVzcGFjZXMgLW89Y3VzdG9tLWNvbHVtbnM9TkFNRVNQQUNFOi5tZXRhZGF0YS5uYW1lc3BhY2UsTkFNRToubWV0YWRhdGEubmFtZSxUWVBFOi50eXBlLEFOTk9UQVRJT05TOi5tZXRhZGF0YS5hbm5vdGF0aW9ucyB8IGdyZXAgLXYgLS0gJy10b2tlbi0nIHwgZ3JlcCAtdiAtLSAnLWRvY2tlcmNmZy0nCiAgICBxdWV1ZSByZXNvdXJjZXMvc2VydmljZXMuanNvbiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgc2VydmljZXMgLS1hbGwtbmFtZXNwYWNlcyAtbyBqc29uCgogICAgRklMVEVSPWd6aXAgcXVldWUgcmVzb3VyY2VzL29wZW5hcGkuanNvbi5neiBvYyAtLXJlcXVlc3QtdGltZW91dD01cyBnZXQgLS1yYXcgL29wZW5hcGkvdjIKCiAgICBlY2hvICJXYWl0aW5nIGZvciBsb2dzIC4uLiIKICAgIHdoaWxlIHdhaXQgLW47IGRvIGpvYnM7IGRvbmUKCiAgICBpZiAoKCAkKHN0YXQgLWMlcyAiJHtBUlRJRkFDVFNfVEVNUH0vcmVzb3VyY2VzL29wZW5hcGkuanNvbi5neiIpIDw9IDIwICkpCiAgICB0aGVuCiAgICAgICAgcm0gLXJmICIke0FSVElGQUNUU19URU1QfSIKICAgIGVsc2UKICAgICAgICBybSAtcmYgIiR7QVBJX1NFUlZFUl9BUlRJRkFDVFNfRElSfSIKICAgICAgICBta2RpciAtcCAiJHtBUElfU0VSVkVSX0FSVElGQUNUU19ESVJ9IgogICAgICAgIG12ICIke0FSVElGQUNUU19URU1QfS9yZXNvdXJjZXMiICIke0FQSV9TRVJWRVJfQVJUSUZBQ1RTX0RJUn0iCiAgICBmaQp9Cg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-pivot.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-service-record.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-verify-api-server-urls.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/crio-configure.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1byBwaXBlZmFpbAojIEJlZm9yZSBrdWJlbGV0LnNlcnZpY2UgYW5kIGNyaW8uc2VydmljZSBzdGFydCwgZW5zdXJlCiMgdGhhdCB3ZSdyZSB1c2luZyB0aGUgcGF1c2UgaW1hZ2UgZnJvbSBvdXIgcGF5bG9hZCBqdXN0IGxpa2UgdGhlIHByaW1hcnkgY2x1c3Rlci4KIyBUaGUgY29uZmlnIHNob3VsZCBtYXRjaCB0aGUgb25lIGdlbmVyYXRlZCBieSB0aGUgTUNPIGlkZWFsbHk6CiMgaHR0cHM6Ly9naXRodWIuY29tL29wZW5zaGlmdC9tYWNoaW5lLWNvbmZpZy1vcGVyYXRvci9ibG9iL2U4NjFjY2IxMmYwOWM3Yzc2OGQ1MWZkZjBhMTc4NzlmY2M5YTg3ZDUvdGVtcGxhdGVzL21hc3Rlci8wMS1tYXN0ZXItY29udGFpbmVyLXJ1bnRpbWUvX2Jhc2UvZmlsZXMvY3Jpby55YW1sCiMgQnV0IGZvciBub3cgd2UncmUganVzdCBjaGFuZ2luZyB0aGUga2V5IGJpdHM6IGltYWdlIGFuZCBjb21tYW5kLgojIFBlcmhhcHMgZG93biB0aGUgbGluZSB3ZSBjaGFuZ2UgdGhpcyB0byBydW4gc29tZXRoaW5nIGxpa2U6CiMgcG9kbWFuIHJ1biBtYWNoaW5lLWNvbmZpZy1kYWVtb24gYm9vdHN0cmFwIC4uLiAocGFzc2luZyB0aGUgcmVsZWFzZSBpbWFnZSBhbmQgdGhlIGhvc3Qgcm9vdGZzKQoKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCi4gL3Vzci9sb2NhbC9iaW4vcmVsZWFzZS1pbWFnZS5zaAoKTUFDSElORV9DT05GSUdfSU5GUkFfSU1BR0U9JChpbWFnZV9mb3IgcG9kKQoKIyBtYWtlIHRoZSBkcm9wLWluIGRpcmVjdG9yeSBpZiB0aGF0IGhhc24ndCBiZWVuIGRvbmUgeWV0Cm1rZGlyIC1wIC9ldGMvY3Jpby9jcmlvLmNvbmYuZAoKY2F0IDw8RU9GID4gL2V0Yy9jcmlvL2NyaW8uY29uZi5kLzUwLWJvb3RzdHJhcC1vdmVycmlkZS5jb25mCltjcmlvXQpbY3Jpby5ydW50aW1lXQpob29rc19kaXIgPSBbCgkiL3Vzci9zaGFyZS9jb250YWluZXJzL29jaS9ob29rcy5kIiwKCSIvZXRjL2NvbnRhaW5lcnMvb2NpL2hvb2tzLmQiLApdCltjcmlvLmltYWdlXQpwYXVzZV9pbWFnZSA9ICIkTUFDSElORV9DT05GSUdfSU5GUkFfSU1BR0UiCnBhdXNlX2NvbW1hbmQgPSAiL3Vzci9iaW4vcG9kIgpFT0YK"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/installer-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/installer-masters-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBHZXQgdGFyZ2V0IGFyY2hpdGVjdHVyZQphcmNoPSQodW5hbWUgLW0pCgppZiB0ZXN0ICJ4JHsxfSIgPSAneC0taWQnCnRoZW4KCUdBVEhFUl9JRD0iJHsyfSIKCXNoaWZ0IDIKZmkKCkFSVElGQUNUUz0iL3RtcC9hcnRpZmFjdHMtJHtHQVRIRVJfSUR9Igpta2RpciAtcCAiJHtBUlRJRkFDVFN9IgoKZWNobyAiR2F0aGVyaW5nIG1hc3RlciBzeXN0ZW1kIHN1bW1hcnkgLi4uIgpMQU5HPVBPU0lYIHN5c3RlbWN0bCBsaXN0LXVuaXRzIC0tc3RhdGU9ZmFpbGVkID4mICIke0FSVElGQUNUU30vZmFpbGVkLXVuaXRzLnR4dCIKCmVjaG8gIkdhdGhlcmluZyBtYXN0ZXIgZmFpbGVkIHN5c3RlbWQgdW5pdCBzdGF0dXMgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzIgpzZWQgLW4gJ3MvXlwqIFwoW14gXSpcKSAuKi9cMS9wJyA8ICIke0FSVElGQUNUU30vZmFpbGVkLXVuaXRzLnR4dCIgfCB3aGlsZSByZWFkIC1yIFVOSVQKZG8KICAgIHN5c3RlbWN0bCBzdGF0dXMgLS1mdWxsICIke1VOSVR9IiA+JiAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzLyR7VU5JVH0udHh0IgogICAgam91cm5hbGN0bCAtdSAiJHtVTklUfSIgPiAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzLyR7VU5JVH0ubG9nIgpkb25lCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIGpvdXJuYWxzIC4uLiIKbWtkaXIgLXAgIiR7QVJUSUZBQ1RTfS9qb3VybmFscyIKZm9yIHNlcnZpY2UgaW4gY3JpbyBrdWJlbGV0IG1hY2hpbmUtY29uZmlnLWRhZW1vbi1ob3N0IG1hY2hpbmUtY29uZmlnLWRhZW1vbi1maXJzdGJvb3Qgb3BlbnNoaWZ0LWF6dXJlLXJvdXRlcyBvcGVuc2hpZnQtZ2NwLXJvdXRlcyBwaXZvdCBzc3NkCmRvCiAgICBqb3VybmFsY3RsIC0tYm9vdCAtLW5vLXBhZ2VyIC0tb3V0cHV0PXNob3J0IC0tdW5pdD0iJHtzZXJ2aWNlfSIgPiAiJHtBUlRJRkFDVFN9L2pvdXJuYWxzLyR7c2VydmljZX0ubG9nIgpkb25lCgpqb3VybmFsY3RsIC1vIHdpdGgtdW5pdCAtLW5vLXBhZ2VyIHwgZ3ppcCA+ICIke0FSVElGQUNUU30vam91cm5hbHMvam91cm5hbC5sb2cuZ3oiCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIG5ldHdvcmtpbmcgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L25ldHdvcmsiCmlwIGFkZHIgPiYgIiR7QVJUSUZBQ1RTfS9uZXR3b3JrL2lwLWFkZHIudHh0IgppcCByb3V0ZSA+JiAiJHtBUlRJRkFDVFN9L25ldHdvcmsvaXAtcm91dGUudHh0Igpob3N0bmFtZSA+JiAiJHtBUlRJRkFDVFN9L25ldHdvcmsvaG9zdG5hbWUudHh0IgpuZXRzdGF0IC1hbnAgPiYgIiR7QVJUSUZBQ1RTfS9uZXR3b3JrL25ldHN0YXQudHh0IgpjcCAtciAvZXRjL3Jlc29sdi5jb25mICIke0FSVElGQUNUU30vbmV0d29yay8iCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIGNvbnRhaW5lcnMgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMiCmZvciBjb250YWluZXIgaW4gJChjcmljdGwgcHMgLS1hbGwgLS1xdWlldCkKZG8KICAgIGNvbnRhaW5lcl9uYW1lPSQoY3JpY3RsIHBzIC1hIC0taWQgIiR7Y29udGFpbmVyfSIgLXYgfCBncmVwIC1vUCAiTmFtZTogXFxLKC4qKSIpCiAgICBjcmljdGwgbG9ncyAiJHtjb250YWluZXJ9IiA+JiAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMvJHtjb250YWluZXJfbmFtZX0tJHtjb250YWluZXJ9LmxvZyIKICAgIGNyaWN0bCBpbnNwZWN0ICIke2NvbnRhaW5lcn0iID4mICIke0FSVElGQUNUU30vY29udGFpbmVycy8ke2NvbnRhaW5lcl9uYW1lfS0ke2NvbnRhaW5lcn0uaW5zcGVjdCIKZG9uZQoKcG9kbWFuIHBzIC0tYWxsIC0tZm9ybWF0ICJ7eyAuSUQgfX0ge3sgLk5hbWVzIH19IiB8IHdoaWxlIHJlYWQgLXIgY29udGFpbmVyX2lkIGNvbnRhaW5lcl9uYW1lCmRvCiAgICBwb2RtYW4gbG9ncyAiJHtjb250YWluZXJfaWR9IiA+JiAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMvJHtjb250YWluZXJfbmFtZX0tJHtjb250YWluZXJfaWR9LmxvZyIKICAgIHBvZG1hbiBpbnNwZWN0ICIke2NvbnRhaW5lcl9pZH0iID4mICIke0FSVElGQUNUU30vY29udGFpbmVycy8ke2NvbnRhaW5lcl9uYW1lfS0ke2NvbnRhaW5lcl9pZH0uaW5zcGVjdCIKZG9uZQoKZWNobyAiR2F0aGVyaW5nIG1hc3RlciBycG0tb3N0cmVlIGluZm8gLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L3JwbS1vc3RyZWUiCnN1ZG8gcnBtLW9zdHJlZSBzdGF0dXMgPiYgIiR7QVJUSUZBQ1RTfS9ycG0tb3N0cmVlL3N0YXR1cyIKc3VkbyBycG0tb3N0cmVlIGV4IGhpc3RvcnkgPiYgIiR7QVJUSUZBQ1RTfS9ycG0tb3N0cmVlL2hpc3RvcnkiCgojIENvbGxlY3Qgc3lzdGVtIGluZm9ybWF0aW9uIHNwZWNpZmljIHRvIElCTSBMaW51eCBaIChzMzkweCkgc3lzdGVtcy4gVGhlIGRiZ2luZm8KIyBzY3JpcHQgaXMgYXZhaWxhYmxlIGJ5IGRlZmF1bHQgYXMgcGFydCBvZiB0aGUgczM5MC11dGlscyBycG0gcGFja2FnZQppZiBbICIkYXJjaCIgPT0gInMzOTB4IiBdCnRoZW4KICAgIGVjaG8gIkdhdGhlcmluZyBkYmdpbmZvIGZvciB0aGUgczM5MHggc3lzdGVtIgogICAgbWtkaXIgLXAgIiR7QVJUSUZBQ1RTfS9ub2RlLWRiZ2luZm8iCiAgICAvdXNyL3NiaW4vZGJnaW5mby5zaCAtZCAiJHtBUlRJRkFDVFN9L25vZGUtZGJnaW5mbyIKICAgIGZpbmQgIiR7QVJUSUZBQ1RTfS9ub2RlLWRiZ2luZm8iIC1wcmludDAgfCB4YXJncyAtMCBjaG1vZCBhK3IKZmkKCmVjaG8gIldhaXRpbmcgZm9yIGxvZ3MgLi4uIgp3aGlsZSB3YWl0IC1uOyBkbyBqb2JzOyBkb25lCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/kubelet-pause-image.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1byBwaXBlZmFpbAojIEJlZm9yZSBrdWJlbGV0LnNlcnZpY2UgYW5kIGNyaW8uc2VydmljZSBzdGFydCwgZW5zdXJlCiMgdGhhdCB3ZSdyZSB1c2luZyB0aGUgcGF1c2UgaW1hZ2UgZnJvbSBvdXIgcGF5bG9hZCBqdXN0IGxpa2UgdGhlIHByaW1hcnkgY2x1c3Rlci4KIyBOZWVkIHRvIHNldCB0aGUgLS1wb2QtaW5mcmEtY29udGFpbmVyLWltYWdlIGZsYWcgZm9yIHRoZSBrdWJlbGV0IHRvIHBvaW50IHRvIHRoZSBwYXVzZSBpbWFnZSBmcm9tIHRoZSBwYXlsb2FkCiMgU28gd2UgYWRkIE1BQ0hJTkVfQ09ORklHX0lORlJBX0lNQUdFIHRvIGFuIGVudmlyb25tZW50IGZpbGUgYW5kIHNvdXJjZSB0aGF0IGluIHRoZSBrdWJlbGV0IHNlcnZpY2UKClBSRV9DT01NQU5EPSJrdWJlbGV0LXBhdXNlLWltYWdlIgouIC91c3IvbG9jYWwvYmluL2Jvb3RzdHJhcC1zZXJ2aWNlLXJlY29yZC5zaAoKLiAvdXNyL2xvY2FsL2Jpbi9yZWxlYXNlLWltYWdlLnNoCgplY2hvICJNQUNISU5FX0NPTkZJR19JTkZSQV9JTUFHRT0kKGltYWdlX2ZvciBwb2QpIiA+IC9ldGMva3ViZXJuZXRlcy9rdWJlbGV0LXBhdXNlLWltYWdlLW92ZXJyaWRlCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/kubelet.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIHNvdXJjZT1ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCiAvdXNyL2Jpbi9rdWJlbGV0IFwKICAgIC0tYW5vbnltb3VzLWF1dGg9ZmFsc2UgXAogICAgLS1jb250YWluZXItcnVudGltZS1lbmRwb2ludD0vdmFyL3J1bi9jcmlvL2NyaW8uc29jayBcCiAgICAtLXJ1bnRpbWUtcmVxdWVzdC10aW1lb3V0PSIke0tVQkVMRVRfUlVOVElNRV9SRVFVRVNUX1RJTUVPVVR9IiBcCiAgICAtLXBvZC1tYW5pZmVzdC1wYXRoPS9ldGMva3ViZXJuZXRlcy9tYW5pZmVzdHMgXAogICAgLS1taW5pbXVtLWNvbnRhaW5lci10dGwtZHVyYXRpb249Nm0wcyBcCiAgICAtLWNsdXN0ZXItZG9tYWluPWNsdXN0ZXIubG9jYWwgXAogICAgLS1jZ3JvdXAtZHJpdmVyPXN5c3RlbWQgXAogICAgLS1zZXJpYWxpemUtaW1hZ2UtcHVsbHM9ZmFsc2UgXAogICAgLS12PTIgXAogICAgLS12b2x1bWUtcGx1Z2luLWRpcj0vZXRjL2t1YmVybmV0ZXMva3ViZWxldC1wbHVnaW5zL3ZvbHVtZS9leGVjIFwKICAgIC0tcG9kLWluZnJhLWNvbnRhaW5lci1pbWFnZT0iJHtNQUNISU5FX0NPTkZJR19JTkZSQV9JTUFHRX0iCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/release-image-download.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/release-image.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAojIFRoaXMgbGlicmFyeSBwcm92aWRlcyBhbiBgaW1hZ2VfZm9yYCBoZWxwZXIgZnVuY3Rpb24gd2hpY2ggY2FuIGdldCB0aGUKIyBwdWxsIHNwZWMgZm9yIGEgc3BlY2lmaWMgaW1hZ2UgaW4gYSByZWxlYXNlLgoKIyBDb252ZXJ0IHRoZSByZWxlYXNlIGltYWdlIHB1bGwgc3BlYyB0byBhbiAiYWJzb2x1dGUiIGZvcm0gaWYgYSBkaWdlc3QgaXMgYXZhaWxhYmxlCmlmICEgUkVMRUFTRV9JTUFHRV9ESUdFU1Q9JCggcG9kbWFuIGluc3BlY3QgcXVheS5pby9vcGVuc2hpZnQtcmVsZWFzZS1kZXYvb2NwLXJlbGVhc2VAc2hhMjU2OmZiYWQ5MzFjNzI1YjJlNWI5MzdiMjk1YjU4MzQ1MzM0MzIyYmRhYmIwYjY3ZGExYzgwMGE1MzY4NmQ3Mzk3ZGEgLS1mb3JtYXQgJ3t7IGluZGV4IC5SZXBvRGlnZXN0cyAwIH19JyApIHx8IFtbIC16ICIke1JFTEVBU0VfSU1BR0VfRElHRVNUfSIgXV07IHRoZW4KCWVjaG8gIldhcm5pbmc6IENvdWxkIG5vdCByZXNvbHZlIHJlbGVhc2UgaW1hZ2UgdG8gcHVsbCBieSBkaWdlc3QiIDI+JjEKCVJFTEVBU0VfSU1BR0VfRElHRVNUPSJxdWF5LmlvL29wZW5zaGlmdC1yZWxlYXNlLWRldi9vY3AtcmVsZWFzZUBzaGEyNTY6ZmJhZDkzMWM3MjViMmU1YjkzN2IyOTViNTgzNDUzMzQzMjJiZGFiYjBiNjdkYTFjODAwYTUzNjg2ZDczOTdkYSIKZmkKCmltYWdlX2ZvcigpIHsKICAgIHBvZG1hbiBydW4gLS1xdWlldCAtLXJtIC0tbmV0PW5vbmUgIiR7UkVMRUFTRV9JTUFHRV9ESUdFU1R9IiBpbWFnZSAiJHsxfSIKfQo="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/report-progress.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MxMDkxICAjIHVzaW5nIHBhdGggb24gYm9vdHN0cmFwIG1hY2hpbmUKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCiMgc2hlbGxjaGVjayBkaXNhYmxlPVNDMTA5MSAgIyB1c2luZyBwYXRoIG9uIGJvb3RzdHJhcCBtYWNoaW5lCi4gL3Vzci9sb2NhbC9iaW4vd2FpdC1mb3ItaGEtYXBpLnNoCgpLVUJFQ09ORklHPSIkezF9IgoKd2FpdF9mb3JfZXhpc3RlbmNlKCkgewoJd2hpbGUgWyAhIC1lICIkezF9IiBdCglkbwoJCXNsZWVwIDUKCWRvbmUKfQoKcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3RhcnQgIndhaXQtZm9yLWJvb3RzdHJhcC1jb21wbGV0ZSIKZWNobyAiV2FpdGluZyBmb3IgYm9vdHN0cmFwIHRvIGNvbXBsZXRlLi4uIgp3YWl0X2Zvcl9leGlzdGVuY2UgL29wdC9vcGVuc2hpZnQvLmJvb3RrdWJlLmRvbmUKcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3VjY2VzcwoKIyMgd2FpdCBmb3IgQVBJIHRvIGJlIGF2YWlsYWJsZQp3YWl0X2Zvcl9oYV9hcGkKCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N0YXJ0ICJyZXBvcnQtYm9vdHN0cmFwLWNvbXBsZXRlIgplY2hvICJSZXBvcnRpbmcgaW5zdGFsbCBwcm9ncmVzcy4uLiIKd2hpbGUgISBvYyAtLWt1YmVjb25maWc9IiRLVUJFQ09ORklHIiBjcmVhdGUgLWYgLSA8PC1FT0YKCWFwaVZlcnNpb246IHYxCglraW5kOiBDb25maWdNYXAKCW1ldGFkYXRhOgoJICBuYW1lOiBib290c3RyYXAKCSAgbmFtZXNwYWNlOiBrdWJlLXN5c3RlbQoJZGF0YToKCSAgc3RhdHVzOiBjb21wbGV0ZQpFT0YKZG8KCXNsZWVwIDUKZG9uZQpyZWNvcmRfc2VydmljZV9zdGFnZV9zdWNjZXNzCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/wait-for-ha-api.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-dns-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBETlMKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBjbHVzdGVyCnNwZWM6CiAgYmFzZURvbWFpbjogc25vLmxhYi5sb2NhbAogIHBsYXRmb3JtOgogICAgYXdzOiBudWxsCiAgICB0eXBlOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-infrastructure-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBJbmZyYXN0cnVjdHVyZQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoKICBjbG91ZENvbmZpZzoKICAgIG5hbWU6ICIiCiAgcGxhdGZvcm1TcGVjOgogICAgdHlwZTogTm9uZQpzdGF0dXM6CiAgYXBpU2VydmVySW50ZXJuYWxVUkk6IGh0dHBzOi8vYXBpLWludC5zbm8ubGFiLmxvY2FsOjY0NDMKICBhcGlTZXJ2ZXJVUkw6IGh0dHBzOi8vYXBpLnNuby5sYWIubG9jYWw6NjQ0MwogIGNvbnRyb2xQbGFuZVRvcG9sb2d5OiBTaW5nbGVSZXBsaWNhCiAgY3B1UGFydGl0aW9uaW5nOiBOb25lCiAgZXRjZERpc2NvdmVyeURvbWFpbjogIiIKICBpbmZyYXN0cnVjdHVyZU5hbWU6IHNuby14OGRuNQogIGluZnJhc3RydWN0dXJlVG9wb2xvZ3k6IFNpbmdsZVJlcGxpY2EKICBwbGF0Zm9ybTogTm9uZQogIHBsYXRmb3JtU3RhdHVzOgogICAgdHlwZTogTm9uZQo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-ingress-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBJbmdyZXNzCm1ldGFkYXRhOgogIGNyZWF0aW9uVGltZXN0YW1wOiBudWxsCiAgbmFtZTogY2x1c3RlcgpzcGVjOgogIGRvbWFpbjogYXBwcy5zbm8ubGFiLmxvY2FsCiAgbG9hZEJhbGFuY2VyOgogICAgcGxhdGZvcm06CiAgICAgIHR5cGU6ICIiCnN0YXR1czoKICBkZWZhdWx0UGxhY2VtZW50OiBDb250cm9sUGxhbmUK"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-network-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-proxy-01-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBQcm94eQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoKICB0cnVzdGVkQ0E6CiAgICBuYW1lOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-scheduler-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBTY2hlZHVsZXIKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBjbHVzdGVyCnNwZWM6CiAgbWFzdGVyc1NjaGVkdWxhYmxlOiB0cnVlCiAgcG9saWN5OgogICAgbmFtZTogIiIKICBwcm9maWxlQ3VzdG9taXphdGlvbnM6CiAgICBkeW5hbWljUmVzb3VyY2VBbGxvY2F0aW9uOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cvo-overrides.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/kube-cloud-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKa2luZDogU2VjcmV0Cm1ldGFkYXRhOgogIG5hbWU6IGt1YmUtY2xvdWQtY2ZnCiAgbmFtZXNwYWNlOiBrdWJlLXN5c3RlbQp0eXBlOiBPcGFxdWUKZGF0YToKICBjb25maWc6ICIiCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/kube-system-configmap-root-ca.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKa2luZDogQ29uZmlnTWFwCm1ldGFkYXRhOgogIG5hbWU6IHJvb3QtY2EKICBuYW1lc3BhY2U6IGt1YmUtc3lzdGVtCmRhdGE6CiAgY2EuY3J0OiB8CiAgICAtLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KICAgIE1JSURFRENDQWZpZ0F3SUJBZ0lJWSs5bVpjcWJHUzB3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKICAgIEN4TUpiM0JsYm5Ob2FXWjBNUkF3RGdZRFZRUURFd2R5YjI5MExXTmhNQjRYRFRJMU1EWXhNREl4TXpReE5sb1gKICAgIERUTTFNRFl3T0RJeE16UXhObG93SmpFU01CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHkKICAgIGIyOTBMV05oTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1S3RMVVYxZUh4MmkKICAgIEd4eWNHZ0pHWHhiN2t0NGtqTGJjdVlPMStOdFUyTy9jSVJIVTZWdE5SUkwxSEd4TzBuVHlhTGNGQXJlNGJtVHQKICAgICtXQ3h1cFVxbDBsT3FvV09PSUFzZGRLQzQ2ZlB1N3NkY0hGSnlIcXlQWVE2cW1uMm9LdFN5MUJtQWQ0OCtaYTMKICAgIHdWTWFvQUU1MS9zeXB2YnJQKy9xRVZaS3BMemk4c2xrWUhhT2lmdzgwSCs0NVlMcmR1ZGRMTmxVV21tTHdDYVoKICAgIHFiL0lQNE9OUnhXTEVDUm5WMDR6dEIxcTZBY0ViMytuRVFZVEI4a01PVjc2bFZBcVVPRFJFL3JOdUhaSjVrTjUKICAgIHBzelh4c3RMVEMvcjhIdTMvRkJ5SCtyZXg5a0dYMUFtbUxBR2Q1aUI3cUswb0ZVZUFWNzB6cFpkbjVqaklvVWoKICAgIEo0R0dxdUx4UHdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRFZSMFRBUUgvQkFVd0F3RUIKICAgIC96QWRCZ05WSFE0RUZnUVVJbGtaVDU5N1lTOVdsZzRpVXlxVllsU053TTB3RFFZSktvWklodmNOQVFFTEJRQUQKICAgIGdnRUJBR0E2alNSSDBsSDBYTGwxVk9oeE1ld0pKT015cGh1TFRRd2xtMXluNDZLM2d4YXFOUFNPVFYzR2FZMEsKICAgIGVNSmpxL2NBU3dGeklHL25BY010bjFybXN6QzJJbmlid3VLdW4yL2VnbXpRQzhyL2NicGNRTDFraVpoOU5GUnAKICAgIHdBdkdWeXFRRWxkemVmc2NtN2E3dktEMUlMQnNzZXFBTjJBSHpjWmM1TFdyWC9RdTRrdmgzNVJ1dW55alR6TUYKICAgIFVUNUZEZ2gzTHZQUjBtZldmekg3SkY4ZHlFckZZUE8rLy9XMnc2Q29sRkI2RVhVakgrSjBpQkxlNHZCS3R0NGUKICAgIGNsOGxBRjNxVVhrY05kQlFweGdSVlBxV1NoaUY5RHFTU1BrWXdLTE81YXVvR1VyUzJXODNybzU1MThkUHV3MmcKICAgIGszR2RXVFZrMWF6ZGhnQ3Rua0NyL2Y3dzBHdz0KICAgIC0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0KICAgIAo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/machine-config-server-tls-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKa2luZDogU2VjcmV0Cm1ldGFkYXRhOgogIG5hbWU6IG1hY2hpbmUtY29uZmlnLXNlcnZlci10bHMKICBuYW1lc3BhY2U6IG9wZW5zaGlmdC1tYWNoaW5lLWNvbmZpZy1vcGVyYXRvcgogIGFubm90YXRpb25zOgogICAgb3BlbnNoaWZ0LmlvL293bmluZy1jb21wb25lbnQ6ICJNYWNoaW5lIENvbmZpZyBPcGVyYXRvciIKdHlwZTogT3BhcXVlCmRhdGE6CiAgdGxzLmNydDogTFMwdExTMUNSVWRKVGlCRFJWSlVTVVpKUTBGVVJTMHRMUzB0Q2sxSlNVUlhSRU5EUVd0RFowRjNTVUpCWjBsSllXb3pTR1V6ZURWT1drRjNSRkZaU2t0dldrbG9kbU5PUVZGRlRFSlJRWGRLYWtWVFRVSkJSMEV4VlVVS1EzaE5TbUl6UW14aWJrNXZZVmRhTUUxU1FYZEVaMWxFVmxGUlJFVjNaSGxpTWprd1RGZE9hRTFDTkZoRVZFa3hUVVJaZUUxRVNYaE5lbEY0VG14dldBcEVWRTB4VFVSWmVFMUVTWGhOYW1ONVRXeHZkMHA2Uld4TlEwMUhRVEZWUlVGNFRXTmpNMng2WkVkV2RFOXRNV2haTW1od1ltMVZkRmt5T1hWYWJXeHVDa3hZVG14amJscHNZMnBEUTBGVFNYZEVVVmxLUzI5YVNXaDJZMDVCVVVWQ1FsRkJSR2RuUlZCQlJFTkRRVkZ2UTJkblJVSkJURE53TVN0aWVFZzBSMmNLYUcxU2N6Sk1NRkpGZVVwNGNVUXhValZ3TjJSRWRIazJibUpvVGpOU1p6UTRRelJqYjJ0T1ZWaEhTVUZXWmtkRmVVRjJlREUyYUhwMFlpODFWR2x1UndwMGVHRk9Samc0Um1sMFFYaFZWVFpTT1V4WVIzQkNkV01yTldWQ1JGa3JOVTVsUjFSVVpteG1TV04yU1d0NFF6RnNORWxDVlVGS1FtNHhiaXRYYWxGMUNtNDJTM0IwYUdkTVdtcHFTbkp2UjA5b1ltTndTbmRHYjBabWRqQlZVR2ROTjNWaFdrZzJUbGhYWmpBMksyOVBMMGswWml0MWNEYzVOMnBHUjBSUVZHRUtNbGhKTVVkbFJXZENXREIzYlZSU1kyOVRURk5UTnpKMVZqTk5kRFl4WkVoRFpucFdORVo0WW5SVmVYaHdPVXRzZDBKR01ERjJabFpqU1c1YVpWSjZZZ3BGY0RCRFMzZDRTR0poUlc1blR6RkpZMGRQVm5wRVNsWktaMFk0YUdkRFkxVXhaRzUwU1hobU1YbE1NMlJCV0ZnNVIwUktZMnBuTDFsaU1VNHllV05SQ2tkeFVIcHFWVWxxWml0elEwRjNSVUZCWVU5Q2FVUkRRbWhVUVZSQ1owNVdTRk5WUlVSRVFVdENaMmR5UW1kRlJrSlJZMFJCVkVGTlFtZE9Wa2hTVFVJS1FXWTRSVUZxUVVGTlFqQkhRVEZWWkVSblVWZENRbFJUWm1GMVJsa3pOREpTYVhwWE0yTktNakJ6TjNoM1F6aEtUMnBCWmtKblRsWklVMDFGUjBSQlZ3cG5RbEZwVjFKc1VHNHpkR2hNTVdGWFJHbEtWRXR3Vm1sV1NUTkJlbFJCWjBKblRsWklVa1ZGUjFSQldHZG9WbWhqUjJ0MFlWYzFNRXh1VG5WaWVUVnpDbGxYU1hWaVJ6bHFXVmQzZDBSUldVcExiMXBKYUhaalRrRlJSVXhDVVVGRVoyZEZRa0ZEZEVOR1p5dHllbmxQYUcxTUwwdEZlRlYxUW5Cb0sxaFFZamtLY2tZemNWVkRZMk5NTVZWbVdUWkRkRU40YmtWRFJDdENTbkl5Wm1kTFVXcEVkMDVqY1U1d1duZ3ZTMjlSV2tOM1ptdFJRbGwyTHpaeE5HTnZUa0ZwU3dwQk0yTmlWR1o2TDBKcGVuUnZOSE4xUzBJeU0zVXZiallyYjNoVU1HaG1kR1F2TWl0eFRHZERjMWxZVWxRd01WZHZSQ3RSV0RoeVV6Sk5OM05pYWxGVENuazVORkp5TVRNek9TdG1ZakpwTXpCVVEyWnVkWFJvVG1oQldWZFRaekY1TURGdFZ6RlBRVWhOSzNWWFJXWjVXRGh4UTJObU1qaHBZVTUwVG1wa2FYY0tSbEJsTURNeVYyWnlVSEJMZVcxYWNIZzNWR1l6VW1ONk1VOXFXRWRwTlRjNGNuUlpXVXRrTnpWaFRrNTRjVkpPU0dzNFV6SjVPSFI1YlZKVVlqUlNPUXBCTTNGNGRrVlhOME4yWkRSa2MxQm9jMDl4WTFCM2NsY3dObWN2T0dWVmJIbE1WRzl6Vmt4VFIwWlBjM0EwVm5sSVRsWlBjbWRLYTBkR1VUMEtMUzB0TFMxRlRrUWdRMFZTVkVsR1NVTkJWRVV0TFMwdExRbz0KICB0bHMua2V5OiBMUzB0TFMxQ1JVZEpUaUJTVTBFZ1VGSkpWa0ZVUlNCTFJWa3RMUzB0TFFwTlNVbEZiMmRKUWtGQlMwTkJVVVZCZG1WdVdEVjJSV1puWVVOSFdrZDZXWFpTUlZSSmJrZHZVRlpJYlc1ME1FOHpUSEZrZFVVelpFZEVhbmRNYUhscENsRXhVbU5aWjBKV09GbFVTVU12U0ZoeFNFOHhkaTlzVDB0allUTkdiekJZZW5kWFN6QkVSbEpVY0Vnd2RHTmhhMGMxZWpkc05FVk9hamRyTVRSYVRrNEtLMVk0YUhrNGFWUkZURmRZWjJkR1VVRnJSMlpYWmpWaFRrTTJabTl4YlRKSFFYUnRUMDF0ZFdkWk5rWjBlV3R1UVZkblZpc3ZVbEVyUVhwMU5YQnJaZ3B2TVdSYUwxUnlObWMzT0dwb0x6WTJibll6ZFUxVldVMDVUbkphWTJwVldqUlRRVVptVkVOYVRrWjVhRWwwU2t4MllUVllZM2t6Y2xZd1kwb3ZUbGhuQ2xoR2RURlVURWR1TUhGWVFVVllWRmM1T1ZaM2FXUnNOVWhPYzFOdVVVbHlSRVZrZEc5VFpVRTNWV2gzV1RWWVRVMXNWVzFCV0hsSFFVcDRWRll5WlRBS2FrWXZXRWwyWkRCQ1pHWXdXVTFzZVU5RU9XaDJWVE5pU25oQllXOHZUMDVSYVU0dk5uZEpSRUZSUVVKQmIwbENRVU5sU0dGa2JqSTNSa1JzTkdKTk53cGxTekUxZUZaeVVTOHllWE5DUnpKcGJ6TndUVWRhU1VsSGRrZ3dkMEZHU2tWdWEycE5VWFIzSzBoTFNqWmlNbUYyUVVkcEt5OTVWMWh4SzFKWVowTkRDbTgyWTBOb01EaHZVa3d3ZFRSb2JVZ3pRbUZ1VTFSTVUzVk5hbTFwZUVkR2QwdEJZMHhuVDJ4dWRqZEhXRll2UVN0RE9WRkRNbEZITjJreVdWZHlMMWdLZUZWQmFEa3dabmR4Y2t4eGQwZG9UVlkwVFZOalEyb3JVa2hCYzI1UU1ERjBUV0owS3psRlMycDFTWEpFYUhsVlEyWm1WRzFZYWxKTmVuQlBVbEpaWndwbWVWRlVha2xRTVdkd056Qk9iR3hPWjFCTVdtRXJWM0pHYzFSM05VWndkbEpXTHpZd2JIcGlUWFY2UjFnMWVHNVpaRXhVVnpjeVVHSTViRlZ1TmpGcUNtVnZZWFpzYjJ4SGJrMWhSSGxUU2tNd1IydG9TVU5RVWxCWkt6aDRjM2ROU1ZSMFV6SlFORk5oYlRKUVJWaFpVSHBNZWxKMU16ZFdkVXhyYmtOWlRGVUtNRVJ6TUZsMWEwTm5XVVZCZW5CaWRsTlFNMnBxY0dsSmVYcDBPQ3REYW5FdmRFMWtOWFJUTVhKb1MxaG5aazl2T0VWRGIzUmtkM1Z1UVRJNFdXUTRlZ3BEYmpSYVRtcFpNM2t2UkVKTmRTOUpabUk0VGpWSWIwbFZZMEY2Vm5GQlpUZDVhR1pHVjNoTldVaEZWek5NYVUxcVJqUlFXSFpVTDBwVmNERlJjMjVUQ2xkNGNHOTVNV0Z0U2tkRGRrZzJiM1JGUkdjMWMzcG1abkJzVVhwb1dEVXZZbGRSYmxsNlNsaDBkbXA0TDBZeVpVVjRla2hzVGpCRFoxbEZRVFl4V0dNS2RrSlNRbmhFTkdaa01VTm9UWHBSTDJkaVNra3ZTbTF6TkVoWVRYRTRWSFZqUzNKNGJqTnJTVzU0YmxwVGQwTnhSa05WVHpCamRrTmhWbVEyV1hZeGVBb3JjM0E1ZFV0bk5FSnZWRFV6VmtKdFZXOVFkekJaVTBaeFF5dHljRkJzU25ORFJqSTVZME5zS3paUlJEbGpVMmQ2WkhkTU4zaFFWVEZrYkZCUU9HcG1DbmxUTW5oeGNYRnFkbFZGY0c4dk0zbFlUR2xzZVhBNWRXOXBjRUpyTUc5SFdEUXlSakV5WTBObldVRllObUo1TURsRU5UZzJVV05qYW5wM09UbFhOazhLU2tveldHUkdUemRZU1dWU1JWVldRekpvYTNSUk9XeGFWWFpYTVZadlMzWXJhbmx6TWpoWVJtODBObHBsVFhZMlZXb3dibUl3UXpkU1kxbHlhSE5zVGdvMFduaDJMMGROZVROeFVra3hSWFpuYVRWeE16UlRRVkJxZG1wWWVrVnBMMHAxVm5Wa1FUaDZUWE5ZVTI5dU5HOHpTR3hDVmpONFdqUnVSVGhUU2tsNkNtUldOV2g1ZUdkQlNTOTZUM1V6T1hGemRrRm1UMUZMUW1kQlV6bGlabmszZVRka1VVdFJTVmRPWVdvMldFY3dXVlZxUVVWVFJqaGtRVVZ3VmxwU1oxQUtNbFkwUlRabkswdFVZalJqTDFVdk5tcDRibUZ4VFdaSFdUQnBja2xwYVZNck5GWkZibWg0UkdkWFZHSjNZbEJZZUVodWVqSldNVkl5U0hONVMycElTZ29yYzA1dldtVTVlbXhyUkZVdlVXZHZZbTVWWVZOVFkyeG5Wbk5tYVV0S1kzTlVPVzEzU0ZkSVdXNWtWRWhvWVZGQldXSmlVSHBqTURoeE1ITjVTVkZWQ2t4aFdIaEJiMGRCVWxSUVdsbzBZM2RZVG5FdmJpOHZhblpXUTNNNU1FTkRUa2xPVVV4b2RtMWlabll4UkUxa2NqZDVUR2RaYlhoMldGTlpTalZ6ZWtjS1ZGSjZUbk5RWXpCVGJXWkdNa2RXVjNGVFJFNUtNbU13TkdSdmQxaHdVRVZuYVdFeVpFSkVWelpRUlROTU5GWmhSMWRUS3paemVUUnBVVVJCUTIxa0t3cDROalJWTWtkT1RIZG1NM3BwVFUwcmNDODVOMEZ2WVRWVGVtaE9ibGhrTTNobE5tRjBVRkk0VTBJNFFsTkhLMjVuYTBrOUNpMHRMUzB0UlU1RUlGSlRRU0JRVWtsV1FWUkZJRXRGV1MwdExTMHRDZz09Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/openshift-config-secret-pull-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_feature-gate.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBGZWF0dXJlR2F0ZQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoge30Kc3RhdHVzOgogIGZlYXR1cmVHYXRlczogbnVsbAo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_kubeadmin-password-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKZGF0YToKICBrdWJlYWRtaW46IEpESmhKREV3SkRGVmJFaFNiMk5WZVhkMFlrSlFjMlYzU0dVdk1pNXRXVkZHWldNNGFYRkJZVGRWUjNBelJUaHFRVGhxY1hCTk0wNVJUVFpMCmtpbmQ6IFNlY3JldAptZXRhZGF0YToKICBuYW1lc3BhY2U6IGt1YmUtc3lzdGVtCiAgbmFtZToga3ViZWFkbWluCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/openshift-install-manifests.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKZGF0YToKICBpbnZva2VyOiB1c2VyCiAgdmVyc2lvbjogdjQuMTcuMApraW5kOiBDb25maWdNYXAKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBvcGVuc2hpZnQtaW5zdGFsbC1tYW5pZmVzdHMKICBuYW1lc3BhY2U6IG9wZW5zaGlmdC1jb25maWcK"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-cluster-api_master-user-data-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-machineconfig_99-master-ssh.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogbWFjaGluZWNvbmZpZ3VyYXRpb24ub3BlbnNoaWZ0LmlvL3YxCmtpbmQ6IE1hY2hpbmVDb25maWcKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBsYWJlbHM6CiAgICBtYWNoaW5lY29uZmlndXJhdGlvbi5vcGVuc2hpZnQuaW8vcm9sZTogbWFzdGVyCiAgbmFtZTogOTktbWFzdGVyLXNzaApzcGVjOgogIGJhc2VPU0V4dGVuc2lvbnNDb250YWluZXJJbWFnZTogIiIKICBjb25maWc6CiAgICBpZ25pdGlvbjoKICAgICAgdmVyc2lvbjogMy4yLjAKICAgIHBhc3N3ZDoKICAgICAgdXNlcnM6CiAgICAgIC0gbmFtZTogY29yZQogICAgICAgIHNzaEF1dGhvcml6ZWRLZXlzOgogICAgICAgIC0gfAogICAgICAgICAgc3NoLXJzYSBBQUFBQjNOemFDMXljMkVBQUFBREFRQUJBQUFDQVFDdXJnNDQ4c0QrTVpQWm81TE1UeURSUS9DZGxGRFMveDV3UkZxb0ZDZEZhdGNZaGwvMTRWeStXOVdXNit5SG80eVdZQ0l5UVFkUWFqZ2Z4eWJMSlRCM0RxOG93ZCtSNFVwRmphWkdyQXVGUEpxUllwY3BiclI5ekRpdWpnWkVkQURSWDNudVNYSnM1eDdGYk50dTdjZkkrdndTdzBGWU1PWG1PMkhUUWZQVGFMVDcxRXpUQ2NiazZ6UEF0RUtuRTVtVE5RTU5Sc29GdEpzYTJ4cC9heWpYOUZHcThDUjN1dVNnWU9FRDdVZkJ6S1hDU3NIYWlSQ3NZdHZmT0VWUktZZWw4REhFeCtNdm1lbUpqV3dkcTk1WU1vUk1HWllhVVVLNlN6M0JkS0svNHdxbWF5TUZNa0xlWnUrSGdKMzR4dmp1ZXprc1pNcHBmekViRitUL1M0Z3h3d2xlZDQwb1gyZ1cxSWxHNm11dHFuYmd1MWNUZlY5ZXp1NjhjY2ZTdWU5U1F2ZjJEeStJSnZ5ZEhPTnVXZDh4OEgxZXFSekpTeVNZTnh0T3RhLzBjTWR0WmJta0VCUjNtOTR3a3dBUysyemFvMUlXY0JYT3ovQmVpTWVCN2ZuTDdwS3VxeWJ0cHJvYnQwM0E4ZjNzSndrWWNobENmU3E1Y1RYVDJCemMvSTFCR3hucU1Sb1hrSnhLbVR3WklWLzBEdUpPWGpLVjhHOGFQVW92ZlA1UE95UzBQbXNoVkl0MnNyVkxIWjJlSW5yY0ppbFB6Y2t4Vk44WFlNMnN5VXgwcDNlVlVzMzA4RTdEdG5SWmdQRi83SmIrR0tMcnlmS1ZpbjVFZ0FrTEkxNXBPS1BmczRhalhlclYrL0ljdFhuUkZGRWdnemUvMVVSNU9uakdBMUdkWXc9PSBlZHVhcmRvQE1hY0Jvb2stUHJvCiAgZXh0ZW5zaW9uczogbnVsbAogIGZpcHM6IGZhbHNlCiAga2VybmVsQXJndW1lbnRzOiBudWxsCiAga2VybmVsVHlwZTogIiIKICBvc0ltYWdlVVJMOiAiIgo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-cluster-api_worker-user-data-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-machineconfig_99-worker-ssh.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogbWFjaGluZWNvbmZpZ3VyYXRpb24ub3BlbnNoaWZ0LmlvL3YxCmtpbmQ6IE1hY2hpbmVDb25maWcKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBsYWJlbHM6CiAgICBtYWNoaW5lY29uZmlndXJhdGlvbi5vcGVuc2hpZnQuaW8vcm9sZTogd29ya2VyCiAgbmFtZTogOTktd29ya2VyLXNzaApzcGVjOgogIGJhc2VPU0V4dGVuc2lvbnNDb250YWluZXJJbWFnZTogIiIKICBjb25maWc6CiAgICBpZ25pdGlvbjoKICAgICAgdmVyc2lvbjogMy4yLjAKICAgIHBhc3N3ZDoKICAgICAgdXNlcnM6CiAgICAgIC0gbmFtZTogY29yZQogICAgICAgIHNzaEF1dGhvcml6ZWRLZXlzOgogICAgICAgIC0gfAogICAgICAgICAgc3NoLXJzYSBBQUFBQjNOemFDMXljMkVBQUFBREFRQUJBQUFDQVFDdXJnNDQ4c0QrTVpQWm81TE1UeURSUS9DZGxGRFMveDV3UkZxb0ZDZEZhdGNZaGwvMTRWeStXOVdXNit5SG80eVdZQ0l5UVFkUWFqZ2Z4eWJMSlRCM0RxOG93ZCtSNFVwRmphWkdyQXVGUEpxUllwY3BiclI5ekRpdWpnWkVkQURSWDNudVNYSnM1eDdGYk50dTdjZkkrdndTdzBGWU1PWG1PMkhUUWZQVGFMVDcxRXpUQ2NiazZ6UEF0RUtuRTVtVE5RTU5Sc29GdEpzYTJ4cC9heWpYOUZHcThDUjN1dVNnWU9FRDdVZkJ6S1hDU3NIYWlSQ3NZdHZmT0VWUktZZWw4REhFeCtNdm1lbUpqV3dkcTk1WU1vUk1HWllhVVVLNlN6M0JkS0svNHdxbWF5TUZNa0xlWnUrSGdKMzR4dmp1ZXprc1pNcHBmekViRitUL1M0Z3h3d2xlZDQwb1gyZ1cxSWxHNm11dHFuYmd1MWNUZlY5ZXp1NjhjY2ZTdWU5U1F2ZjJEeStJSnZ5ZEhPTnVXZDh4OEgxZXFSekpTeVNZTnh0T3RhLzBjTWR0WmJta0VCUjNtOTR3a3dBUysyemFvMUlXY0JYT3ovQmVpTWVCN2ZuTDdwS3VxeWJ0cHJvYnQwM0E4ZjNzSndrWWNobENmU3E1Y1RYVDJCemMvSTFCR3hucU1Sb1hrSnhLbVR3WklWLzBEdUpPWGpLVjhHOGFQVW92ZlA1UE95UzBQbXNoVkl0MnNyVkxIWjJlSW5yY0ppbFB6Y2t4Vk44WFlNMnN5VXgwcDNlVlVzMzA4RTdEdG5SWmdQRi83SmIrR0tMcnlmS1ZpbjVFZ0FrTEkxNXBPS1BmczRhalhlclYrL0ljdFhuUkZGRWdnemUvMVVSNU9uakdBMUdkWXc9PSBlZHVhcmRvQE1hY0Jvb2stUHJvCiAgZXh0ZW5zaW9uczogbnVsbAogIGZpcHM6IGZhbHNlCiAga2VybmVsQXJndW1lbnRzOiBudWxsCiAga2VybmVsVHlwZTogIiIKICBvc0ltYWdlVVJMOiAiIgo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/original_cvo_overrides.patch", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,eyJzcGVjIjp7Im92ZXJyaWRlcyI6bnVsbH19"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig-kubelet", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,Y2x1c3RlcnM6Ci0gY2x1c3RlcjoKICAgIGNlcnRpZmljYXRlLWF1dGhvcml0eS1kYXRhOiBMUzB0TFMxQ1JVZEpUaUJEUlZKVVNVWkpRMEZVUlMwdExTMHRDazFKU1VSUlJFTkRRV2xwWjBGM1NVSkJaMGxKVVdkeFJIbDVabTFPTW5OM1JGRlpTa3R2V2tsb2RtTk9RVkZGVEVKUlFYZFFha1ZUVFVKQlIwRXhWVVVLUTNoTlNtSXpRbXhpYms1dllWZGFNRTFUWjNkS1oxbEVWbEZSUkVWNE9YSmtWMHBzVEZkR2QyRllUbXhqYmxwc1kya3hjMkl5VG1oaVIyaDJZek5SZEFwak1teHVZbTFXZVUxQ05GaEVWRWt4VFVSWmVFMUVTWGhOZWxGNFRsWnZXRVJVVFRGTlJGbDNUMFJKZUUxNlVYaE9WbTkzVUdwRlUwMUNRVWRCTVZWRkNrTjRUVXBpTTBKc1ltNU9iMkZYV2pCTlUyZDNTbWRaUkZaUlVVUkZlRGx5WkZkS2JFeFhSbmRoV0U1c1kyNWFiR05wTVhOaU1rNW9Za2RvZG1NelVYUUtZekpzYm1KdFZubE5TVWxDU1dwQlRrSm5hM0ZvYTJsSE9YY3dRa0ZSUlVaQlFVOURRVkU0UVUxSlNVSkRaMHREUVZGRlFUQm5VV1F2T0hvM1JYbGlOQW80WVhSS2FYQlZjV2RvV1hvek5VOUlZUzg1YVVsck9YUXJlR3QxWjFwQlIwMXVheXRZVmt0dlNITk9NelJCSzFaUmFVVmlkblUwU1ZkNFJ6TnNNMDlzQ2xSTVFYRlBhREZGTUN0bWFqQkRUWFpCYkhaallXSmtReTlHTTI1b1FsRktRMmd2WVN0TU1VVXhaalF3V25sc1JFb3ZlSE00T1VaRVFWVlBhbmxITmpjS2VsZHNVRkpxZEVGRE1qSjBjMEowUzB4cFdraFBOMlY2WWpFd2VUTklibW8xU0hRNVJYSkNNbEZSUmpCdlZUVkdNR3BEYjNoaGNURnhXWFZqUkZBM1F3b3ZSR2hhTlRGQllWVkJlbkUxTm1jeE5XNWtjVEY1ZVZWTk4zaE9VVTlCYWxvdllYY3dSa3RYWWs1U2JtWTJibTU2YTFOMFV6STNXRFZETUhad2NsWmpDbUZRVjBOT1dXSkxjbmt6ZWtsNWExSmFWVnBPVmk5UWFuaEhNSE0xWVZvd1ZtNTZOemxYZGl0eGFuUTJabkZUVnpGeVNHOUtMMHN4Y0dKV1J6RXpPVXdLVW1GTGQycHJUV1YxVVVsRVFWRkJRbTh3U1hkUlJFRlBRbWRPVmtoUk9FSkJaamhGUWtGTlEwRnhVWGRFZDFsRVZsSXdWRUZSU0M5Q1FWVjNRWGRGUWdvdmVrRmtRbWRPVmtoUk5FVkdaMUZWVFVNclkxbzVUazVGTkRCbVJGcDBXRVpyZW5sV2FWWXhUVWxWZDBSUldVcExiMXBKYUhaalRrRlJSVXhDVVVGRUNtZG5SVUpCUkdOYWMxSk1jbnAxTmtwQ2FrVTRja0p4VG1sb01rZzBUelYxVDFOV2JYRkpUR0k1UzJNMk1FbFVTazQyUkROMlFVVmFWekZpVVhFM05Fd0tjekozUlhKWWMzWk9TRVp1VldscmFFNXJWVWd5YldOV2RsWnBVaTlrVW10dWRYQmpZamh1V0RGcGNqQTJlRkp0TTJWWGNrdG9XazFwU1d0QmRtbHBlQW80TkhZMVpsZDFUVVp0TldwVVIyWmthR0p3UWs5VU5tTllUbWR4TlVwalJHMWliMmhoZEhsT1ZuWjNVWFpFUVhWQ01HdHJSbmhMYlZwWVRYWjRjVEEyQ21SWWJtdGpVSGRrV0dkNFJqVTROV1YzTlM5aVJXRk5ZalE1ZDFOYUwwbENaVk5vVlZseGRsSkdPWGRyYWs5SlQwbEVOalp5TW1GdWRtZEVkVUowUWtRS2IydFpjMnhXU1ZReGVuZFpRbU41WWpRMVJuSkxSVkZzYkVGdFNuSjRXR1JyZEVsemIzYzBWMHRUVGs1TE4yVTNjbTlIV0hWamRWTmplR0p2WlhGVFN3cEtiRlIxYW1seWMxVlRPVk56YUc1a2MzWTVPWGxRVjA4NFZqZzlDaTB0TFMwdFJVNUVJRU5GVWxSSlJrbERRVlJGTFMwdExTMEtMUzB0TFMxQ1JVZEpUaUJEUlZKVVNVWkpRMEZVUlMwdExTMHRDazFKU1VSVVJFTkRRV3BUWjBGM1NVSkJaMGxKUm1Wd1NqRjBabHBuWmpoM1JGRlpTa3R2V2tsb2RtTk9RVkZGVEVKUlFYZFNSRVZUVFVKQlIwRXhWVVVLUTNoTlNtSXpRbXhpYms1dllWZGFNRTFUTkhkTVFWbEVWbEZSUkVWNVZuSmtWMHBzVEZkR2QyRllUbXhqYmxwc1kya3hlbHBZU2pKaFYwNXNURmMxYkFwa1NHUjJZMjF6ZEdNeWJHNWliVlo1VFVJMFdFUlVTVEZOUkZsNFRVUkplRTE2VVhoT1ZtOVlSRlJOTVUxRVdYZFBSRWw0VFhwUmVFNVdiM2RTUkVWVENrMUNRVWRCTVZWRlEzaE5TbUl6UW14aWJrNXZZVmRhTUUxVE5IZE1RVmxFVmxGUlJFVjVWbkprVjBwc1RGZEdkMkZZVG14amJscHNZMmt4ZWxwWVNqSUtZVmRPYkV4WE5XeGtTR1IyWTIxemRHTXliRzVpYlZaNVRVbEpRa2xxUVU1Q1oydHhhR3RwUnpsM01FSkJVVVZHUVVGUFEwRlJPRUZOU1VsQ1EyZExRd3BCVVVWQmQwMUphRTlXZUVreE4wcFRjMDFFTjJ0UE0zVlVWRzFEWVhGeUswazJhV2x4YTBsSk5tdDZTVmhtVTNGa1ExUnRXV2hFYm10T2QzRTRUMWhGQ2xWclFrbHlabkpFTVRkaVJrZFFaRVJVYm1WME16VkdWVmRHV1VGcFdGWldabGN6YW1aMGNuUXphRmxSTDA5MmJVTnVLM0JYWTJ4UVMzVXZTVlZYUmxvS1YxSXZXazEyYzFONVlVUkdhV2w1VmpaRFZGVjFkbEZvTWl0aE0yMXJURGsyT0VZeU5EbDRhRFZWU2tnNGFGWmlZMGd3TldReFdVVjBkRk5xYkZoWE9BbzRORk5EVG5wclIySkNhV1JIVFZabGRrZG1kVll4VmxoUVpsVXdOVVJOU21SRldUVkZWVFJZYWtVeFNVUnNVWEIxV0hSUFFWZHpiamhST0VkTWVuZDZDbUZQYUVzclVrZE1SbE5zVkdVdk1qSXpTM0ZpWnpKMk5HY3hZbkpQUVZoYWRWcElOVU4zWXpKVFlWcDVNVk50WkhoRU1XeDFNSFZHYTNCR1RWZFJOemNLVmpSU2EzbzNhbFpITWt0QmVUTmllbUZ2T0ZkcVoybGthVkZKUkVGUlFVSnZNRWwzVVVSQlQwSm5UbFpJVVRoQ1FXWTRSVUpCVFVOQmNWRjNSSGRaUkFwV1VqQlVRVkZJTDBKQlZYZEJkMFZDTDNwQlpFSm5UbFpJVVRSRlJtZFJWWGhoVDBKSloyTXhialF2ZVdSdlVuRkNTVUpsUVZsWE9WWnJZM2RFVVZsS0NrdHZXa2xvZG1OT1FWRkZURUpSUVVSblowVkNRVVE1TW5WRWFUTlNkbFJ6V0ZsUE9GVjJUVzAyVVRCTWNqWlZkMWhGZGxnMk5tRnBhMUZHY0VoVGRXb0tVRUZPYVV0MU5uVnhRMlU1THpkS1ZuUmhhWFZ1VkhaMFpFVlhXbmhaU0VVdlUzWnFRMlI2TDBSTFEwMDVVVEJaZGxjemIwZERiVGszYURWamVWTnRWQXBrYkdoUmJXOUlVVGx3WTJSaVRXb3JWalpFWTFONE9FUnBWRGMwYlhNNVRHNHdUazVvZEVVMU5tbE5ia3d5WWxWemFrcExWVVJsUzFWdVFVd3lWamRhQ2tORFdsZGpkSEJqYjFsTFJGaEphMDlGVVhwUldGRTVWa0ZEWWpkeVQzYzFlbUY1VmpKUlNXTXhXazB4VVhSeVRGcEpNR0pzUW5wWFQyOXhjbGt3VGtrS1NYVXljbnB2U3psNkt6UklTVUpMTlZCSE9Xb3hPVWt3TUhKbFQzQTVTalF6V0RWRGJsRjVTV2RQY0U4NFowY3lhRlZqVVVKTWRGaEtURnA2Vm1obFJncGFWazFNTWxkR09IRXJTMmxvZDJkRU1YcE5PWEF6ZUhSS1RHVnhZV0kyY0hVM01GQnpZaTlaWkRnNFBRb3RMUzB0TFVWT1JDQkRSVkpVU1VaSlEwRlVSUzB0TFMwdENpMHRMUzB0UWtWSFNVNGdRMFZTVkVsR1NVTkJWRVV0TFMwdExRcE5TVWxFVFdwRFEwRm9jV2RCZDBsQ1FXZEpTVmd2YUdOeFlraENhemxqZDBSUldVcExiMXBKYUhaalRrRlJSVXhDVVVGM1RucEZVMDFDUVVkQk1WVkZDa040VFVwaU0wSnNZbTVPYjJGWFdqQk5VMFYzU0hkWlJGWlJVVVJGZUdoeVpGZEtiRXhYUm5kaFdFNXNZMjVhYkdOcE1YTlphVEY2WVZka2RWcFlTWGNLU0doalRrMXFWWGRPYWtWM1RXcEZlazVFUlRGWGFHTk9UWHBWZDA1cVFUUk5ha1Y2VGtSRk1WZHFRVE5OVWtsM1JVRlpSRlpSVVV4RmQyeDJZMGRXZFFwak1taHdXbTVSZUVsVVFXWkNaMDVXUWtGTlZFZEhkREZaYlZWMFdWaENjR015Vm5sa2JWWjVURmQ0YVV4WVRuQmFNalZzWTJwRFEwRlRTWGRFVVZsS0NrdHZXa2xvZG1OT1FWRkZRa0pSUVVSblowVlFRVVJEUTBGUmIwTm5aMFZDUVU1dFpUbGhjbTlPYWsxS1ZubHhMM1JoTVdjelpFOU1kSGROWjJaaksxa0tOa1ZUVjFGUlkyeEtVVEJ6WWpoQlNuUjNiakp0VlVVeGNHNXdXV2xWYXpSTVZIaEpjRVV3Y20xRlFubGpMM1ZHZW1SU1YwMUhTR0ZOVTNwMVlWaEhVUXB5VVhOVU4xbEZXSFUxZGtvM2VWWkZWRTlJT0N0a1lsbEZWRmhRVlZOcU1VeE1NblJCU2xGMWVTOHpjMGxZTm5CUFlVUjFaekZ3WlVkRlJuQnRZMVZYQ2t0NWRuZ3ZLMkY0TVZwQ2VtMXRaMDlpYzFOelltZzRPVnBETVVoMVduZExaR05yVFdaNWFHMVllSHBLWjJwcU5IaEllSGhzTnlzcmNtcEhSVFZQU2tjS01HcEVVWEJXVjJ4cWVsaDNXRlp1YVVsbGRrZFlRVlZGYlhWWU1DOXVOMGwwUzI0elVscElZbkJUTDIxWmJHeG1UblZSYnpsa1FucFhMelJ1Ym05SVN3cFVObTFSYVZOcloxQkVWa2N6UjAxVVZUSXpNV3BXWkN0TWVUWk9TVWt2ZURsaWNXVlhPWGhqVlVGUllXeDRka1Y1UkdaSk56Vk5RMEYzUlVGQllVNURDazFGUVhkRVoxbEVWbEl3VUVGUlNDOUNRVkZFUVdkTGEwMUJPRWRCTVZWa1JYZEZRaTkzVVVaTlFVMUNRV1k0ZDBoUldVUldVakJQUWtKWlJVWktaRzhLTURObWRuUnNTRUp2YUU5SlJIQnJSMWQ1V0d4TFozTlBUVUV3UjBOVGNVZFRTV0l6UkZGRlFrTjNWVUZCTkVsQ1FWRkNNWEZpTm14S1ZGRnRTbU42TWdwblkwWnNUSE14VW5CR05qSkNlVzgxVm5kaGNIZ3dlWEJhTjFSUE5uQm9hVVZqV25BclJHOVhWak5EZEZCaVdXdEZPV2czVjBkSWVWRXdlR1pzZUU0MkNrRjJhMVZZTVdNNWJFbGtWRGRRVGpsdGRGTjZUMDF2YjFKTFptZE5NM0ZNVEU5d1RGZG1XamRaVFhFdmJFdEVTVGs1ZW1GYVNsaEpVak5MUzJGUlJESUtSa0o0VlhSRE4xVkVZblpSYURCaU1WRnVjQ3RDVVNzMVkwa3dUVGwzSzFGVFZTdEdNemQ0TkdWVU1ubHViV1p4VERScVVuWjFhM2d2YTFoM1p5OXNNd295TTI1UkwwOW9lbFJ6Uld0V2MxaE9VRVZWU1ZGTmFUQmlkV0oxZURKNVUxVTFWekk1Tkd4T1JuSTFjbVJJUjFoaU4wRXlaV051VkhOcU1HSldhbWx1Q2tkelJVaHlOa0prYUZscmEwMW1LMkZ6VEVaaGFVeGpiSGRKSzNOcmNXVmpSR1ZITDNaV1RIQlVUVEI2ZVVWUlptTTRTM2RxVEdOWVNEa3JVRTlPVVVFS1NUaFhkazVRU2xRS0xTMHRMUzFGVGtRZ1EwVlNWRWxHU1VOQlZFVXRMUzB0TFFvPQogICAgc2VydmVyOiBodHRwczovL2FwaS1pbnQuc25vLmxhYi5sb2NhbDo2NDQzCiAgbmFtZTogc25vCmNvbnRleHRzOgotIGNvbnRleHQ6CiAgICBjbHVzdGVyOiBzbm8KICAgIHVzZXI6IGt1YmVsZXQKICBuYW1lOiBrdWJlbGV0CmN1cnJlbnQtY29udGV4dDoga3ViZWxldApwcmVmZXJlbmNlczoge30KdXNlcnM6Ci0gbmFtZToga3ViZWxldAogIHVzZXI6CiAgICBjbGllbnQtY2VydGlmaWNhdGUtZGF0YTogTFMwdExTMUNSVWRKVGlCRFJWSlVTVVpKUTBGVVJTMHRMUzB0Q2sxSlNVUTRSRU5EUVhScFowRjNTVUpCWjBsSlUyMUljMWhQWkhaRlZEUjNSRkZaU2t0dldrbG9kbU5PUVZGRlRFSlJRWGRSYWtWVFRVSkJSMEV4VlVVS1EzaE5TbUl6UW14aWJrNXZZVmRhTUUxVGQzZExaMWxFVmxGUlJFVjVUbkprVjBwc1lrZFdNRXhYU25aaU0xSjZaRWhLYUdORE1YSmtWMHBzV1RJNWRRcGFiV3h1VEZoT2NGb3lOV3hqYWtGbFJuY3dlVTVVUVRKTlZFRjVUVlJOTUUxVVpHRkdkekI2VGxSQk1rMUVaM2xOVkUwd1RWUmtZVTFKUnpKTlYwRjNDa2hSV1VSV1VWRkxSWGhhZW1WWVRqQmFWekEyWXpKV2VXUnRiR3BhVjBacVdUSTVNV0p1VW5wTlJEaEhRVEZWUlVOb1RUUmpNMng2WkVkV2RFOXVUbXdLWTI1YWNGa3lWbWhaTWs1MlpGYzFNR042Y0haalIxWjFZekpvY0ZwdVVYUmlWMFpxWVVkc2RWcFRNV3BpTWpWdFlWZGpkR0l6UW14amJVWXdZak5KZUFwVmFrSlJRbWRPVmtKQlRWUlRXRTQxWXpOU2JHSlVjSHBhV0VveVlWZE9iRmxYVG1waU0xWjFaRVJ3ZG1OSFZuVmpNbWh3V201UmRHSlhSbXBoUjJ4MUNscFRNV3BpTWpWdFlWZGpkR0l6UW14amJVWXdZak5KTm1KdE9XdGFVekZwWWpJNU1HTXpVbmxaV0VKM1dsaEpkMmRuUldsTlFUQkhRMU54UjFOSllqTUtSRkZGUWtGUlZVRkJORWxDUkhkQmQyZG5SVXRCYjBsQ1FWRkVUMFZMZEdwaWJtWmhXRzV6WW1OWlptUkNhVmczTUhKV1QzQnlWM1IyVTNNd1JqZENVUW93YlhvMFdFOUhObVY0WlhSRWRGRlJVWGR4UzB0eWMwTXhRVkI0UkVwc01HbHpkRlUyZFZkSk5IRXZiVlpqU0hSSWJ6SjZja1pvYmtaTmRqSnNiMDFuQ2tKcU56TmxUWHB2VTI1RlZVeDJkaTl1VkROM2RHbHFWWFpwVVZoc1lUQk1NRkZHYzFSWFFuZEtTRlpuVm5ONGRGUkVRVk42V0UxQ1oyOW1hU3RuSzI4S2JUSjBPRGxIUzJGRlNTOXJNWGRFZWxaWU0zSlhObE5vVTFKUWFTdHpkRmQzVjJZeUsyeEVUazk1WVVkM2FIUkZZbnB1UlRVMFdESjBRamxzWTBGVmJnbzFWbkJoY0dScFIxbEVlRlJNYVdaTFVsTnJZa1l6ZGl0QmIwbHhaM2h1Tnl0b1JteGxNMFI2WTFOTGFrRnVlbEF4V1RWVWVHWnNUemhzYkZsT2RHbHJDbkpvU0dWU1RHcERRMWRtV2k5dGFIWTNkVGNyVUVGdE15dHVaVGxrTVRCVGF6Tm1RVEZqVTBvd05HZHVUVTFPTVVGblRVSkJRVWRxWkZSQ2VrMUJORWNLUVRGVlpFUjNSVUl2ZDFGRlFYZEpSbTlFUVZSQ1owNVdTRk5WUlVSRVFVdENaMmR5UW1kRlJrSlJZMFJCYWtGTlFtZE9Wa2hTVFVKQlpqaEZRV3BCUVFwTlFqQkhRVEZWWkVSblVWZENRbFF3VEc1MFEwVmpTRzFUVmpac1dUTm9WSE5FT0c1S1dEbHJOVVJCWmtKblRsWklVMDFGUjBSQlYyZENVemw2UkRoRENrZG5SRmhRWXpkb09YbDBPR2hHUXpkc1lqSjNkMVJCVGtKbmEzRm9hMmxIT1hjd1FrRlJjMFpCUVU5RFFWRkZRVkkyVjJ0NVdVMDVSbnBFVG1KcFpFVUtia3BCUmpCc1VYUnFPVWxSVWpWNGNsUk9ZbUZoU214RVFqQXZVV0pWYTFsc2JDdFRVRFZsTTAwNVNWTlZZM0UwVGtKMFkyUmxXblp4UVRRMU0zWm1ZZ3B2VVdSRmNsWTJhVzlIWnk5WU1taDBUV2x6V1ZKbVRucHpWVXBNU2tGMVZVVlVUV0p4VjB0TksyVk9VV2w2UmtRdmVVNVpka3N4WlhKUFlpdHlaWFpLQ2sxUk1XRlRRU3RKVTJ4UWNWYzRTakpMTWxob2RqaFVVVEYxVVU1dVpGUjRiRzFTVGpsNWJrZDRRbmxFV0RKR1RESTNZVVZsTjFBNGJuZDJWR016YTB3S1owSTVPRlZLTHk5b1VIUmlPSEUxUjBwbFJYTlhjakJXVDBvd2RETlhiRk5vZFhWU1RFRnpPRFJZTldnd2JFaGFSRVJEWWxkaUwzZENjRkpWWm10M2VBcGpVVmhEUVdSck5ETlZaSHBvU1V0aFRuRnJiak51U0RORWJGTTFLM053UTBvNE4wbFNWVloyTVVSYUwwdHljMXByTVRCNFRtazRWRVJsZGpnelpDOUJDakptTjNvMVFUMDlDaTB0TFMwdFJVNUVJRU5GVWxSSlJrbERRVlJGTFMwdExTMEsKICAgIGNsaWVudC1rZXktZGF0YTogTFMwdExTMUNSVWRKVGlCU1UwRWdVRkpKVmtGVVJTQkxSVmt0TFMwdExRcE5TVWxGY0VGSlFrRkJTME5CVVVWQmVtaERjbGt5TlRNeWJEVTNSek5IU0ROUldXd3JPVXN4VkhGaE1YSmlNSEpPUW1WM1ZVNUtjeXRHZW1oMWJuTllDbkpSTjFWRlJVMUxhV2x4TjBGMFVVUTRVWGxhWkVseVRGWlBjbXhwVDB0Mk5XeFlRamRTTms1ek5uaFpXbmhVVERsd1lVUkpRVmtyT1ROcVRUWkZjSGdLUmtNM055ODFNRGs0VEZsdk1VdzBhMFkxVjNSRE9VVkNZa1V4WjJORFVqRlpSbUpOWWxWM2QwVnpNWHBCV1V0SU5IWnZVSEZLZEhKbVVGSnBiV2hEVUFvMVRtTkJPREZXT1RZeGRXdHZWV3RVTkhaeVRGWnpSbTQ1ZG5CUmVsUnpiV2h6U1dKU1J6ZzFlRTlsUmpseVVXWmFXRUZHU2l0V1lWZHhXRmxvYlVFNENsVjVORzU1YTFWd1IzaGtOeTluUzBOTGIwMWFLeTl2VWxwWWRIYzRNMFZwYjNkS09IbzVWMDlWT0ZnMVZIWktXbGRFWWxsd1N6UlNNMnRUTkhkbmJHNEtNbVkxYjJJck4zVXZhbmRLZEM5d00zWllaR1JGY0U0emQwNVlSV2xrVDBsS2VrUkVaRkZKUkVGUlFVSkJiMGxDUVZGRGMyWk9UMW94V0hoS2N6bERSUXAwYkVkMGRERnVVVWw0TlZaRFkwMUdMMVpVVDNSQk1rbGxZblI0V1dZMGNFcExVVFl6YWtOM2RFRXhPR1pwZDNkUU9YaFZlQ3RGYWpWd1NYaE5UR0ZYQ2tWSWNVRTJZMUE0WjJocVNFUjZjVzltYjFsRmExSlRUbXdyZWxwS1lYTlFkbXhDVGxCek56bDBiR1ZGZVVaWlFsUnRMMVIwYUhjeGVXVjJVa0pwZGpFS1kzVTRNV2g2U25wUmRGUjVlVVJ4SzFKek9FMU9XbXR3TXpNM2IwSm1OMVJaYUZRNVRURk9SVXg1V2xSQ2MzRjBXR3B1UkRWdmVIcDRlWGRJZVhKalZBcGxURGsyTmt0Q1dUUklUbnBpVWxWQlkxVjBiVEEwYlRCT1prOVpjamxXYW1sdFQzbHpNVWRaUm5FeFNFMTFkV2hKWms4MFVuWkNNR3hsUlc1U05FcERDbEJDY0UxbWVVWjFhM0UxU2s0eFFqZ3dRblo2TldoV2RVSlpOM041U2tFNGVIVkVabE5MVWpKdFRrTXdWMEpvYzJNMFdXUlZNbWR3U0VGMU1FaDRUa0lLZDIxS2JtSlZWMEpCYjBkQ1FVNUZUV0pwYVVaWk9YSkhNMHhLWTJsT2VrRlNTakJzVUc5TlNWbHlhMUl4YlVSMWNqRnVaUzh5UzFSNGVGZFpTVFZsZGdwWFFWWTJVV2RqUzB4M1QzWnRXVUpoVm05d09YTnhRbXcyWTJGMmNtNVlRemhMTUd3dlZHOU1jVFpMWTBGWFZIUnBLME5XTW1rd1NqSnNkazlqYVVkSENqVnpNWFp0U0VGck5rUkxkWGxhWmk5aFZFTjNWUzlvYVdoMVNEaFpZVXhMVXk5V1ZETnVWU3M1Vkc5NFVVMVFhMDQwV1hwc05GaDRRVzlIUWtGUWVGa0tjelU0ZERaSFVIcDZkVWhTUmxoTU5WcHdNbVJCUzFZMlZXeFBlVXhZTVRSMVRYQlJaRkkyVmsxbFJVUnBZMWxSWXpSVVdrMTRXVXA0ZW1Fck5VdHpkZ3BNZVdJNVFURTRLMVZGY0U1dWFXVkJNa3hYUWk5SFV6ZDRlRTFxYlVWNFNXZFVkRlZLWm5GSk5WSTNVRkZsT0ZSck1tWTFNR1JhVTJacVpIUXpZM04wQ210a1RrcHVXbEJ1YldRdmJsazRjbGRqVkcxbFpHNU5iM3BTYXl0dllsVTBkR2xHYkZoTlNFWkJiMGRCVTJZMVNsYzBVbXBhVFU5M2VVWXdhV2RIYURVS2IwNXVWemsxUWxoVFZWSTRRMmhHVWpsa1EwNTFjbTEwTlZGSVZub3hTR05MVVhkMVNrMXVRUzlIUVVOVFJVVjRhM0JIVGxWWFRHaFVWbVJRZGtKVlJncEdXRkl4TTJvM1ZsRkthRkpyWjFSWE1YVmhWSFJCT1doMk9EaG9OMHRaVVU5aVZDOVZkWEIwVjJOemRWSmxUemgxZFZGbGVFeE5la3BRWjNSSmRUZFNDakZuVFdGdldYUnZVak5ITldnNWVsZHViVlJUYUVSRlEyZFpSVUZ3UlhoclpIVnNLeXN3VlUxc0x6VTBRbkZ1V0ZwQ2RVeENha3hGVlROWldrcEROWElLYzJ3ek9GRXpUa1ZVU1hKMlRYbFJWemh3U1hkTWRFMXBMemhRSzFGdE5uZE9jVkphWnpkUFozUjVRV1ZNZGpRelIzTmhUbEJHUkN0YWNtMTNUMVZ4YWdwNVlYTmhTbmx5YldOek4ya3dVRkptTjNwNU5tOU9Oa3BEZWtobVZFdENXVFU0V25kalNrcEZialJIWVdWalFuaGhLMElyYjNKQldVTmhNamQyTjBGNUNqbFVPVE41ZW10RFoxbENVRFYzVjBGRmRVOXBhbXBpYVVKU2VITjRRamx2ZVhWSFdsSnZRbVZSVjBaNWIwTkJjRU4xTlRSTFYyRnVhU3RETWt3eE1ra0taMW8wWm5sSGMwVlJVbHBuY1RaNWRsbERNRVZPVmtVclRsRlNNek5rT1VOWE4zWjBRbTVhZFV0SU9Hb3Ziek5hVldseFRIY3pkVXBrZDJZMFoyUklZZ29yV2xKS2VUVmlPSEoxY0U1RVNtNWtUM2g0UVVaSk4yeHRPR2R2T0VNdldGbFpaM1JLYkdKNVpYWjFTRVJUUTFkSlNFTlFSMEU5UFFvdExTMHRMVVZPUkNCU1UwRWdVRkpKVmtGVVJTQkxSVmt0TFMwdExRbz0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig-loopback", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/admin-kubeconfig-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNRENDQWhpZ0F3SUJBZ0lJSEJhaS92bGQyeDh3RFFZSktvWklodmNOQVFFTEJRQXdOakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TQXdIZ1lEVlFRREV4ZGhaRzFwYmkxcmRXSmxZMjl1Wm1sbkxYTnBaMjVsY2pBZQpGdzB5TlRBMk1UQXlNVE0wTVRSYUZ3MHpOVEEyTURneU1UTTBNVFJhTURZeEVqQVFCZ05WQkFzVENXOXdaVzV6CmFHbG1kREVnTUI0R0ExVUVBeE1YWVdSdGFXNHRhM1ZpWldOdmJtWnBaeTF6YVdkdVpYSXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRHBCUk9zdnIyVEU5M1krUFVhUVVYSEsxOEIwZHkrdW5lTQpBRi9tSG91dTNvUU1Nc3ZieGJnTjczWHMrSnJRQjBQTTUweGsyOU1pSWVaS1ZmVkl5V1U3cUo5dXFtUGFIMjRICnpIczZ5ZTgydE0wbXc0QjRIc1dVYWhRWjNHdTl2dU5rbEhtdXR5RU50bXc1TnNmN0xRTUZTVWxCdDgxYmU2V1cKQ1Jwc29uYkNzNnZSMUZxdjhzclJ1UWtiaHBueUcweVVjWFRPZGFnL1ZJUmlFNDNuemc5MWhrWjhJNTRTQ1NLQQpzdnpMZlBaSlVnTitYekw3K3R6SUFsTWpGcExXM1F0bFlaYlphNFZYeVpHOVhFZlg5YXJvY25lZHB2N3Z5VlNSCllQL2NRbmN0Q2krWCt2V2VPTVJYM05wcEV4cVBIbWljd085Q0xiU1NjWEVNVEIzUkdvVFZBZ01CQUFHalFqQkEKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlF5V0xRMwpyZUFmSHF3c0o2MlQwc0VFeWx1UE56QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFqbUwzNVp4b0pPbzBBSlhWCnpFWmxjenN0dlJHTWdQbElRb1M5MU12aSt1UzdiZ0tKemwyRDFiT2RXVno5dlZNSnhhNitaU0M1VnF1VDIrTGsKemV1Qk5ueDdDYTlvOGZOdXRrSmJwcFU2WjcwSndOS0pVdHFhZVBXaHRGa3NNWFdaNzlvaHNteTVkL2V6ek9UcQpMQWFUU3FqWE45c2xRVzAvNktiZVBBdm0zVVRhVGpGTTduTEg3Z2pFTmtEQmJVMjEwWHNnZFpzdllhbWN1cHgzCllUaXhYOWc4TmVzMFFLZjRnRmV2OW5jVURRallwUlJRYko2K0p2bEV1bURmTWIzTHBKVXVFaFg0QWhCNkFDbDUKVWF6clExdzRpUWdpb25xWVBNQnk3QTdEbzdZYUJDaFNnY0R5UmQ5d2Z2Q1QwdGI5WUVYN1NBMm1XT3VMb2F5awpBeDhndUE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURGRENDQWZ5Z0F3SUJBZ0lJZmxuYWNtenJHeU13RFFZSktvWklodmNOQVFFTEJRQXdLREVSTUE4R0ExVUUKQ3hNSVltOXZkR3QxWW1VeEV6QVJCZ05WQkFNVENtRm5aM0psWjJGMGIzSXdIaGNOTWpVd05qRXdNakV6TkRFNApXaGNOTWpVd05qRXhNakV6TkRFNFdqQW9NUkV3RHdZRFZRUUxFd2hpYjI5MGEzVmlaVEVUTUJFR0ExVUVBeE1LCllXZG5jbVZuWVhSdmNqQ0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU1YN1FvRWMKek5DVmZBQVZEV1dlaHF0RXI5cnR6YnNZRVl1R2dzNk83RTVuVG0wWVQ4TlRVSzV1K3FPSk9DWUhVZGw0ZnhpbApCalZnMktnSHlKSkpaMEgyRXhIaW1ocktlRzgyY2Y4b2NxZWNITjFZMFp3YTBkaUU5TGNyV0hOTlJGN2QzSFlICjZFU1kwTU1BR1dXcmJVWWJOVGd4SUhyVkxmSTIxMFE3dEl6ODQza1U0a1hqenRoK1lSTGJ0bmhzalcweUo0akkKa0hUOEZZMDJRMTNQQ3JISkRBN001aytDYjltZnQvV05PaW9vWTlBVGR2VUREbXBSalZuV1p2ZU9LQnZxb2N2SgpRTHJvd3JybVZ2VVpuVGsxNEVqZlpVdHdudm5XdC9kZHpBSEswM1hZUTVFQU5hS2poOTZtSDZFVjlORlY4U0psCmpaOUtLUjkxbkVoa0Z4RUNBd0VBQWFOQ01FQXdEZ1lEVlIwUEFRSC9CQVFEQWdLa01BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdIUVlEVlIwT0JCWUVGQTlGOEhkcFlvTlZMWVEwYnFZeTJXNEd4Y2NOTUEwR0NTcUdTSWIzRFFFQgpDd1VBQTRJQkFRQmEwbGU2UWE5VFVTakw3MkhMRXVQbkhYQWwxRklGSHYyOUNqOXo0bjRnRzlGSCtIMCszSmR5CjJuRGhFaEl2NDIxam1va1ZGT0hwdmRMQzFaOENORGFrcTcvWFp5bG9jOUt4Qk9hZU05M0dJSmRrWGRqNU0wODcKUGwxVkd6S24vZzJOVjNBaTBXM3V0WEwyMERkbDZMRzExZllpNFEwdloySlBwVklFY3BvT01sL09NLzBsbEZhbgp1bmx4VWFBUTlRejZTUnVjeDVxY0VValFya1JXWFhESC81TEpmVzZoR2M4WmZDYWJjeFFiVTV2SkdxWVdHeWRpCmRhcnVCK3dyZlJkRnRZYm9lN0crQURpeUtBaEVZMkw2Tm5aM0p3OHNDSmh1Z0xrSFVQMTVXanRIbW40SE9saEoKWTl5N2VMNTZFVEhOUEh1VzJYVU5VeW1WWTlVZzVnWm4KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKRENDQWd5Z0F3SUJBZ0lJREh6WGVRWjFhcmt3RFFZSktvWklodmNOQVFFTEJRQXdNREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Sb3dHQVlEVlFRREV4RmhaMmR5WldkaGRHOXlMWE5wWjI1bGNqQWVGdzB5TlRBMgpNVEF5TVRNME1UaGFGdzB5TlRBMk1URXlNVE0wTVRoYU1EQXhFakFRQmdOVkJBc1RDVzl3Wlc1emFHbG1kREVhCk1CZ0dBMVVFQXhNUllXZG5jbVZuWVhSdmNpMXphV2R1WlhJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFDMUF1MFkvQUpicmhRblVnVUpONitpOEZGKzZydjFoNzd4eS8wMFM4SjhjelZEKzdzbQpzVlpwSXYyMVBxOFNUbnhHRUFRL2IwbWFaV2dLRkRpUmdGQmpCdFo0a2FJcjVwTmJiU1grbjJNaXc3TFVRU25VCi9JNnNHVlBVV3VMUXVybEZtQVExUTVQNXV3UlFQSDJhY2kyZzZPMGpxUkdjZTk1dXE5Y2VmcGpCK05wanFJWkEKYjZRc0tNQzZ5enI4bXZTVUVJd3hoZXRmdGlBTXZySit2QThKMFprbjNHMWNpWVB3b2xWQ0xrS1RsaVlkWDVyZwpUMHZyTU9SV0hodWJXQ09hL0kvN2RSYW1wNUVob3cxMXMxS0ZoN1hrMlAzL0ZmZDVjZHVVUnRwS0srMncrWTl0CkVaWmxtMW5NMG16NVRsci9zNWYzdUY4T09zeTUvVloxdlBnZEFnTUJBQUdqUWpCQU1BNEdBMVVkRHdFQi93UUUKQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCU2l6djNJck9SbXpicUx6RW5ZNE9hbQoyVHZMY3pBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQVNQQ2Jvbmsya2R0NTVMNTRCZ3pSYmpVdXdxL1FHN2wrCkw4OHZINXd4Zk1pU3l3L0RRZC9DYXZiY095R3ZyME43VE5icEtrQUZtem93Qyt2UkNObXpQVnVscVZLbDFxaHoKbm5zY0hBTWFmUEtZQzZDL3NYTytXbGJWMTdETll0eUdXOWFvbHBQdEI4RStYTjgrdFJDTlkxYndIL29vbHJkZQo2Z0lrd3NCRzBodHdLZ21LUE5aVHl2STJaZVMxTnk1Yzdxb3EvTmUrUGVJZFVxSlZ1c2pvZWJYUHEzM3B3MzBMCi9MZndybmx2ek12TUlWNG1SNnVubCtpR2ZuNEVCSWNQQnZlVXZmZVZpYlE4TzkxMmp3YWhlNDdWdUQ0RTA1MW4KTmJKTXZzNm1ObXA1NU5YYXlBMTNDL0FMOHRPR3M3WXRGSWxiRzVnTmk2KysrMXNBS3UxQkRRPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURZekNDQWt1Z0F3SUJBZ0lJRTI0SVVSL0dLbVF3RFFZSktvWklodmNOQVFFTEJRQXdNREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Sb3dHQVlEVlFRREV4RmhaMmR5WldkaGRHOXlMWE5wWjI1bGNqQWVGdzB5TlRBMgpNVEF5TVRNME1UaGFGdzB5TlRBMk1URXlNVE0wTVRsYU1Ed3hGREFTQmdOVkJBb1RDMnQxWW1VdGJXRnpkR1Z5Ck1TUXdJZ1lEVlFRREV4dHplWE4wWlcwNmEzVmlaUzFoY0dselpYSjJaWEl0Y0hKdmVIa3dnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREZIM3BJTTM3eGE0RFdFZURyUVM1SytZNWVWTDVuYUl0WApzdDBDNVZHd1lzVVBXSEpoaGxabXViZnd4TXJKM0dYMjU5YW8xR09rZEwvcnBPMjRUdVlsMTFLaDNiWXN1WEpQCjhxZ1FOOERGT1EvN2FqbWdDcTdkY1JWbDI1Qm1saWhwV29TY3lRSmkwSS9FWk1IUEN4ajVPdWpBM0kwU0EyTmcKQ04yKzQ0MjJwbmZkc3YvZC9sSFlmb3I0ajcvZVJDeE02aUxlOXBVbkFKQkpmelF0aTA2K0VTY0N5Nmp1dVJsQgpEOWpLdWR0NENjTnBvUUFXWk84b09NNUlXUTVFS3lnM1lzYzB1OVhpc3psQTRGenJFVkdQUjAzRUdlQ2g5NC9jCmpySytEbEFUb3ZRR3dIVmFJN24xaUJ3OE1LalpSNTIxOWg3MnN3dGl6WDNwZzRmKy9BTFhBZ01CQUFHamRUQnoKTUE0R0ExVWREd0VCL3dRRUF3SUZvREFUQmdOVkhTVUVEREFLQmdnckJnRUZCUWNEQWpBTUJnTlZIUk1CQWY4RQpBakFBTUIwR0ExVWREZ1FXQkJUVzI3RWhlc0trNTd0MExtdXMzSm1Lc2M4ejVUQWZCZ05WSFNNRUdEQVdnQlNpCnp2M0lyT1JtemJxTHpFblk0T2FtMlR2TGN6QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFiK2ppcmU5Q092dDEKTHFubWJENXoxbVhhdWFzTFIxMFVLVmVjRjdzUGdyZ0FXa1puTUVlSFM0MFRxek1BYm03R05uVjNMVFllVWp1NApkbEJGSGdvSEsvVVVRd1N0Z0xIM1F4cDk2T0pkNXB3amtmc3A3T0pnMUs5M0labkMzSTdHYmJHc2pNeWJrMFRpCk9LR055T1NnQytFU0kxamF1L2F4ek10Y1ErWklLcVVhaHRzM2VyMjQ5bTRoYk5tc0tBMkhHY3BDUm5IMUkrOEwKanJZeEEweDRjc3BVZFFMYnl0NUY3UEJLMXJYNUhVaGJlUlpZbFR2WC80bDd4R2FJcm40M2p3TE84SEtycjh6QgppTGlPaXZpc3N0cWFyTTYxNDM1OThTaGZQSHEza0tmOTE4Wmk3TUtzQlFJVXVGVHhSbmJUclVRbFFnTDZJekNBCmZLUkVmYVBablE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKRENDQWd5Z0F3SUJBZ0lJREh6WGVRWjFhcmt3RFFZSktvWklodmNOQVFFTEJRQXdNREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Sb3dHQVlEVlFRREV4RmhaMmR5WldkaGRHOXlMWE5wWjI1bGNqQWVGdzB5TlRBMgpNVEF5TVRNME1UaGFGdzB5TlRBMk1URXlNVE0wTVRoYU1EQXhFakFRQmdOVkJBc1RDVzl3Wlc1emFHbG1kREVhCk1CZ0dBMVVFQXhNUllXZG5jbVZuWVhSdmNpMXphV2R1WlhJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFDMUF1MFkvQUpicmhRblVnVUpONitpOEZGKzZydjFoNzd4eS8wMFM4SjhjelZEKzdzbQpzVlpwSXYyMVBxOFNUbnhHRUFRL2IwbWFaV2dLRkRpUmdGQmpCdFo0a2FJcjVwTmJiU1grbjJNaXc3TFVRU25VCi9JNnNHVlBVV3VMUXVybEZtQVExUTVQNXV3UlFQSDJhY2kyZzZPMGpxUkdjZTk1dXE5Y2VmcGpCK05wanFJWkEKYjZRc0tNQzZ5enI4bXZTVUVJd3hoZXRmdGlBTXZySit2QThKMFprbjNHMWNpWVB3b2xWQ0xrS1RsaVlkWDVyZwpUMHZyTU9SV0hodWJXQ09hL0kvN2RSYW1wNUVob3cxMXMxS0ZoN1hrMlAzL0ZmZDVjZHVVUnRwS0srMncrWTl0CkVaWmxtMW5NMG16NVRsci9zNWYzdUY4T09zeTUvVloxdlBnZEFnTUJBQUdqUWpCQU1BNEdBMVVkRHdFQi93UUUKQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCU2l6djNJck9SbXpicUx6RW5ZNE9hbQoyVHZMY3pBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQVNQQ2Jvbmsya2R0NTVMNTRCZ3pSYmpVdXdxL1FHN2wrCkw4OHZINXd4Zk1pU3l3L0RRZC9DYXZiY095R3ZyME43VE5icEtrQUZtem93Qyt2UkNObXpQVnVscVZLbDFxaHoKbm5zY0hBTWFmUEtZQzZDL3NYTytXbGJWMTdETll0eUdXOWFvbHBQdEI4RStYTjgrdFJDTlkxYndIL29vbHJkZQo2Z0lrd3NCRzBodHdLZ21LUE5aVHl2STJaZVMxTnk1Yzdxb3EvTmUrUGVJZFVxSlZ1c2pvZWJYUHEzM3B3MzBMCi9MZndybmx2ek12TUlWNG1SNnVubCtpR2ZuNEVCSWNQQnZlVXZmZVZpYlE4TzkxMmp3YWhlNDdWdUQ0RTA1MW4KTmJKTXZzNm1ObXA1NU5YYXlBMTNDL0FMOHRPR3M3WXRGSWxiRzVnTmk2KysrMXNBS3UxQkRRPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/apiserver-proxy.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/apiserver-proxy.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURXekNDQWtPZ0F3SUJBZ0lJVXdhYkZOeHBxMTh3RFFZSktvWklodmNOQVFFTEJRQXdLREVSTUE4R0ExVUUKQ3hNSVltOXZkR3QxWW1VeEV6QVJCZ05WQkFNVENtRm5aM0psWjJGMGIzSXdIaGNOTWpVd05qRXdNakV6TkRFNApXaGNOTWpVd05qRXhNakV6TkRFNVdqQThNUlF3RWdZRFZRUUtFd3RyZFdKbExXMWhjM1JsY2pFa01DSUdBMVVFCkF4TWJjM2x6ZEdWdE9tdDFZbVV0WVhCcGMyVnlkbVZ5TFhCeWIzaDVNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUYKQUFPQ0FROEFNSUlCQ2dLQ0FRRUF6Ukl5V2pmNDU3Y1VTWEFqeW9aSUh1TjNhT3M4ZWgzQzFwNVFLTWhzR3JjZgp2U3lzRlZSWUxOQjJpVGEvR08vRytzcWpKcTVPT3pFU3l0OFl6b0QySGcrWTBFbkgwVnp0WDVpOHcvWjh0RnBlCit2ZkN4SldESUdZNjZkMmxwSGs3V0VlRGpwOENJWS9PQzNMZVBmbzZ0Yk9BQWJ1cEprVTRwNTYvd1greUYxZTMKQ1hZeTZnWndpWXJyVGR1OUdCcVM0VTVYK09Gd3lBZEFBU245VnVOVGpwSHZiTjZDa1YwUnNlN24yRmpaMVJPQwpvUEpsNlIwSXVXWisyTXBnRzNWZitxRzB6SkhrMDh4WGw2UkY3U1FRNEYySmZhcDRzU0JLRjM1QStoTXRDcVI5CjZJZTJFbGRVZ2dCOEJQWFpzU2Roc0VwWGl6THdLb0U2Qy8zMm54L000UUlEQVFBQm8zVXdjekFPQmdOVkhROEIKQWY4RUJBTUNCYUF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdJd0RBWURWUjBUQVFIL0JBSXdBREFkQmdOVgpIUTRFRmdRVXYxMHBvcWNyaVA0a0M4aEpTaWljRjNxRXhXc3dId1lEVlIwakJCZ3dGb0FVRDBYd2QybGlnMVV0CmhEUnVwakxaYmdiRnh3MHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSnFKbmpLbHZQOWZSY3czT1VzTE9QVi8KckxEUGRweGEvME92YTZ2VnpXdDc0QTg1Mlk1UVlDdnNtbkV5S2p2UDNTZ1Z2dnFJRGFpZkROZnk1MlNEMkpmRgphOUp6NjNSV2xpZjR0bHp2b0RRMS9mMVlDYTdQY0hlNFVQaGw4SmRQTmd0RW5ZaFV5Wjg4ZDZTQnYycmNBeFlrCjZIa2VzSG9RTXJRdER0YkNLUklRblRxSWYxYlZMdk9TMG56TGJnY3pUQVlnVVpKVE5Pa2svMk1MSmxueXNDVXUKYUdZNCtZR0ZwMTdZbWphajVia0FyUkVYQXA5elYydWtPNUxYTjlMT2Y4UDJKTGVvVGllVjNwdWhFZEhjYmdtNgowak03cjN4MFVkYnBSRExTQjRGZENyeWV4ZUhEbHZqNWdoMzBLZ3l1WjNCRnpJTFNrcVM4Ums5RzJMVjMvQ1k9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNakNDQWhxZ0F3SUJBZ0lJWC9oY3FiSEJrOWN3RFFZSktvWklodmNOQVFFTEJRQXdOekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TRXdId1lEVlFRREV4aHJkV0psTFdGd2FYTmxjblpsY2kxc1lpMXphV2R1WlhJdwpIaGNOTWpVd05qRXdNakV6TkRFMVdoY05NelV3TmpBNE1qRXpOREUxV2pBM01SSXdFQVlEVlFRTEV3bHZjR1Z1CmMyaHBablF4SVRBZkJnTlZCQU1UR0d0MVltVXRZWEJwYzJWeWRtVnlMV3hpTFhOcFoyNWxjakNDQVNJd0RRWUoKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTm1lOWFyb05qTUpWeXEvdGExZzNkT0x0d01nZmMrWQo2RVNXUVFjbEpRMHNiOEFKdHduMm1VRTFwbnBZaVVrNExUeElwRTBybUVCeWMvdUZ6ZFJXTUdIYU1TenVhWEdRCnJRc1Q3WUVYdTV2Sjd5VkVUT0g4K2RiWUVUWFBVU2oxTEwydEFKUXV5LzNzSVg2cE9hRHVnMXBlR0VGcG1jVVcKS3l2eC8rYXgxWkJ6bW1nT2JzU3NiaDg5WkMxSHVad0tkY2tNZnlobVh4ekpnamo0eEh4eGw3KytyakdFNU9KRwowakRRcFZXbGp6WHdYVm5pSWV2R1hBVUVtdVgwL243SXRLbjNSWkhicFMvbVlsbGZOdVFvOWRCelcvNG5ub0hLClQ2bVFpU2tnUERWRzNHTVRVMjMxalZkK0x5Nk5JSS94OWJxZVc5eGNVQVFhbHh2RXlEZkk3NU1DQXdFQUFhTkMKTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkpkbwowM2Z2dGxIQm9oT0lEcGtHV3lYbEtnc09NQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUIxcWI2bEpUUW1KY3oyCmdjRmxMczFScEY2MkJ5bzVWd2FweDB5cFo3VE82cGhpRWNacCtEb1dWM0N0UGJZa0U5aDdXR0h5UTB4Zmx4TjYKQXZrVVgxYzlsSWRUN1BOOW10U3pPTW9vUktmZ00zcUxMT3BMV2ZaN1lNcS9sS0RJOTl6YVpKWElSM0tLYVFEMgpGQnhVdEM3VURidlFoMGIxUW5wK0JRKzVjSTBNOXcrUVNVK0YzN3g0ZVQyeW5tZnFMNGpSdnVreC9rWHdnL2wzCjIzblEvT2h6VHNFa1ZzWE5QRVVJUU1pMGJ1YnV4MnlTVTVXMjk0bE5GcjVyZEhHWGI3QTJlY25Uc2owYlZqaW4KR3NFSHI2QmRoWWtrTWYrYXNMRmFpTGNsd0krc2txZWNEZUcvdlZMcFRNMHp5RVFmYzhLd2pMY1hIOStQT05RQQpJOFd2TlBKVAotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-internal-lb-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-internal-lb-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNakNDQWhxZ0F3SUJBZ0lJWC9oY3FiSEJrOWN3RFFZSktvWklodmNOQVFFTEJRQXdOekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TRXdId1lEVlFRREV4aHJkV0psTFdGd2FYTmxjblpsY2kxc1lpMXphV2R1WlhJdwpIaGNOTWpVd05qRXdNakV6TkRFMVdoY05NelV3TmpBNE1qRXpOREUxV2pBM01SSXdFQVlEVlFRTEV3bHZjR1Z1CmMyaHBablF4SVRBZkJnTlZCQU1UR0d0MVltVXRZWEJwYzJWeWRtVnlMV3hpTFhOcFoyNWxjakNDQVNJd0RRWUoKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTm1lOWFyb05qTUpWeXEvdGExZzNkT0x0d01nZmMrWQo2RVNXUVFjbEpRMHNiOEFKdHduMm1VRTFwbnBZaVVrNExUeElwRTBybUVCeWMvdUZ6ZFJXTUdIYU1TenVhWEdRCnJRc1Q3WUVYdTV2Sjd5VkVUT0g4K2RiWUVUWFBVU2oxTEwydEFKUXV5LzNzSVg2cE9hRHVnMXBlR0VGcG1jVVcKS3l2eC8rYXgxWkJ6bW1nT2JzU3NiaDg5WkMxSHVad0tkY2tNZnlobVh4ekpnamo0eEh4eGw3KytyakdFNU9KRwowakRRcFZXbGp6WHdYVm5pSWV2R1hBVUVtdVgwL243SXRLbjNSWkhicFMvbVlsbGZOdVFvOWRCelcvNG5ub0hLClQ2bVFpU2tnUERWRzNHTVRVMjMxalZkK0x5Nk5JSS94OWJxZVc5eGNVQVFhbHh2RXlEZkk3NU1DQXdFQUFhTkMKTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkpkbwowM2Z2dGxIQm9oT0lEcGtHV3lYbEtnc09NQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUIxcWI2bEpUUW1KY3oyCmdjRmxMczFScEY2MkJ5bzVWd2FweDB5cFo3VE82cGhpRWNacCtEb1dWM0N0UGJZa0U5aDdXR0h5UTB4Zmx4TjYKQXZrVVgxYzlsSWRUN1BOOW10U3pPTW9vUktmZ00zcUxMT3BMV2ZaN1lNcS9sS0RJOTl6YVpKWElSM0tLYVFEMgpGQnhVdEM3VURidlFoMGIxUW5wK0JRKzVjSTBNOXcrUVNVK0YzN3g0ZVQyeW5tZnFMNGpSdnVreC9rWHdnL2wzCjIzblEvT2h6VHNFa1ZzWE5QRVVJUU1pMGJ1YnV4MnlTVTVXMjk0bE5GcjVyZEhHWGI3QTJlY25Uc2owYlZqaW4KR3NFSHI2QmRoWWtrTWYrYXNMRmFpTGNsd0krc2txZWNEZUcvdlZMcFRNMHp5RVFmYzhLd2pMY1hIOStQT05RQQpJOFd2TlBKVAotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lJUWdxRHl5Zm1OMnN3RFFZSktvWklodmNOQVFFTEJRQXdQakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TZ3dKZ1lEVlFRREV4OXJkV0psTFdGd2FYTmxjblpsY2kxc2IyTmhiR2h2YzNRdApjMmxuYm1WeU1CNFhEVEkxTURZeE1ESXhNelF4TlZvWERUTTFNRFl3T0RJeE16UXhOVm93UGpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2d3SmdZRFZRUURFeDlyZFdKbExXRndhWE5sY25abGNpMXNiMk5oYkdodmMzUXQKYzJsbmJtVnlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQTBnUWQvOHo3RXliNAo4YXRKaXBVcWdoWXozNU9IYS85aUlrOXQreGt1Z1pBR01uaytYVktvSHNOMzRBK1ZRaUVidnU0SVd4RzNsM09sClRMQXFPaDFFMCtmajBDTXZBbHZjYWJkQy9GM25oQlFKQ2gvYStMMUUxZjQwWnlsREoveHM4OUZEQVVPanlHNjcKeldsUFJqdEFDMjJ0c0J0S0xpWkhPN2V6YjEweTNIbmo1SHQ5RXJCMlFRRjBvVTVGMGpDb3hhcTFxWXVjRFA3QwovRGhaNTFBYVVBenE1NmcxNW5kcTF5eVVNN3hOUU9BalovYXcwRktXYk5SbmY2bm56a1N0UzI3WDVDMHZwclZjCmFQV0NOWWJLcnkzekl5a1JaVVpOVi9QanhHMHM1YVowVm56NzlXditxanQ2ZnFTVzFySG9KL0sxcGJWRzEzOUwKUmFLd2prTWV1UUlEQVFBQm8wSXdRREFPQmdOVkhROEJBZjhFQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVTUMrY1o5Tk5FNDBmRFp0WEZrenlWaVYxTUlVd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBRGNac1JMcnp1NkpCakU4ckJxTmloMkg0TzV1T1NWbXFJTGI5S2M2MElUSk42RDN2QUVaVzFiUXE3NEwKczJ3RXJYc3ZOSEZuVWlraE5rVUgybWNWdlZpUi9kUmtudXBjYjhuWDFpcjA2eFJtM2VXcktoWk1pSWtBdmlpeAo4NHY1Zld1TUZtNWpUR2ZkaGJwQk9UNmNYTmdxNUpjRG1ib2hhdHlOVnZ3UXZEQXVCMGtrRnhLbVpYTXZ4cTA2CmRYbmtjUHdkWGd4RjU4NWV3NS9iRWFNYjQ5d1NaL0lCZVNoVVlxdlJGOXdrak9JT0lENjZyMmFudmdEdUJ0QkQKb2tZc2xWSVQxendZQmN5YjQ1RnJLRVFsbEFtSnJ4WGRrdElzb3c0V0tTTk5LN2U3cm9HWHVjdVNjeGJvZXFTSwpKbFR1amlyc1VTOVNzaG5kc3Y5OXlQV084Vjg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lJUWdxRHl5Zm1OMnN3RFFZSktvWklodmNOQVFFTEJRQXdQakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TZ3dKZ1lEVlFRREV4OXJkV0psTFdGd2FYTmxjblpsY2kxc2IyTmhiR2h2YzNRdApjMmxuYm1WeU1CNFhEVEkxTURZeE1ESXhNelF4TlZvWERUTTFNRFl3T0RJeE16UXhOVm93UGpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2d3SmdZRFZRUURFeDlyZFdKbExXRndhWE5sY25abGNpMXNiMk5oYkdodmMzUXQKYzJsbmJtVnlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQTBnUWQvOHo3RXliNAo4YXRKaXBVcWdoWXozNU9IYS85aUlrOXQreGt1Z1pBR01uaytYVktvSHNOMzRBK1ZRaUVidnU0SVd4RzNsM09sClRMQXFPaDFFMCtmajBDTXZBbHZjYWJkQy9GM25oQlFKQ2gvYStMMUUxZjQwWnlsREoveHM4OUZEQVVPanlHNjcKeldsUFJqdEFDMjJ0c0J0S0xpWkhPN2V6YjEweTNIbmo1SHQ5RXJCMlFRRjBvVTVGMGpDb3hhcTFxWXVjRFA3QwovRGhaNTFBYVVBenE1NmcxNW5kcTF5eVVNN3hOUU9BalovYXcwRktXYk5SbmY2bm56a1N0UzI3WDVDMHZwclZjCmFQV0NOWWJLcnkzekl5a1JaVVpOVi9QanhHMHM1YVowVm56NzlXditxanQ2ZnFTVzFySG9KL0sxcGJWRzEzOUwKUmFLd2prTWV1UUlEQVFBQm8wSXdRREFPQmdOVkhROEJBZjhFQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVTUMrY1o5Tk5FNDBmRFp0WEZrenlWaVYxTUlVd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBRGNac1JMcnp1NkpCakU4ckJxTmloMkg0TzV1T1NWbXFJTGI5S2M2MElUSk42RDN2QUVaVzFiUXE3NEwKczJ3RXJYc3ZOSEZuVWlraE5rVUgybWNWdlZpUi9kUmtudXBjYjhuWDFpcjA2eFJtM2VXcktoWk1pSWtBdmlpeAo4NHY1Zld1TUZtNWpUR2ZkaGJwQk9UNmNYTmdxNUpjRG1ib2hhdHlOVnZ3UXZEQXVCMGtrRnhLbVpYTXZ4cTA2CmRYbmtjUHdkWGd4RjU4NWV3NS9iRWFNYjQ5d1NaL0lCZVNoVVlxdlJGOXdrak9JT0lENjZyMmFudmdEdUJ0QkQKb2tZc2xWSVQxendZQmN5YjQ1RnJLRVFsbEFtSnJ4WGRrdElzb3c0V0tTTk5LN2U3cm9HWHVjdVNjeGJvZXFTSwpKbFR1amlyc1VTOVNzaG5kc3Y5OXlQV084Vjg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURURENDQWpTZ0F3SUJBZ0lJRmVwSjF0ZlpnZjh3RFFZSktvWklodmNOQVFFTEJRQXdSREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjJhV05sTFc1bApkSGR2Y21zdGMybG5ibVZ5TUI0WERUSTFNRFl4TURJeE16UXhOVm9YRFRNMU1EWXdPREl4TXpReE5Wb3dSREVTCk1CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjIKYVdObExXNWxkSGR2Y21zdGMybG5ibVZ5TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQwpBUUVBd01JaE9WeEkxN0pTc01EN2tPM3VUVG1DYXFyK0k2aWlxa0lJNmt6SVhmU3FkQ1RtWWhEbmtOd3E4T1hFClVrQklyZnJEMTdiRkdQZERUbmV0MzVGVVdGWUFpWFZWZlczamZ0cnQzaFlRL092bUNuK3BXY2xQS3UvSVVXRloKV1IvWk12c1N5YURGaWl5VjZDVFV1dlFoMithM21rTDk2OEYyNDl4aDVVSkg4aFZiY0gwNWQxWUV0dFNqbFhXOAo4NFNDTnprR2JCaWRHTVZldkdmdVYxVlhQZlUwNURNSmRFWTVFVTRYakUxSURsUXB1WHRPQVdzbjhROEdMend6CmFPaEsrUkdMRlNsVGUvMjIzS3FiZzJ2NGcxYnJPQVhadVpINUN3YzJTYVp5MVNtZHhEMWx1MHVGa3BGTVdRNzcKVjRSa3o3alZHMktBeTNiemFvOFdqZ2lkaVFJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRApWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVXhhT0JJZ2MxbjQveWRvUnFCSUJlQVlXOVZrY3dEUVlKCktvWklodmNOQVFFTEJRQURnZ0VCQUQ5MnVEaTNSdlRzWFlPOFV2TW02UTBMcjZVd1hFdlg2NmFpa1FGcEhTdWoKUEFOaUt1NnVxQ2U5LzdKVnRhaXVuVHZ0ZEVXWnhZSEUvU3ZqQ2R6L0RLQ005UTBZdlczb0dDbTk3aDVjeVNtVApkbGhRbW9IUTlwY2RiTWorVjZEY1N4OERpVDc0bXM5TG4wTk5odEU1NmlNbkwyYlVzakpLVURlS1VuQUwyVjdaCkNDWldjdHBjb1lLRFhJa09FUXpRWFE5VkFDYjdyT3c1emF5VjJRSWMxWk0xUXRyTFpJMGJsQnpXT29xclkwTkkKSXUycnpvSzl6KzRISUJLNVBHOWoxOUkwMHJlT3A5SjQzWDVDblF5SWdPcE84Z0cyaFVjUUJMdFhKTFp6VmhlRgpaVk1MMldGOHErS2lod2dEMXpNOXAzeHRKTGVxYWI2cHU3MFBzYi9ZZDg4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURURENDQWpTZ0F3SUJBZ0lJRmVwSjF0ZlpnZjh3RFFZSktvWklodmNOQVFFTEJRQXdSREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjJhV05sTFc1bApkSGR2Y21zdGMybG5ibVZ5TUI0WERUSTFNRFl4TURJeE16UXhOVm9YRFRNMU1EWXdPREl4TXpReE5Wb3dSREVTCk1CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjIKYVdObExXNWxkSGR2Y21zdGMybG5ibVZ5TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQwpBUUVBd01JaE9WeEkxN0pTc01EN2tPM3VUVG1DYXFyK0k2aWlxa0lJNmt6SVhmU3FkQ1RtWWhEbmtOd3E4T1hFClVrQklyZnJEMTdiRkdQZERUbmV0MzVGVVdGWUFpWFZWZlczamZ0cnQzaFlRL092bUNuK3BXY2xQS3UvSVVXRloKV1IvWk12c1N5YURGaWl5VjZDVFV1dlFoMithM21rTDk2OEYyNDl4aDVVSkg4aFZiY0gwNWQxWUV0dFNqbFhXOAo4NFNDTnprR2JCaWRHTVZldkdmdVYxVlhQZlUwNURNSmRFWTVFVTRYakUxSURsUXB1WHRPQVdzbjhROEdMend6CmFPaEsrUkdMRlNsVGUvMjIzS3FiZzJ2NGcxYnJPQVhadVpINUN3YzJTYVp5MVNtZHhEMWx1MHVGa3BGTVdRNzcKVjRSa3o3alZHMktBeTNiemFvOFdqZ2lkaVFJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRApWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVXhhT0JJZ2MxbjQveWRvUnFCSUJlQVlXOVZrY3dEUVlKCktvWklodmNOQVFFTEJRQURnZ0VCQUQ5MnVEaTNSdlRzWFlPOFV2TW02UTBMcjZVd1hFdlg2NmFpa1FGcEhTdWoKUEFOaUt1NnVxQ2U5LzdKVnRhaXVuVHZ0ZEVXWnhZSEUvU3ZqQ2R6L0RLQ005UTBZdlczb0dDbTk3aDVjeVNtVApkbGhRbW9IUTlwY2RiTWorVjZEY1N4OERpVDc0bXM5TG4wTk5odEU1NmlNbkwyYlVzakpLVURlS1VuQUwyVjdaCkNDWldjdHBjb1lLRFhJa09FUXpRWFE5VkFDYjdyT3c1emF5VjJRSWMxWk0xUXRyTFpJMGJsQnpXT29xclkwTkkKSXUycnpvSzl6KzRISUJLNVBHOWoxOUkwMHJlT3A5SjQzWDVDblF5SWdPcE84Z0cyaFVjUUJMdFhKTFp6VmhlRgpaVk1MMldGOHErS2lod2dEMXpNOXAzeHRKTGVxYWI2cHU3MFBzYi9ZZDg4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-complete-server-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-complete-client-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRVENDQWltZ0F3SUJBZ0lITk1WSkYyUlFLakFOQmdrcWhraUc5dzBCQVFzRkFEQS9NUkl3RUFZRFZRUUwKRXdsdmNHVnVjMmhwWm5ReEtUQW5CZ05WQkFNVElHdDFZbVV0WVhCcGMyVnlkbVZ5TFhSdkxXdDFZbVZzWlhRdApjMmxuYm1WeU1CNFhEVEkxTURZeE1ESXhNelF5TTFvWERUSTJNRFl4TURJeE16UXlNMW93UHpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2t3SndZRFZRUURFeUJyZFdKbExXRndhWE5sY25abGNpMTBieTFyZFdKbGJHVjAKTFhOcFoyNWxjakNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFMbHhjSXJOY1ZiSQpqdEtWY0RSWU45QTY3MUs3bTBYaHl0MWhqNUFtb3VRbmZhVmp0TDAxUDdjWTlmOEdxSUZTdEJMRHZwNE1XYi9RCk9zazdRWC84NnEyMUR0aWtUbm5QK0NlL3lBQzdlVG9rNE5QTjlRR2pSMjVQSzBGbnBrYkdnZFZIYVU4R0YrVFIKTzRZVkhpaGs2MmkrS0liWmVMVHRLcGtZTk9zWVFQZk1DeC9xbG94S3RnYzFkTHZkR1BnY2MvanJ1ZjhFYzVObgo5Q0ZwY2hRZW0vYXo5MDk2QWFCY29SWSsvWHdmQVB0ZlBENlVVUmNsYURrTFBUTFpRNWV6OTRuaEp4dzNMOCt3ClYxMWlUQS9KbmpNV3FWQ1VidWNnVk1hd2FJOXNTWGFISktaMkZ5K01SWXk3L1Y4ZUV2Rnh2TXN3ekxNVGlOb3QKNjdPcmxUeDNVaU1DQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnS2tNQThHQTFVZEV3RUIvd1FGTUFNQgpBZjh3SFFZRFZSME9CQllFRkhyQTlVU3d5Z1JPcWp3RUQ2bEErR1lqRStva01BMEdDU3FHU0liM0RRRUJDd1VBCkE0SUJBUUNucmxKYS9SZndiMXM5YUxkQjA0YWV1SFNoaUJDQ0dCUHFJc04wUGxTaWRXZkhQTXUrNWcweG4yaEQKYklqK0pGKy9EeHAvekpZN3g4elhHSXQ4R2VETE1QZlkrWGM3Ym4zN1F1L1p2SGt3LzNHeElFOWRmZnV5a1FraApoM2R1bmkvR1UwbWIybFJhR0s5NCtiL2tNMmV1QmNtSE5SYS94NkNUY3h0UGFSZERiU1A2UWNGVGVXQU9pK2RMCmdMREpXbllocUd6UDU1UGFYMnB4YkJOSXEvVlE3ZjlCOEp0NFNQQkZkdFYzMmRYVWV4b3p5TVpMR0Yyamh0SDkKVkIrazZnQmMxZ2I4aWNQcVZQcjVuejkyZWdGbmt0RFVyWHRzbGxtNDI0K1V2QUdOUjljTm5kK3VROEVsdzFrZgpjR1JORG5WQmVFSzV2ci95VUZKVHJpNUpvMThjCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURiRENDQWxTZ0F3SUJBZ0lJWUlsaEs2bklZc1F3RFFZSktvWklodmNOQVFFTEJRQXdQekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Ta3dKd1lEVlFRREV5QnJkV0psTFdGd2FYTmxjblpsY2kxMGJ5MXJkV0psYkdWMApMWE5wWjI1bGNqQWVGdzB5TlRBMk1UQXlNVE0wTWpOYUZ3MHlOakEyTVRBeU1UTTBNak5hTURZeEZEQVNCZ05WCkJBb1RDMnQxWW1VdGJXRnpkR1Z5TVI0d0hBWURWUVFERXhWemVYTjBaVzA2YTNWaVpTMWhjR2x6WlhKMlpYSXcKZ2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRE13VlpKbzgvMktjaTRCQVpKeDIzSAo1RVo5UXFYdmFpek9TajdaUURQNERjdXJRNUtKeGFEU0FPajh4MWxDK0dTVWplRXlhTVlhWmU0cFRxWm1TUHh6CkdDT1UrUmhUcHdRbC9HSmNZZ2Z6S25zSDVDMmZkTm50SmRGSmJGci9JQU1iMWhrUG1hTEZXbVh0R0JrZFZCN0sKNmZNOUdWM0NPME9Pa0hvZW85cHRKMnZyMnVxSmQ3aVovNzkwWk1lN3haOW5wRzVNbmtGajhmaTBDSytNSlUregpmbysxRjRDSGZJQkRwNFdqMnU2T1YxY2dKTjJackpSZ25rbGlDa0h5ZmJWRDFqanNFaGg4Q1kxL1lram53SVppClBJVnFndWpTSExLL0E3WFpuN2xMSC9ZNW92RkhhMHVEWHR2UzBCdE9QaENaNklqYmcrak9mbG9mYVY3eXpkTjkKQWdNQkFBR2pkVEJ6TUE0R0ExVWREd0VCL3dRRUF3SUZvREFUQmdOVkhTVUVEREFLQmdnckJnRUZCUWNEQWpBTQpCZ05WSFJNQkFmOEVBakFBTUIwR0ExVWREZ1FXQkJTYkxIMVNEancyUkgzd3RYeVphdzdtMzUxVE56QWZCZ05WCkhTTUVHREFXZ0JSNndQVkVzTW9FVHFvOEJBK3BRUGhtSXhQcUpEQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUEKU0JTSUJFM2V4VjAyc2xOaUNqcENvRDR1WTJzT1N0dGhiWXhBby9YVGovMUJQVFg5L1FGdlRSdDJ3YmorczZWbApQeVFXakFsTmtTV3lJbW5qVEFOeHBYRUNjcmJPWUJvVGh0Z0ZUWGFxeUFXeFVUZ3ZWcDJEZkFrSkJ5L1NKczVDClB1a0lzMFhLZDZQTUZnSkpLM3NuZnNKalp5NmJVY3ZtRGRQTDFuUzJmOGdEeU9hUmZzbUVyQk1NRndnOFk5ME0KT25ObnU3eDJVSnhMck1vUWdzRlRQaXNjclBxZFZVNmZEWnZQbVR0d2RFUmdYNW5GSXdicXlBTkVtR3o1cTlLWgpPa1k3MkwzRlUxOXZHTE9YMFFWUndXNXlETnBCdjdYZDEvTFdZMnBWOXFJTzJDZWJqSngycE1qYS9iUWtwUDNoCmNZZWkwaGhFVDdHaTgwa0F1eU9lN3c9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRVENDQWltZ0F3SUJBZ0lITk1WSkYyUlFLakFOQmdrcWhraUc5dzBCQVFzRkFEQS9NUkl3RUFZRFZRUUwKRXdsdmNHVnVjMmhwWm5ReEtUQW5CZ05WQkFNVElHdDFZbVV0WVhCcGMyVnlkbVZ5TFhSdkxXdDFZbVZzWlhRdApjMmxuYm1WeU1CNFhEVEkxTURZeE1ESXhNelF5TTFvWERUSTJNRFl4TURJeE16UXlNMW93UHpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2t3SndZRFZRUURFeUJyZFdKbExXRndhWE5sY25abGNpMTBieTFyZFdKbGJHVjAKTFhOcFoyNWxjakNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFMbHhjSXJOY1ZiSQpqdEtWY0RSWU45QTY3MUs3bTBYaHl0MWhqNUFtb3VRbmZhVmp0TDAxUDdjWTlmOEdxSUZTdEJMRHZwNE1XYi9RCk9zazdRWC84NnEyMUR0aWtUbm5QK0NlL3lBQzdlVG9rNE5QTjlRR2pSMjVQSzBGbnBrYkdnZFZIYVU4R0YrVFIKTzRZVkhpaGs2MmkrS0liWmVMVHRLcGtZTk9zWVFQZk1DeC9xbG94S3RnYzFkTHZkR1BnY2MvanJ1ZjhFYzVObgo5Q0ZwY2hRZW0vYXo5MDk2QWFCY29SWSsvWHdmQVB0ZlBENlVVUmNsYURrTFBUTFpRNWV6OTRuaEp4dzNMOCt3ClYxMWlUQS9KbmpNV3FWQ1VidWNnVk1hd2FJOXNTWGFISktaMkZ5K01SWXk3L1Y4ZUV2Rnh2TXN3ekxNVGlOb3QKNjdPcmxUeDNVaU1DQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnS2tNQThHQTFVZEV3RUIvd1FGTUFNQgpBZjh3SFFZRFZSME9CQllFRkhyQTlVU3d5Z1JPcWp3RUQ2bEErR1lqRStva01BMEdDU3FHU0liM0RRRUJDd1VBCkE0SUJBUUNucmxKYS9SZndiMXM5YUxkQjA0YWV1SFNoaUJDQ0dCUHFJc04wUGxTaWRXZkhQTXUrNWcweG4yaEQKYklqK0pGKy9EeHAvekpZN3g4elhHSXQ4R2VETE1QZlkrWGM3Ym4zN1F1L1p2SGt3LzNHeElFOWRmZnV5a1FraApoM2R1bmkvR1UwbWIybFJhR0s5NCtiL2tNMmV1QmNtSE5SYS94NkNUY3h0UGFSZERiU1A2UWNGVGVXQU9pK2RMCmdMREpXbllocUd6UDU1UGFYMnB4YkJOSXEvVlE3ZjlCOEp0NFNQQkZkdFYzMmRYVWV4b3p5TVpMR0Yyamh0SDkKVkIrazZnQmMxZ2I4aWNQcVZQcjVuejkyZWdGbmt0RFVyWHRzbGxtNDI0K1V2QUdOUjljTm5kK3VROEVsdzFrZgpjR1JORG5WQmVFSzV2ci95VUZKVHJpNUpvMThjCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-controller-manager-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-controller-manager-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURYekNDQWtlZ0F3SUJBZ0lJR2tqeU8yV0RDRzh3RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXhNREl4TXpReU0xb1hEVEkyTURZeE1ESXhNelF5TTFvd01ERVhNQlVHQTFVRUNoTU9jM2x6CmRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41YzNSbGJUcGhaRzFwYmpDQ0FTSXdEUVlKS29aSWh2Y04KQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU5aQWNJTDhwK1lsUlVhSUswSVBYbnNCZTBKbW16TmdzMS9vVDFnSgpDMktBNDB1Z2IvbkdEU1hyZXd6QnBBdXpWbUk1YlFQWnhjZzU5eGZ6MkRSRWZ0dW55UUlVVEt0RC9MMVM2N1pFCktEeWhHejNta3d5aS9XNTZTZWFOaVp2dE8rZ0dTYUQwVHhJOXZRMGNIRDRKQTNiQ0FWK0NOdlJzaC9oa1NQeG4KK2JWam9VNk5ISGN3QWhZeC9rMkJNNG1JYlN4NzIzdlZRcTk3QmhsZUxGWS9FNEs2K2FtdGxzcGMxbWIvenQ5ZQpweG1ETGd1OVBsZnhJc2JVRzhmMVhkdEZnd2U3d3IzSktoUm56ekVWM3gwWndMdXloM0Y5Z0d1U0QvblQ4MFVXClIxV0lyZ3pZOFBKSmxEUFBVdUdEMTdrb3V4ZTZreUhqbkpHRlJBY25lMUhZaHdNQ0F3RUFBYU4xTUhNd0RnWUQKVlIwUEFRSC9CQVFEQWdXZ01CTUdBMVVkSlFRTU1Bb0dDQ3NHQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBdwpIUVlEVlIwT0JCWUVGSi9IU25KQmJIOUJlRWZEZ0tSU0lEWnlqTnFPTUI4R0ExVWRJd1FZTUJhQUZPRm92NDU5CkJDdURPcHEzV2FKNnVwTVJ5aS9oTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFDcjVBdmg3TWk4Tk5SY3pkNVUKTWNac010SVFueEtIV29xSi9jTmQ4djBqa3BkUGhheExma2ZrL08rajhVT250UmtMU0lIaWF4cnJyNFNYRlpPOQppZHYzR2oxRVVIZFFuaXVacUJnWkl2UzdrL3BvS2I4dDN1RHF6ZGVWcGUwcS8vOVpnMHlUL3BLWDF6YkxySm9lCmw3dlhObG5DMUR1RnlQZzl3WUwxNkFTRS9abzNUMElBb1EydW9UcHBxS1JaNHBidmwvNGh1RzdidEEvUEhpeG0KSGU5VXk5K2taa1dhMDFPZ1VmMXhjUmd0Z09jOUxpeUxkUmtzRGZEdTAvUlVEck1PWml1QXcvMm90V0hyZmtpKwp4dmpreHREL2QxMmh6T3k2UHlWenBMOCtGWWFnSkFrUWtseCtsWk5DMm1YOURWNkZ6Q00zdU9weG02d1hQQ1ppCmpaTGUKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-scheduler-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-scheduler-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURYekNDQWtlZ0F3SUJBZ0lJTmcwY3RacmF5Vmt3RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXhNREl4TXpReU0xb1hEVEkyTURZeE1ESXhNelF5TkZvd01ERVhNQlVHQTFVRUNoTU9jM2x6CmRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41YzNSbGJUcGhaRzFwYmpDQ0FTSXdEUVlKS29aSWh2Y04KQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUtVR2s2amRheDJjR0MzSDBRR0JSMkpaaE5MRmVkY0VlOU0rMGIwcgpvYTZTUnJMSEp4RVJXWTI3djhBczNGeTdkQjRlRjM3cjNTTThWNlRmbzV1YjYwYW9KWlh2VzlHNkJNVmJFYmlxCjEvcnQyUjhEM01FaXFqL0FOWGVOSEhoK0k1QjQ4a0wwVldXNElDREp5TzlER016SzE0L0wwdXJQUjhyU3NieWcKSktDdHRLTUEyeVNpUXVaOGtVdFBmWmRGTmRadjNwVjBNZGdCNWhodE5XamRCdlByeDBqK1F2eUtvUEhmNC9QaQpSUUxtV3VaQjFTencvcmM1Mm40MHVOUm04RVVPMnF2cURDSjQ5R21oYUZNMGVwdTNqNmNlWEVraHlNU0pMVXZ1CkpoSTV4ZzErUkZXemRkbzRKR2t2d2RVQy9oTGN0ZERrQnMxVEdhRUswRGhFUmY4Q0F3RUFBYU4xTUhNd0RnWUQKVlIwUEFRSC9CQVFEQWdXZ01CTUdBMVVkSlFRTU1Bb0dDQ3NHQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBdwpIUVlEVlIwT0JCWUVGSlR0Tit4R0g1bjJQV1VPVkdjRm5yOGMydHdYTUI4R0ExVWRJd1FZTUJhQUZPRm92NDU5CkJDdURPcHEzV2FKNnVwTVJ5aS9oTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFDWERBQy9rRFFrVThXYXRYdEYKYUpicTEyQ1ZHalAyenNlU3RUK3c5RVIxR0R0Q3hpR2lxbnpBSkd4RHhRMUNTUGFHa1pFbjVIb0hpQW90eFNaegpMQjZVYlBabG4wRTU1RXc3cFpHelUzazJlWmxWOVFRQUJmMzYrd1orZHRnQVhFdllnaFlZM0d6ajlHdkl3dS9YCkFWa0FCbm1USlNObTB3alJqK2xOeG5QRXJJSk5SZ0VPSmtDeCs0TDk3SW5ZNnM2V0Zic1VYRWhtVFFYYmF1UWsKcmxPZXdrUjNiYUFsSFI4ck5jaitaUnl5RkVVZUVFdmZtMnV1ZGVhd0JCc2NNZldvam90SmlWMG5JMHF3QzBFOAo5NkNnamY0eUJzTmNvTlFaZ1ZWUWpaUGpNUFJFaERxQzdIeFFPcXdKbzNsVm5ONUlZS2hseVdGcXZkU1FUQW50CllSOUUKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURORENDQWh5Z0F3SUJBZ0lJRi96aHFGeHhndG93RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXhNREl4TXpReU0xb1hEVEkyTURZeE1ESXhNelF5TTFvd09ERVNNQkFHQTFVRUN4TUpiM0JsCmJuTm9hV1owTVNJd0lBWURWUVFERXhscmRXSmxMV052Ym5SeWIyd3RjR3hoYm1VdGMybG5ibVZ5TUlJQklqQU4KQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBdFYzeXVtMk1pWHg3L0JoQ0lwblZyTVA5c3JKUQo3RHA3aXlOOHlPdHRTWldNWkpFaUExSk13dVJqTGN6MW1VdjlwVFFEMWxDRmdKSjRjNTJaM2FsRXBBRkpCSC9aCjI5OFVIU2J6SzJqdWcvUVNSVWpML1JpcGZ2ZGdFOTJ3QlMySUpoTEhhR1NhTk5EK29rdVJCbTlvZmx5Mk12cE4KbGhONHFkTm54Y0JTM3duckFjVEdoSlBBRGg5enVETC9KRjlBeE5EakdxVzlVNVJkQ0c4RmE0bDJKU0ttcDIvSAp3TkJseUY5a3FYaTREeGZPVkRaZHM3Z0pJQXdKeHJOZUZjeW9aY3ZNVWF4MThDbDlEWGVIWUtiY0VLNk9EYWxICkxxM2ZiVDVlQTZFaGtZN1k2OHN4cnVmZ255SFdIY1ZseDk1cTRHRlBaWFNVQUUzdU5yRUpITkE1NHdJREFRQUIKbzBJd1FEQU9CZ05WSFE4QkFmOEVCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVQo0V2kvam4wRUs0TTZtcmRab25xNmt4SEtMK0V3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUs4U0Vkc2Uvc3p1Cml6dW54eU83VVUvcUR5OUY5MDhrNndYZ2ZwbW9EdWNQc0QzWExnWG95dWxuVHJKWTh2dVNQUjNDV3U2WU81ZmoKdmIzTDhtWG4vd0FTcTRuVVZxMzdwZHdFalJ2eVNjNGFFdk1RYVdrSC9sdzh3NUFUTUJtQjFoYldaR3QvemcyRQpGZG5lVTlCMXZING9FbTZZVUgzRDJlUnlicFJ6SHVJdDFWWVp4TUxpVTB2THhZTWZKVS8yRGVmclFFODVqeFlZClU4eGlqZjBDcGxvR2ZVR00wNFB2dmF6NlBHU0dSWGhYTVhRNkEzblBkN3drSzErcmRKdHBLMjNadVpnQmhJUWYKZFlkRkZpU28wR0p2NXZHWUxVRHFDTGdyOGtmK21yMk1FbEpNOFpPZVB5ZDk0Z2JQY1NjeUd2c0p2QitaVU1zMQpVdmJtbFdidGxUUT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-bootstrap-kubeconfig-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURTRENDQWpDZ0F3SUJBZ0lJSXErZUcxSDdJdEF3RFFZSktvWklodmNOQVFFTEJRQXdRakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Td3dLZ1lEVlFRREV5TnJkV0psYkdWMExXSnZiM1J6ZEhKaGNDMXJkV0psWTI5dQpabWxuTFhOcFoyNWxjakFlRncweU5UQTJNVEF5TVRNME1UZGFGdzB6TlRBMk1EZ3lNVE0wTVRkYU1FSXhFakFRCkJnTlZCQXNUQ1c5d1pXNXphR2xtZERFc01Db0dBMVVFQXhNamEzVmlaV3hsZEMxaWIyOTBjM1J5WVhBdGEzVmkKWldOdmJtWnBaeTF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDcApTcG5wL1NlVGFvRUlNMSt6Ni9vb1RmRThFNWx1SWh3OGJEblZyVW15aE1zMENnZXd2N1hDMlg1WFBpbWxxYjZ4CkhpTFVXNnROZWp5dWpvdm43clNEUjlUOVY3c3M4KzUrUWtCRVFWRFVFeFREcEFaVFlva0ZVaW5ubFNCVDRYV0oKZFNqYUQrSTJNYU1yQXNtWVhVcWd5N2VHaWpieWkvelFkbTJPUTJyblo4WFZYVjZ1Z2YvbzNrK2toUWU3MW43ZwpRQWNEUnB1Mk10L2hkQXNsQzg2V01nK2wxS0dRbktDRzlvMW1BUmdINmNGMEx2K1E2VVFnamNlQ3YyTU9qL0JKCm1lZHg2NlZ1cFdaRFkwNHpIaVQwK1U2dzZNWmlWd1BZUVRkOFIrT2lxSWREbG5uWlZKN0Z1NVAyNHY5MjR4MVMKVEV6VlQ0bnZtMFZkVkUvcStiYkpBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVBCZ05WSFJNQgpBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlM5ekQ4Q0dnRFhQYzdoOXl0OGhGQzdsYjJ3d1RBTkJna3Foa2lHCjl3MEJBUXNGQUFPQ0FRRUFqT3hsZ2FpSWJVZUZlRjkwMGk0Y08wdmcwTXJjSStFZmZpdzlZRTZCeloxelgxd0QKYXVlMndNSVplRmVDVFNrcHZWblhtNTVYbW1aTENoQUxlQ2ZjVkRENXhwQmVYd01SSUJqR09GbEJvMDR6a1F5Ngo0UXEwU0xRUHhobUh3OVBISnFxSFJzMTdDVWNoTDM5Tmo2WndMVUY5dGNaY2hPUVpOeHlBNzVlSHQrclErcFFNCi9kMHdkRWFGV0I0WUZkMGhFbmIzYi9lbko1RU0vSmV2WWJINTcyZ0ZGZzI4VmE0YnBzQ3UxOS9EanlCWGd5QksKZFgyM2IzVVhiT0IyaXkyMjd2am5oQmFVbUJCVHoxL1dncFczSi9zWEVQNmlmNHEwc0dNTE42em52WXZmQTlVdwpZQVl0eU1seUphNjk3aTM5bC9oLzlGVkpsUk5ERWlLYURLMHRZdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJUVlLaEJ6WGZwb0l3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTVRBeQpNVE0wTWpKYUZ3MHlOVEEyTVRFeU1UTTBNakphTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRRFR3aTBHdVF4VklNN0VkeXJsb3dWSTdvRzJzSjFEOHAyVytyU2t4VGxjMWdSdkE2QUh3UG5Sc3h2Tgo3WTJNR3oyQS9xKzFTQ09jVlBzUDR4c3NmOW0rOGZIaEhrdkQxNlNOQXFNUTl0MUh6dHhOUGVsTmJzUC94Zmg2ClowUVY0V1Z4Q3BEbzRLUlpMbEZMTTBjbGNkWjJyTHdUV1lZc0RpbGJFZmtSdzdrNWJYQVhwN21CRGFJUlBTMVcKVXhzSElFNzd2WmVTbHducThNMlk2Qy9TbmYrVjBWRW9NeEltUys4OFFBdDAvZ2RDR2JGRmFFeXhTSUhkTzIwUwpZelVXbW1senZDMytyeXVGc0pXVEpuZVpoV1UvWXNEMklFTWErVGFEVklBbTVyZ3daOGQ3OUprcGo1cXZEYkhoCm9hNGNaUExuK3NUQ3hqMGNqUnBaYlFPRENGTnZBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlRlRTVUVGx3ZUI3RUdpdGtjYTloWWJFeW5KaHpBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFibXZENUI5eExMZjZoeS9STFpWZkRtR21yN3Nwa0Vha3VKb3VIMUE5CmNlT0VIdHFvNHE2aFJCMWEyNUNyOGxaTTY5RkF6YnZFS3NMYUZrMFZldHFXQXRyb3Y2NWRCZEwwcnpqbHZvanIKa0swNm8va1l6U1lmWDhici8yYTFNRGRJbXFYZjB2eW11YVhmMDlqM0V5QTFiZUplRjZmbitEOGY3TXVIRWIwdAphVEp4dC9rZzlZUWVVWkhJSFB1cFJiNm5JcmY4VThzTnJDTlJQV2F0TGcvUFMyMVJoY3I5OERxcVU2NldPbk9rCkExQWVDRmxGRXkvVkVWMzUzNFdQUE8xUjUwYngrZ1VEMytFcGYwa3p3bmFPelc4bXFOc3pnaTJBeWxjYklXQUYKU2xrbkw5NUU1Ujk1dnJMajhXcWRLSi9JQ2MzVExLdHg2QjE5dEIvU1BxRUVudz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ4RENDQXRpZ0F3SUJBZ0lJU21Ic1hPZHZFVDR3RFFZSktvWklodmNOQVFFTEJRQXdRakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Td3dLZ1lEVlFRREV5TnJkV0psYkdWMExXSnZiM1J6ZEhKaGNDMXJkV0psWTI5dQpabWxuTFhOcFoyNWxjakFlRncweU5UQTJNVEF5TVRNME1UZGFGdzB6TlRBMk1EZ3lNVE0wTVRkYU1JRzJNV0F3CkhRWURWUVFLRXhaemVYTjBaVzA2YzJWeWRtbGpaV0ZqWTI5MWJuUnpNRDhHQTFVRUNoTTRjM2x6ZEdWdE9uTmwKY25acFkyVmhZMk52ZFc1MGN6cHZjR1Z1YzJocFpuUXRiV0ZqYUdsdVpTMWpiMjVtYVdjdGIzQmxjbUYwYjNJeApVakJRQmdOVkJBTVRTWE41YzNSbGJUcHpaWEoyYVdObFlXTmpiM1Z1ZERwdmNHVnVjMmhwWm5RdGJXRmphR2x1ClpTMWpiMjVtYVdjdGIzQmxjbUYwYjNJNmJtOWtaUzFpYjI5MGMzUnlZWEJ3WlhJd2dnRWlNQTBHQ1NxR1NJYjMKRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFET0VLdGpibmZhWG5zYmNZZmRCaVg3MHJWT3ByV3R2U3MwRjdCUQowbXo0WE9HNmV4ZXREdFFRUXdxS0tyc0MxQVB4REpsMGlzdFU2dVdJNHEvbVZjSHRIbzJ6ckZobkZNdjJsb01nCkJqNzNlTXpvU25FVUx2di9uVDN3dGlqVXZpUVhsYTBMMFFGc1RXQndKSFZnVnN4dFREQVN6WE1CZ29maStnK28KbTJ0ODlHS2FFSS9rMXdEelZYM3JXNlNoU1JQaStzdFd3V2YyK2xETk95YUd3aHRFYnpuRTU0WDJ0QjlsY0FVbgo1VnBhcGRpR1lEeFRMaWZLUlNrYkYzditBb0lxZ3huNytoRmxlM0R6Y1NLakFuelAxWTVUeGZsTzhsbFlOdGlrCnJoSGVSTGpDQ1dmWi9taHY3dTcrUEFtMytuZTlkMTBTazNmQTFjU0owNGduTU1OMUFnTUJBQUdqZFRCek1BNEcKQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBakFNQmdOVkhSTUJBZjhFQWpBQQpNQjBHQTFVZERnUVdCQlQwTG50Q0VjSG1TVjZsWTNoVHNEOG5KWDlrNURBZkJnTlZIU01FR0RBV2dCUzl6RDhDCkdnRFhQYzdoOXl0OGhGQzdsYjJ3d1RBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQVI2V2t5WU05RnpETmJpZEUKbkpBRjBsUXRqOUlRUjV4clROYmFhSmxEQjAvUWJVa1lsbCtTUDVlM005SVNVY3E0TkJ0Y2RlWnZxQTQ1M3ZmYgpvUWRFclY2aW9HZy9YMmh0TWlzWVJmTnpzVUpMSkF1VUVUTWJxV0tNK2VOUWl6RkQveU5ZdksxZXJPYityZXZKCk1RMWFTQStJU2xQcVc4SjJLMlhodjhUUTF1UU5uZFR4bG1STjl5bkd4QnlEWDJGTDI3YUVlN1A4bnd2VGMza0wKZ0I5OFVKLy9oUHRiOHE1R0plRXNXcjBWT0owdDNXbFNodXVSTEFzODRYNWgwbEhaRERDYldiL3dCcFJVZmt3eApjUVhDQWRrNDNVZHpoSUthTnFrbjNuSDNEbFM1K3NwQ0o4N0lSVVZ2MURaL0tyc1prMTB4Tmk4VERldjgzZC9BCjJmN3o1QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJUVlLaEJ6WGZwb0l3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTVRBeQpNVE0wTWpKYUZ3MHlOVEEyTVRFeU1UTTBNakphTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRRFR3aTBHdVF4VklNN0VkeXJsb3dWSTdvRzJzSjFEOHAyVytyU2t4VGxjMWdSdkE2QUh3UG5Sc3h2Tgo3WTJNR3oyQS9xKzFTQ09jVlBzUDR4c3NmOW0rOGZIaEhrdkQxNlNOQXFNUTl0MUh6dHhOUGVsTmJzUC94Zmg2ClowUVY0V1Z4Q3BEbzRLUlpMbEZMTTBjbGNkWjJyTHdUV1lZc0RpbGJFZmtSdzdrNWJYQVhwN21CRGFJUlBTMVcKVXhzSElFNzd2WmVTbHducThNMlk2Qy9TbmYrVjBWRW9NeEltUys4OFFBdDAvZ2RDR2JGRmFFeXhTSUhkTzIwUwpZelVXbW1senZDMytyeXVGc0pXVEpuZVpoV1UvWXNEMklFTWErVGFEVklBbTVyZ3daOGQ3OUprcGo1cXZEYkhoCm9hNGNaUExuK3NUQ3hqMGNqUnBaYlFPRENGTnZBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlRlRTVUVGx3ZUI3RUdpdGtjYTloWWJFeW5KaHpBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFibXZENUI5eExMZjZoeS9STFpWZkRtR21yN3Nwa0Vha3VKb3VIMUE5CmNlT0VIdHFvNHE2aFJCMWEyNUNyOGxaTTY5RkF6YnZFS3NMYUZrMFZldHFXQXRyb3Y2NWRCZEwwcnpqbHZvanIKa0swNm8va1l6U1lmWDhici8yYTFNRGRJbXFYZjB2eW11YVhmMDlqM0V5QTFiZUplRjZmbitEOGY3TXVIRWIwdAphVEp4dC9rZzlZUWVVWkhJSFB1cFJiNm5JcmY4VThzTnJDTlJQV2F0TGcvUFMyMVJoY3I5OERxcVU2NldPbk9rCkExQWVDRmxGRXkvVkVWMzUzNFdQUE8xUjUwYngrZ1VEMytFcGYwa3p3bmFPelc4bXFOc3pnaTJBeWxjYklXQUYKU2xrbkw5NUU1Ujk1dnJMajhXcWRLSi9JQ2MzVExLdHg2QjE5dEIvU1BxRUVudz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-serving-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJUVlLaEJ6WGZwb0l3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTVRBeQpNVE0wTWpKYUZ3MHlOVEEyTVRFeU1UTTBNakphTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRRFR3aTBHdVF4VklNN0VkeXJsb3dWSTdvRzJzSjFEOHAyVytyU2t4VGxjMWdSdkE2QUh3UG5Sc3h2Tgo3WTJNR3oyQS9xKzFTQ09jVlBzUDR4c3NmOW0rOGZIaEhrdkQxNlNOQXFNUTl0MUh6dHhOUGVsTmJzUC94Zmg2ClowUVY0V1Z4Q3BEbzRLUlpMbEZMTTBjbGNkWjJyTHdUV1lZc0RpbGJFZmtSdzdrNWJYQVhwN21CRGFJUlBTMVcKVXhzSElFNzd2WmVTbHducThNMlk2Qy9TbmYrVjBWRW9NeEltUys4OFFBdDAvZ2RDR2JGRmFFeXhTSUhkTzIwUwpZelVXbW1senZDMytyeXVGc0pXVEpuZVpoV1UvWXNEMklFTWErVGFEVklBbTVyZ3daOGQ3OUprcGo1cXZEYkhoCm9hNGNaUExuK3NUQ3hqMGNqUnBaYlFPRENGTnZBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlRlRTVUVGx3ZUI3RUdpdGtjYTloWWJFeW5KaHpBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFibXZENUI5eExMZjZoeS9STFpWZkRtR21yN3Nwa0Vha3VKb3VIMUE5CmNlT0VIdHFvNHE2aFJCMWEyNUNyOGxaTTY5RkF6YnZFS3NMYUZrMFZldHFXQXRyb3Y2NWRCZEwwcnpqbHZvanIKa0swNm8va1l6U1lmWDhici8yYTFNRGRJbXFYZjB2eW11YVhmMDlqM0V5QTFiZUplRjZmbitEOGY3TXVIRWIwdAphVEp4dC9rZzlZUWVVWkhJSFB1cFJiNm5JcmY4VThzTnJDTlJQV2F0TGcvUFMyMVJoY3I5OERxcVU2NldPbk9rCkExQWVDRmxGRXkvVkVWMzUzNFdQUE8xUjUwYngrZ1VEMytFcGYwa3p3bmFPelc4bXFOc3pnaTJBeWxjYklXQUYKU2xrbkw5NUU1Ujk1dnJMajhXcWRLSi9JQ2MzVExLdHg2QjE5dEIvU1BxRUVudz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/machine-config-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/machine-config-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURXRENDQWtDZ0F3SUJBZ0lJYWozSGUzeDVOWkF3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZeE1ESXhNelF4TmxvWApEVE0xTURZeE1ESXhNamN5TWxvd0p6RWxNQ01HQTFVRUF4TWNjM2x6ZEdWdE9tMWhZMmhwYm1VdFkyOXVabWxuCkxYTmxjblpsY2pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTDNwMStieEg0R2cKaG1SczJMMFJFeUp4cUQxUjVwN2REdHk2bmJoTjNSZzQ4QzRjb2tOVVhHSUFWZkdFeUF2eDE2aHp0Yi81VGluRwp0eGFORjg4Rml0QXhVVTZSOUxYR3BCdWMrNWVCRFkrNU5lR1RUZmxmSWN2SWt4QzFsNElCVUFKQm4xbitXalF1Cm42S3B0aGdMWmpqSnJvR09oYmNwSndGb0ZmdjBVUGdNN3VhWkg2TlhXZjA2K29PL0k0Zit1cDc5N2pGR0RQVGEKMlhJMUdlRWdCWDB3bVRSY29TTFNTNzJ1VjNNdDYxZEhDZnpWNEZ4YnRVeXhwOUtsd0JGMDF2ZlZjSW5aZVJ6YgpFcDBDS3d4SGJhRW5nTzFJY0dPVnpESlZKZ0Y4aGdDY1UxZG50SXhmMXlMM2RBWFg5R0RKY2pnL1liMU4yeWNRCkdxUHpqVUlqZitzQ0F3RUFBYU9CaURDQmhUQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBVEFNQmdOVkhSTUIKQWY4RUFqQUFNQjBHQTFVZERnUVdCQlRTZmF1RlkzNDJSaXpXM2NKMjBzN3h3QzhKT2pBZkJnTlZIU01FR0RBVwpnQlFpV1JsUG4zdGhMMWFXRGlKVEtwVmlWSTNBelRBZ0JnTlZIUkVFR1RBWGdoVmhjR2t0YVc1MExuTnVieTVzCllXSXViRzlqWVd3d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFDdENGZytyenlPaG1ML0tFeFV1QnBoK1hQYjkKckYzcVVDY2NMMVVmWTZDdEN4bkVDRCtCSnIyZmdLUWpEd05jcU5wWngvS29RWkN3ZmtRQll2LzZxNGNvTkFpSwpBM2NiVGZ6L0JpenRvNHN1S0IyM3UvbjYrb3hUMGhmdGQvMitxTGdDc1lYUlQwMVdvRCtRWDhyUzJNN3NialFTCnk5NFJyMTMzOStmYjJpMzBUQ2ZudXRoTmhBWVdTZzF5MDFtVzFPQUhNK3VXRWZ5WDhxQ2NmMjhpYU50TmpkaXcKRlBlMDMyV2ZyUHBLeW1acHg3VGYzUmN6MU9qWEdpNTc4cnRZWUtkNzVhTk54cVJOSGs4UzJ5OHR5bVJUYjRSOQpBM3F4dkVXN0N2ZDRkc1Boc09xY1B3clcwNmcvOGVVbHlMVG9zVkxTR0ZPc3A0VnlITlZPcmdKa0dGUT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/service-account.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/service-account.pub", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBME5LUDVpcVJYQTc0ZHNsaEY4djAKMFg4L0ZqbXVyVHFnKzNaajBobzh4UEtkZUpGUTlXTWwrN1cvVElwQWl1b2YxWkdTTno2S2JQSmFJLzIwdHFXUAp0U1AvVGlwb0NLcjV0MDJuNGdGV3l2b3NFcEdBdG1jalBMcEtBYmYzY0Vqc1l0QXUzVHlEVGdpTFduMXowNkxRCkcreUZSMGFKMEhQajhVQXh1Q3lzYzcwaU0vUFR2MUMySlgycUFOTG9vcFpVMTI0U3hudW9TQ2EvNU9sdkNYdjcKL2d0VkVFRHcwS3c3aFR3dWdNNEk2NnIrQXBHRVBDSG9scW4rQm56eVU5bjNXdm5qMWI0VmFwcTlIWUpJakFXUwpzaW0zOXU1M3ljNGhneXd2cmE1VXREbVVKOHUxSkRORzY5dDBOa0JZZjJKTTRDeTN2NnVlSHdvTEh1dWNLb3IrCjZRSURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/journal-gatewayd.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/journal-gatewayd.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURZRENDQWtpZ0F3SUJBZ0lJZEg3Y0xmOTBsS1F3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZeE1ESXhNelF4TmxvWApEVE0xTURZd09ESXhNelF5TUZvd09URWNNQm9HQTFVRUNoTVRUM0JsYmxOb2FXWjBJRUp2YjNSemRISmhjREVaCk1CY0dBMVVFQXhNUWFtOTFjbTVoYkMxbllYUmxkMkY1WkRDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVAKQURDQ0FRb0NnZ0VCQU5PWmUxWjU0bWdUMkxDOEVoK2F2WGgwY1NYWEFvS3orZ3BMd1dQLzdoTGd6K2ZJUDZWYgpha2pZbVo2aUhMMkdwdnpkdkRHTE9tMURCbUM0ZFErYzNqTFNCODQ4ME13RFovWjhUUk9uTGxubGVIWkQ2S2c1CmQwS3dCMWthV014RzJWWnhPNGJsT2x0WGpuci9xa2Z2VklrRlpjVFUyLytIdFlZK3V4THBsMmRSc2pKUXdqRUUKcFYzYUFJS2pWU3BaQlc1ZnZjOWQ0aTcyR2ROYUZ3WUVtMXFBeXkrMkZiL20zb3FiR1NLanI3M1BBd1BVTS9nUgpweE5yRHJ5OHFsTnhEVDB6eE1KQVd0cnAwdEZLRWloUUtkakUzVlpHREtzS2FnVkYwYzVqTzlNaW1jTnYyV0tkCkV6VENLMy9jeVhvQk5zYVE0VWZBTkRidEx0ZXBYZjJ1U3RVQ0F3RUFBYU4vTUgwd0RnWURWUjBQQVFIL0JBUUQKQWdXZ01CMEdBMVVkSlFRV01CUUdDQ3NHQVFVRkJ3TUJCZ2dyQmdFRkJRY0RBakFNQmdOVkhSTUJBZjhFQWpBQQpNQjBHQTFVZERnUVdCQlJXQVA1d1U4eFVmMVdSS3Y5K0Q5Nk5PV2pVd2pBZkJnTlZIU01FR0RBV2dCUWlXUmxQCm4zdGhMMWFXRGlKVEtwVmlWSTNBelRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQXA5VmRrR1RxeGZ5aDA4eUMKZ09tVlVvVmJPa1pJREwvV2F1MUFjRFZINVJiSk0vT2lCRTAyUi9ib1kxYStwT2MxdWN5SzBIUFhvQ04xaHRjVQpqLzhFUU9tazBuNmNEWXNPczM1UkdFS1lXQTFZWTJpNTkvaGNiaWMyeVpuM1kyaDI4c1FWTmhHODI0elBZKzJ2ClQrcC9sTXdxcHoxd0VQZEN6WUY3QWwvaTV4ekVVaEg3SjhEQW9aV2hBcDYvM3F6TTFFTG9RZkgrcVg4Z0RKVlQKdHlEZVBkeCtCbkdWdHlOOTdDYko1ZXF2YVhnWXhNRTRhazFrcjN5RzVrZzBobkV1eVQrVVBRcWVtNXVjdDg1QwpobnYxaWRzRHk3bkNTOE5WQ2k3cWh1cUt1S1Y1c2FWelVWdzNqU3ZiTmVjSVJ2SDA1dHpjcUNBSzN5c3JRcU5QCldBem53dz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/root-ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURFRENDQWZpZ0F3SUJBZ0lJWSs5bVpjcWJHUzB3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZeE1ESXhNelF4TmxvWApEVE0xTURZd09ESXhNelF4Tmxvd0pqRVNNQkFHQTFVRUN4TUpiM0JsYm5Ob2FXWjBNUkF3RGdZRFZRUURFd2R5CmIyOTBMV05oTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1S3RMVVYxZUh4MmkKR3h5Y0dnSkdYeGI3a3Q0a2pMYmN1WU8xK050VTJPL2NJUkhVNlZ0TlJSTDFIR3hPMG5UeWFMY0ZBcmU0Ym1UdAorV0N4dXBVcWwwbE9xb1dPT0lBc2RkS0M0NmZQdTdzZGNIRkp5SHF5UFlRNnFtbjJvS3RTeTFCbUFkNDgrWmEzCndWTWFvQUU1MS9zeXB2YnJQKy9xRVZaS3BMemk4c2xrWUhhT2lmdzgwSCs0NVlMcmR1ZGRMTmxVV21tTHdDYVoKcWIvSVA0T05SeFdMRUNSblYwNHp0QjFxNkFjRWIzK25FUVlUQjhrTU9WNzZsVkFxVU9EUkUvck51SFpKNWtONQpwc3pYeHN0TFRDL3I4SHUzL0ZCeUgrcmV4OWtHWDFBbW1MQUdkNWlCN3FLMG9GVWVBVjcwenBaZG41ampJb1VqCko0R0dxdUx4UHdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRFZSMFRBUUgvQkFVd0F3RUIKL3pBZEJnTlZIUTRFRmdRVUlsa1pUNTk3WVM5V2xnNGlVeXFWWWxTTndNMHdEUVlKS29aSWh2Y05BUUVMQlFBRApnZ0VCQUdBNmpTUkgwbEgwWExsMVZPaHhNZXdKSk9NeXBodUxUUXdsbTF5bjQ2SzNneGFxTlBTT1RWM0dhWTBLCmVNSmpxL2NBU3dGeklHL25BY010bjFybXN6QzJJbmlid3VLdW4yL2VnbXpRQzhyL2NicGNRTDFraVpoOU5GUnAKd0F2R1Z5cVFFbGR6ZWZzY203YTd2S0QxSUxCc3NlcUFOMkFIemNaYzVMV3JYL1F1NGt2aDM1UnV1bnlqVHpNRgpVVDVGRGdoM0x2UFIwbWZXZnpIN0pGOGR5RXJGWVBPKy8vVzJ3NkNvbEZCNkVYVWpIK0owaUJMZTR2Qkt0dDRlCmNsOGxBRjNxVVhrY05kQlFweGdSVlBxV1NoaUY5RHFTU1BrWXdLTE81YXVvR1VyUzJXODNybzU1MThkUHV3MmcKazNHZFdUVmsxYXpkaGdDdG5rQ3IvZjd3MEd3PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/bootstrap-in-place/bootstrap-in-place-post-reboot.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/bootstrap-in-place/master-update.fcc", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-in-place.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/install-to-disk.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1b0UgcGlwZWZhaWwgIyMgLUUgb3B0aW9uIHdpbGwgY2F1c2UgZnVuY3Rpb25zIHRvIGluaGVyaXQgdHJhcAoKIyBUaGlzIHNjcmlwdCBpcyBleGVjdXRlZCBieSBpbnN0YWxsLXRvLWRpc2sgc2VydmljZSB3aGVuIGluc3RhbGxpbmcgc2luZ2xlIG5vZGUgd2l0aCBib290c3RyYXAgaW4gcGxhY2UKCi4gL3Vzci9sb2NhbC9iaW4vYm9vdHN0cmFwLXNlcnZpY2UtcmVjb3JkLnNoCgpyZWNvcmRfc2VydmljZV9zdGFnZV9zdGFydCAid2FpdC1mb3ItYm9vdGt1YmUiCmVjaG8gIldhaXRpbmcgZm9yIC9vcHQvb3BlbnNoaWZ0Ly5ib290a3ViZS5kb25lIgp1bnRpbCBbIC1mIC9vcHQvb3BlbnNoaWZ0Ly5ib290a3ViZS5kb25lIF07IGRvCiAgc2xlZXAgNQpkb25lCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N1Y2Nlc3MKCmlmIFsgISAtZiBjb3Jlb3MtaW5zdGFsbGVyLmRvbmUgXTsgdGhlbgogIHJlY29yZF9zZXJ2aWNlX3N0YWdlX3N0YXJ0ICJjb3Jlb3MtaW5zdGFsbGVyIgogICMgV3JpdGUgaW1hZ2UgKyBpZ25pdGlvbiB0byBkaXNrCiAgZWNobyAiRXhlY3V0aW5nIGNvcmVvcy1pbnN0YWxsZXIgd2l0aCB0aGUgZm9sbG93aW5nIG9wdGlvbnM6IGluc3RhbGwgLWkgL29wdC9vcGVuc2hpZnQvbWFzdGVyLmlnbiAvZGV2L3ZkYSIKICBjb3Jlb3MtaW5zdGFsbGVyIGluc3RhbGwgLW4gLWkgL29wdC9vcGVuc2hpZnQvbWFzdGVyLmlnbiAvZGV2L3ZkYQoKICB0b3VjaCBjb3Jlb3MtaW5zdGFsbGVyLmRvbmUKICByZWNvcmRfc2VydmljZV9zdGFnZV9zdWNjZXNzCmZpCgpyZWNvcmRfc2VydmljZV9zdGFnZV9zdGFydCAicmVib290IgplY2hvICJHb2luZyB0byByZWJvb3QiCnNodXRkb3duIC1yICsxICJCb290c3RyYXAgY29tcGxldGVkLCBzZXJ2ZXIgaXMgZ29pbmcgdG8gcmVib290LiIKdG91Y2ggL29wdC9vcGVuc2hpZnQvLmluc3RhbGwtdG8tZGlzay5kb25lCmVjaG8gIkRvbmUiCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N1Y2Nlc3MK"}, "mode": 365}]}, "systemd": {"units": [{"contents": "[Unit]\nDescription=Approve CSRs during bootstrap phase\nWants=bootkube.service\nAfter=bootkube.service\n\n[Service]\nExecStart=/usr/local/bin/approve-csr.sh /opt/openshift/auth/kubeconfig-loopback\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "approve-csr.service"}, {"contents": "[Unit]\nDescription=Bootstrap a Kubernetes cluster\nRequires=crio-configure.service\nWants=kubelet.service\nAfter=kubelet.service crio-configure.service\nConditionPathExists=!/opt/openshift/.bootkube.done\n\n[Service]\nWorkingDirectory=/opt/openshift\nExecStart=/usr/local/bin/bootkube.sh\n\nRestart=on-failure\nRestartSec=5s\n", "name": "bootkube.service"}, {"contents": "[Unit]\nDescription=Configure CRI-O to use the pause image\nAfter=release-image.service\nRequires=release-image.service\nBefore=crio.service\n\n[Service]\nType=oneshot\nExecStart=/usr/local/bin/crio-configure.sh\nRemainAfterExit=true\n\n[Install]\nRequiredBy=crio.service\n", "name": "crio-configure.service"}, {"contents": "[Unit]\nDescription=Kubernetes Kubelet\nWants=rpc-statd.service crio.service release-image.service\nAfter=crio.service release-image.service\n[Service]\nType=notify\nNotifyAccess=all\nExecStartPre=/bin/mkdir --parents /etc/kubernetes/manifests\nExecStartPre=/bin/mkdir --parents /etc/kubernetes/kubelet-plugins/volume/exec\nExecStartPre=/usr/local/bin/kubelet-pause-image.sh\nEnvironment=KUBELET_RUNTIME_REQUEST_TIMEOUT=10m\nEnvironmentFile=-/etc/kubernetes/kubelet-env\nEnvironmentFile=-/etc/kubernetes/kubelet-pause-image-override\n\nExecStart=/usr/local/bin/kubelet.sh\n\nRestart=always\nRestartSec=10\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "kubelet.service"}, {"contents": "[Unit]\nDescription=Report the completion of the cluster bootstrap process\n# Workaround for https://github.com/systemd/systemd/issues/1312\nWants=bootkube.service\nAfter=bootkube.service\n\n[Service]\nExecStart=/usr/local/bin/report-progress.sh /opt/openshift/auth/kubeconfig\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "progress.service"}, {"contents": "", "name": "release-image-pivot.service"}, {"contents": "[Unit]\nDescription=Download the OpenShift Release Image\nWants=network-online.target\nAfter=network-online.target\n\n[Service]\nType=oneshot\nExecStart=/usr/local/bin/release-image-download.sh\nRemainAfterExit=true\n", "name": "release-image.service"}, {"dropins": [{"contents": "[Service]\nExecStart=\nExecStart=/usr/lib/systemd/systemd-journal-gatewayd \\\n  --key=/opt/openshift/tls/journal-gatewayd.key \\\n  --cert=/opt/openshift/tls/journal-gatewayd.crt \\\n  --trust=/opt/openshift/tls/root-ca.crt\n", "name": "certs.conf"}], "name": "systemd-journal-gatewayd.service"}, {"dropins": [{"contents": "[Unit]\nConditionPathExists=/enoent\n", "name": "okd-machine-os-disabled.conf"}], "name": "zincati.service"}, {"contents": "# In RHEL8 the service uses DynamicUser=yes; we need to work both ways, so hence\n# we hack this by adding the user if it doesn't exist and chown the file, rather\n# than doing it in Ignition.\n# https://github.com/openshift/installer/pull/1445\n[Unit]\nDescription=Change ownership of journal-gatewayd.key\nBefore=systemd-journal-gatewayd.service\n\n[Service]\nType=oneshot\nRemainAfterExit=yes\nExecStart=/bin/sh -c \". /usr/local/bin/bootstrap-service-record.sh; if ! getent passwd systemd-journal-gateway &>/dev/null; then useradd -r systemd-journal-gateway; fi && chown systemd-journal-gateway: /opt/openshift/tls/journal-gatewayd.{crt,key}\"\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "chown-gatewayd-key.service"}, {"contents": "", "enabled": true, "name": "systemd-journal-gatewayd.socket"}, {"contents": "[Unit]\nDescription=Install to disk\nRequires=bootkube.service\nWants=bootkube.service\nAfter=bootkube.service\nConditionPathExists=!/opt/openshift/.install-to-disk.done\n\n[Service]\nWorkingDirectory=/opt/openshift\nExecStart=/usr/local/bin/install-to-disk.sh\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target", "enabled": true, "name": "install-to-disk.service"}]}}