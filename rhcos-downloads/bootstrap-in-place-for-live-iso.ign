{"ignition": {"version": "3.2.0"}, "passwd": {"users": [{"name": "core", "sshAuthorizedKeys": ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCurg448sD+MZ<PERSON>Zo5LMTyDRQ/CdlFDS/x5wRFqoFCdFatcYhl/14Vy+W9WW6+yHo4yWYCIyQQdQajgfxybLJTB3Dq8owd+R4UpFjaZGrAuFPJqRYpcpbrR9zDiujgZEdADRX3nuSXJs5x7FbNtu7cfI+vwSw0FYMOXmO2HTQfPTaLT71EzTCcbk6zPAtEKnE5mTNQMNRsoFtJsa2xp/ayjX9FGq8CR3uuSgYOED7UfBzKXCSsHaiRCsYtvfOEVRKYel8DHEx+MvmemJjWwdq95YMoRMGZYaUUK6Sz3BdKK/4wqmayMFMkLeZu+HgJ34xvjuezksZMppfzEbF+T/S4gxwwled40oX2gW1IlG6mutqnbgu1cTfV9ezu68ccfSue9SQvf2Dy+IJvydHONuWd8x8H1eqRzJSySYNxtOta/0cMdtZbmkEBR3m94wkwAS+2zao1IWcBXOz/BeiMeB7fnL7pKuqybtprobt03A8f3sJwkYchlCfSq5cTXT2Bzc/I1BGxnqMRoXkJxKmTwZIV/0DuJOXjKV8G8aPUovfP5POyS0PmshVIt2srVLHZ2eInrcJilPzckxVN8XYM2syUx0p3eVUs308E7DtnRZgPF/7Jb+GKLryfKVin5EgAkLI15pOKPfs4ajXerV+/IctXnRFFEggze/1UR5OnjGA1GdYw== eduardo@MacBook-Pro\n", "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCuddb1JQn/5cJnFjhYYslXeV6tifKr62e4rTt09ncRDG7Xo7roIkLpLDZLBXvfYpNBlDUykZ6i5SM26xeufv+Zjhn/bSp3HOH4OCzbZtYjaE0AaLE9ue8IhN6c+kRLehz2PwpFtmkWAQkFeTnGBKMOlTe8ifjYa24GQWUkDQLJ27A0kZiY/yhEAsotFhOn3YmUJlkfES9NvT7FK6gEsMdNX9OgInBMTgixWnEEnc7sCHmtF/ksIuHs75nXsWI8JilLMcyajpPYHtO+SlyOQWC6/nZGcjnni8Rd+Xb2LeM5zvLUcjer2x6neEGXLGWOfbzYg/TKwerLhRaSYU3BSLG9\n"]}]}, "storage": {"files": [{"overwrite": true, "path": "/etc/containers/registries.conf", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/ignition-machine-config-encapsulated.json", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,ewogICJtZXRhZGF0YSI6IHsKICAgICJuYW1lIjogImJvb3RzdHJhcC1maXBzIgogIH0sCiAgInNwZWMiOiB7CiAgICAiY29uZmlnIjogewogICAgICAiaWduaXRpb24iOiB7CiAgICAgICAgInZlcnNpb24iOiAiMy4xLjAiCiAgICAgIH0KICAgIH0sCiAgICAia2VybmVsQXJndW1lbnRzIjogW10sCiAgICAiZmlwcyI6IGZhbHNlCiAgfQp9Cg=="}, "mode": 384}, {"overwrite": false, "path": "/etc/motd", "user": {"name": "root"}, "append": [{"source": "data:text/plain;charset=utf-8;base64,VGhpcyBpcyB0aGUgYm9vdHN0cmFwIG5vZGU7IGl0IHdpbGwgYmUgZGVzdHJveWVkIHdoZW4gdGhlIG1hc3RlciBpcyBmdWxseSB1cC4KClRoZSBwcmltYXJ5IHNlcnZpY2VzIGFyZSByZWxlYXNlLWltYWdlLnNlcnZpY2UgZm9sbG93ZWQgYnkgYm9vdGt1YmUuc2VydmljZS4gVG8gd2F0Y2ggdGhlaXIgc3RhdHVzLCBydW4gZS5nLgoKICBqb3VybmFsY3RsIC1iIC1mIC11IHJlbGVhc2UtaW1hZ2Uuc2VydmljZSAtdSBib290a3ViZS5zZXJ2aWNlCg=="}], "mode": 420}, {"overwrite": true, "path": "/etc/pki/ca-trust/source/anchors/ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/profile.d/proxy.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 384}, {"overwrite": true, "path": "/etc/systemd/system.conf.d/10-default-env.conf", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,W01hbmFnZXJdCg=="}, "mode": 384}, {"overwrite": true, "path": "/root/.docker/config.json", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/usr/local/bin/approve-csr.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MxMDkxICAjIHVzaW5nIHBhdGggb24gYm9vdHN0cmFwIG1hY2hpbmUKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCktVQkVDT05GSUc9IiR7MX0iCgplY2hvICJBcHByb3ZpbmcgYWxsIENTUiByZXF1ZXN0cyB1bnRpbCBib290c3RyYXBwaW5nIGlzIGNvbXBsZXRlLi4uIgp3aGlsZSBbICEgLWYgL29wdC9vcGVuc2hpZnQvLmJvb3RrdWJlLmRvbmUgXQpkbwogICAgb2MgLS1rdWJlY29uZmlnPSIkS1VCRUNPTkZJRyIgZ2V0IGNzciAtLW5vLWhlYWRlcnMgfCBncmVwIFBlbmRpbmcgfCBcCiAgICAgICAgYXdrICd7cHJpbnQgJDF9JyB8IFwKICAgICAgICB4YXJncyAtLW5vLXJ1bi1pZi1lbXB0eSBvYyAtLWt1YmVjb25maWc9IiRLVUJFQ09ORklHIiBhZG0gY2VydGlmaWNhdGUgYXBwcm92ZQoJc2xlZXAgMjAKZG9uZQo="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootkube.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-cluster-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-pivot.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-service-record.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAojIFRoaXMgbGlicmFyeSBwcm92aWRlcyBhIGhlbHBlciBmdW5jdGlvbnMgZm9yIHJlY29yZGluZyB3aGVuIGEgc2VydmljZQojIGFuZCBpdHMgc3RhZ2VzIHN0YXJ0IGFuZCBlbmQuCgojIyMKIyBXaGVuIHJ1bm5pbmcgYXMgYSBwcmUtIG9yIHBvc3QtY29tbWFuZCwgc2V0IFBSRV9DT01NQU5EIG9yIFBPU1RfQ09NTUFORCwgcmVzcGVjdGl2ZWx5LgojIFRoZXNlIG11c3QgYmUgc2V0ICpwcmlvciogdG8gc291cmNpbmcgdGhpcyBzY3JpcHQuCiMgUFJFX0NPTU1BTkQgaXMgdGhlIG5hbWUgaWRlbnRpZnlpbmcgdGhlIHByZS1jb21tYW5kIGJlaW5nIHJ1bi4KIyBQT1NUX0NPTU1BTkQgaXMgdGhlIG5hbWUgaWRlbnRpZnlpbmcgdGhlIHBvc3QtY29tbWFuZCBiZWluZyBydW4uCgojIFNFUlZJQ0VfUkVDT1JEU19ESVIgaXMgdGhlIGRpcmVjdG9yeSB1bmRlciB3aGljaCBzZXJ2aWNlIHJlY29yZHMgd2lsbCBiZSBzdG9yZWQuClNFUlZJQ0VfUkVDT1JEU19ESVI9IiR7U0VSVklDRV9SRUNPUkRTX0RJUjotL3Zhci9sb2cvb3BlbnNoaWZ0L30iCiMgU1lTVEVNRF9VTklUX05BTUUgaXMgdGhlIG5hbWUgb2YgdGhlIHN5c3RlbWQgdW5pdCBmb3IgdGhlIHNlcnZpY2UKU1lTVEVNRF9VTklUX05BTUU9IiQocHMgLW8gdW5pdD0gJCQpIgojIFNFUlZJQ0VfTkFNRSBpcyB0aGUgbmFtZSBvZiB0aGUgc2VydmljZQpTRVJWSUNFX05BTUU9IiR7U0VSVklDRV9OQU1FOi0ke1NZU1RFTURfVU5JVF9OQU1FJS5zZXJ2aWNlfX0iCgojIGFkZF9zZXJ2aWNlX3JlY29yZF9lbnRyeSBhZGRzIGEgcmVjb3JkIGVudHJ5IHRvIHRoZSBzZXJ2aWNlIHJlY29yZHMgZmlsZS4KIyAgIFBIQVNFIC0gcGhhc2UgYmVpbmcgcmVjb3JkZWQ7IG9uZSBvZiAic2VydmljZSBzdGFydCIsICJzZXJ2aWNlIGVuZCIsICJzdGFnZSBzdGFydCIsICJzdGFnZSBlbmQiLCAicHJlLWNvbW1hbmQgc3RhcnQiLAojICAgICAgICAgICAicHJlLWNvbW1hbmQgZW5kIiwgInBvc3QtY29tbWFuZCBzdGFydCIsICJwb3N0LWNvbW1hbmQgZW5kIgojICAgUkVTVUxUIC0gcmVzdWx0IG9mIHRoZSBhY3Rpb24KIyAgIFNUQUdFIChvcHRpb25hbCkgLSBzdGFnZSBvZiB0aGUgc2VydmljZQojICAgUFJFX0NPTU1BTkQgKG9wdGlvbmFsKSAtIG5hbWUgb2YgdGhlIHByZS1jb21tYW5kCiMgICBQT1NUX0NPTU1BTkQgKG9wdGlvbmFsKSAtIG5hbWUgb2YgdGhlIHBvc3QtY29tbWFuZAojICAgRVJST1JfTElORSAob3B0aW9uYWwpIC0gbGluZSB3aGVyZSB0aGUgZXJyb3Igb2NjdXJyZWQKIyAgIEVSUk9SX01FU1NBR0UgKG9wdGlvbmFsKSAtIG1lc3NhZ2UgZm9yIHRoZSBlcnJvcgphZGRfc2VydmljZV9yZWNvcmRfZW50cnkoKSB7CiAgbG9jYWwgRklMRU5BTUU9IiR7U0VSVklDRV9SRUNPUkRTX0RJUn0vJHtTRVJWSUNFX05BTUV9Lmpzb24iCiAgbWtkaXIgLS1wYXJlbnRzICIkKGRpcm5hbWUgIiR7RklMRU5BTUV9IikiCiAgIyBBcHBlbmQgdGhlIG5ldyBlbnRyeSB0byB0aGUgZXhpc3RpbmcgYXJyYXkgaW4gdGhlIGZpbGUuCiAgIyBJZiB0aGUgZmlsZSBkb2VzIG5vdCBhbHJlYWR5IGV4aXN0LCBzdGFydCB3aXRoIGFuIGVtcHR5IGFycmF5LgogICMgVGhlIG5ldyBlbnRyeSBjb250YWlucyBvbmx5IHRoZSBmaWVsZHMgdGhhdCBoYXZlIG5vbi1lbXB0eSB2YWx1ZXMsIHRvIG9taXQgb3B0aW9uYWwgdmFsdWVzIHRoYXQgd2VyZSBub3QgcHJvdmlkZWQuCiAgKFsgLWYgIiR7RklMRU5BTUV9IiBdICYmIGNhdCAiJHtGSUxFTkFNRX0iIHx8IGVjaG8gJ1tdJykgfCBcCiAgICAgIGpxIFwKICAgICAgICAtLWFyZyB0aW1lc3RhbXAgIiQoZGF0ZSArIiVZLSVtLSVkVCVIOiVNOiVTWiIpIiBcCiAgICAgICAgLS1hcmcgcHJlQ29tbWFuZCAiJHtQUkVfQ09NTUFORC19IiBcCiAgICAgICAgLS1hcmcgcG9zdENvbW1hbmQgIiR7UE9TVF9DT01NQU5ELX0iIFwKICAgICAgICAtLWFyZyBzdGFnZSAiJHtTVEFHRV9OQU1FLX0iIFwKICAgICAgICAtLWFyZyBwaGFzZSAiJHtQSEFTRX0iIFwKICAgICAgICAtLWFyZyByZXN1bHQgIiR7UkVTVUxULX0iIFwKICAgICAgICAtLWFyZyBlcnJvckxpbmUgIiR7RVJST1JfTElORS19IiBcCiAgICAgICAgLS1hcmcgZXJyb3JNZXNzYWdlICIke0VSUk9SX01FU1NBR0UtfSIgXAogICAgICAgICcuICs9IFsKICAgICAgICAgIHskdGltZXN0YW1wLCRwcmVDb21tYW5kLCRwb3N0Q29tbWFuZCwkc3RhZ2UsJHBoYXNlLCRyZXN1bHQsJGVycm9yTGluZSwkZXJyb3JNZXNzYWdlfSB8CiAgICAgICAgICByZWR1Y2Uga2V5c1tdIGFzICRrICguOyBpZiAuWyRrXSA9PSAiIiB0aGVuIGRlbCguWyRrXSkgZWxzZSAuIGVuZCkKICAgICAgICBdJyBcCiAgICAgID4gIiR7RklMRU5BTUV9LnRtcCIgJiYgXAogICAgbXYgIiR7RklMRU5BTUV9LnRtcCIgIiR7RklMRU5BTUV9Igp9CgojIHJlY29yZF9zZXJ2aWNlX3N0YXJ0KCkgcmVjb3JkcyB0aGUgc3RhcnQgb2YgYSBzZXJ2aWNlLgpyZWNvcmRfc2VydmljZV9zdGFydCgpIHsKICBpZiBbICIke1BSRV9DT01NQU5ELX0iIF0KICB0aGVuCiAgICBsb2NhbCBQSEFTRT0icHJlLWNvbW1hbmQgc3RhcnQiCiAgZWxpZiBbICIke1BPU1RfQ09NTUFORC19IiBdCiAgdGhlbgogICAgbG9jYWwgUEhBU0U9InBvc3QtY29tbWFuZCBzdGFydCIKICBlbHNlCiAgICBsb2NhbCBQSEFTRT0ic2VydmljZSBzdGFydCIKICBmaQoKICBhZGRfc2VydmljZV9yZWNvcmRfZW50cnkKfQoKIyByZWNvcmRfc2VydmljZV9lbmQocmVzdWx0KSByZWNvcmRzIHRoZSBlbmQgb2YgYSBzZXJ2aWNlLgojICAgRVJST1JfTElORSAtIGxpbmUgd2hlcmUgdGhlIGVycm9yIG9jY3VycmVkLCBpZiB0aGVyZSB3YXMgYW4gZXJyb3IKIyAgIEVSUk9SX01FU1NBR0UgLSBlcnJvciBtZXNzYWdlLCBpZiB0aGVyZSB3YXMgYW4gZXJyb3IKcmVjb3JkX3NlcnZpY2VfZW5kKCkgewogIGlmIFsgIiR7UFJFX0NPTU1BTkQtfSIgXQogIHRoZW4KICAgIGxvY2FsIFBIQVNFPSJwcmUtY29tbWFuZCBlbmQiCiAgZWxpZiBbICIke1BPU1RfQ09NTUFORC19IiBdCiAgdGhlbgogICAgbG9jYWwgUEhBU0U9InBvc3QtY29tbWFuZCBlbmQiCiAgZWxzZQogICAgbG9jYWwgUEhBU0U9InNlcnZpY2UgZW5kIgogIGZpCiAgbG9jYWwgUkVTVUxUPSR7MTo/TXVzdCBzcGVjaWZ5IGEgcmVzdWx0fQoKICBhZGRfc2VydmljZV9yZWNvcmRfZW50cnkKfQoKIyByZWNvcmRfc2VydmljZV9zdGFnZV9zdGFydChzdGFnZV9uYW1lKSByZWNvcmRzIHRoZSBzdGFydCBvZiBhIHN0YWdlIG9mIGEgc2VydmljZS4KcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3RhcnQoKSB7CiAgaWYgWyAiJHtTVEFHRV9OQU1FLX0iIF0KICB0aGVuCiAgICBlY2hvICJhdHRlbXB0IHRvIHJlY29yZCB0aGUgc3RhcnQgb2YgYSBzdGFnZSB3aXRob3V0IGVuZGluZyB0aGUgcHJldmlvdXMgb25lIgogICAgZXhpdCAxCiAgZmkKCiAgbG9jYWwgUEhBU0U9InN0YWdlIHN0YXJ0IgogIFNUQUdFX05BTUU9JHsxOj9NdXN0IHNwZWNpZnkgYSBzdGFnZSBuYW1lfQoKICBhZGRfc2VydmljZV9yZWNvcmRfZW50cnkKfQoKIyByZWNvcmRfc2VydmljZV9zdGFnZV9lbmQocmVzdWx0KSByZWNvcmRzIHRoZSBlbmQgIG9mIGEgc3RhZ2Ugb2YgYSBzZXJ2aWNlLgojICAgRVJST1JfTElORSAtIGxpbmUgd2hlcmUgdGhlIGVycm9yIG9jY3VycmVkLCBpZiB0aGVyZSB3YXMgYW4gZXJyb3IKIyAgIEVSUk9SX01FU1NBR0UgLSBlcnJvciBtZXNzYWdlLCBpZiB0aGVyZSB3YXMgYW4gZXJyb3IKcmVjb3JkX3NlcnZpY2Vfc3RhZ2VfZW5kKCkgewogIGlmIFsgLXogIiR7U1RBR0VfTkFNRS19IiBdCiAgdGhlbgogICAgZWNobyAiYXR0ZW1wdCB0byByZWNvcmQgdGhlIGVuZCBvZiBhIHN0YWdlIHdpdGhvdXQgc3RhcnRpbmcgb25lIgogICAgZXhpdCAxCiAgZmkKCiAgbG9jYWwgUEhBU0U9InN0YWdlIGVuZCIKICBsb2NhbCBSRVNVTFQ9JHsxOj9NdXN0IHNwZWNpZnkgYSByZXN1bHR9CgogIGFkZF9zZXJ2aWNlX3JlY29yZF9lbnRyeQoKICBTVEFHRV9OQU1FPSIiCn0KCiMgcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3VjY2VzcyByZWNvcmRzIHRoZSBzdWNjZXNzZnVsIGVuZCBvZiBhIHN0YWdlIG9mIGEgc2VydmljZS4KcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3VjY2VzcygpIHsKICByZWNvcmRfc2VydmljZV9zdGFnZV9lbmQgInN1Y2Nlc3MiCn0KCnJlY29yZF9zZXJ2aWNlX3N0YWdlX2ZhaWx1cmUoKSB7CiAgbG9jYWwgRVJST1JfTElORQogIGxvY2FsIEVSUk9SX01FU1NBR0UKICBnZXRfZXJyb3JfaW5mbyBFUlJPUl9MSU5FIEVSUk9SX01FU1NBR0UKICByZWNvcmRfc2VydmljZV9zdGFnZV9lbmQgImZhaWx1cmUiCn0KCnJlY29yZF9zZXJ2aWNlX2V4aXQoKSB7CiAgaWYgWyAiJDEiIC1lcSAwIF0KICB0aGVuCiAgICBsb2NhbCBSRVNVTFQ9InN1Y2Nlc3MiCiAgZWxzZQogICAgbG9jYWwgUkVTVUxUPSJmYWlsdXJlIgogICAgbG9jYWwgRVJST1JfTElORQogICAgbG9jYWwgRVJST1JfTUVTU0FHRQogICAgZ2V0X2Vycm9yX2luZm8gRVJST1JfTElORSBFUlJPUl9NRVNTQUdFCiAgZmkKCiAgaWYgWyAiJHtTVEFHRV9OQU1FLX0iIF0KICB0aGVuCiAgICByZWNvcmRfc2VydmljZV9zdGFnZV9lbmQgIiR7UkVTVUxUfSIKICBmaQoKICByZWNvcmRfc2VydmljZV9lbmQgIiR7UkVTVUxUfSIKfQoKZ2V0X2Vycm9yX2luZm8oKSB7CiAgbG9jYWwgLW4gZXJyb3JfbGluZT0kMQogIGxvY2FsIC1uIGVycm9yX21lc3NhZ2U9JDIKICAjIHNoZWxsY2hlY2sgZGlzYWJsZT1TQzIwMzQgICMgdmFyaWFibGUgdXNlZCBpbmRpcmVjdGx5CiAgZXJyb3JfbGluZT0iJChjYWxsZXIgMSkiCiAgIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MyMDM0ICAjIHZhcmlhYmxlIHVzZWQgaW5kaXJlY3RseQogIGVycm9yX21lc3NhZ2U9IiQoam91cm5hbGN0bCAtLXVuaXQ9IiR7U1lTVEVNRF9VTklUX05BTUV9IiAtLWxpbmVzPTMgLS1vdXRwdXQ9Y2F0KSIKfQoKcmVjb3JkX3NlcnZpY2Vfc3RhcnQKCnRyYXAgJ3JlY29yZF9zZXJ2aWNlX2V4aXQgJD8nIEVYSVQ="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-verify-api-server-urls.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/crio-configure.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1byBwaXBlZmFpbAojIEJlZm9yZSBrdWJlbGV0LnNlcnZpY2UgYW5kIGNyaW8uc2VydmljZSBzdGFydCwgZW5zdXJlCiMgdGhhdCB3ZSdyZSB1c2luZyB0aGUgcGF1c2UgaW1hZ2UgZnJvbSBvdXIgcGF5bG9hZCBqdXN0IGxpa2UgdGhlIHByaW1hcnkgY2x1c3Rlci4KIyBUaGUgY29uZmlnIHNob3VsZCBtYXRjaCB0aGUgb25lIGdlbmVyYXRlZCBieSB0aGUgTUNPIGlkZWFsbHk6CiMgaHR0cHM6Ly9naXRodWIuY29tL29wZW5zaGlmdC9tYWNoaW5lLWNvbmZpZy1vcGVyYXRvci9ibG9iL2U4NjFjY2IxMmYwOWM3Yzc2OGQ1MWZkZjBhMTc4NzlmY2M5YTg3ZDUvdGVtcGxhdGVzL21hc3Rlci8wMS1tYXN0ZXItY29udGFpbmVyLXJ1bnRpbWUvX2Jhc2UvZmlsZXMvY3Jpby55YW1sCiMgQnV0IGZvciBub3cgd2UncmUganVzdCBjaGFuZ2luZyB0aGUga2V5IGJpdHM6IGltYWdlIGFuZCBjb21tYW5kLgojIFBlcmhhcHMgZG93biB0aGUgbGluZSB3ZSBjaGFuZ2UgdGhpcyB0byBydW4gc29tZXRoaW5nIGxpa2U6CiMgcG9kbWFuIHJ1biBtYWNoaW5lLWNvbmZpZy1kYWVtb24gYm9vdHN0cmFwIC4uLiAocGFzc2luZyB0aGUgcmVsZWFzZSBpbWFnZSBhbmQgdGhlIGhvc3Qgcm9vdGZzKQoKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCi4gL3Vzci9sb2NhbC9iaW4vcmVsZWFzZS1pbWFnZS5zaAoKTUFDSElORV9DT05GSUdfSU5GUkFfSU1BR0U9JChpbWFnZV9mb3IgcG9kKQoKIyBtYWtlIHRoZSBkcm9wLWluIGRpcmVjdG9yeSBpZiB0aGF0IGhhc24ndCBiZWVuIGRvbmUgeWV0Cm1rZGlyIC1wIC9ldGMvY3Jpby9jcmlvLmNvbmYuZAoKY2F0IDw8RU9GID4gL2V0Yy9jcmlvL2NyaW8uY29uZi5kLzUwLWJvb3RzdHJhcC1vdmVycmlkZS5jb25mCltjcmlvXQpbY3Jpby5ydW50aW1lXQpob29rc19kaXIgPSBbCgkiL3Vzci9zaGFyZS9jb250YWluZXJzL29jaS9ob29rcy5kIiwKCSIvZXRjL2NvbnRhaW5lcnMvb2NpL2hvb2tzLmQiLApdCltjcmlvLmltYWdlXQpwYXVzZV9pbWFnZSA9ICIkTUFDSElORV9DT05GSUdfSU5GUkFfSU1BR0UiCnBhdXNlX2NvbW1hbmQgPSAiL3Vzci9iaW4vcG9kIgpFT0YK"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/installer-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/installer-masters-gather.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBHZXQgdGFyZ2V0IGFyY2hpdGVjdHVyZQphcmNoPSQodW5hbWUgLW0pCgppZiB0ZXN0ICJ4JHsxfSIgPSAneC0taWQnCnRoZW4KCUdBVEhFUl9JRD0iJHsyfSIKCXNoaWZ0IDIKZmkKCkFSVElGQUNUUz0iL3RtcC9hcnRpZmFjdHMtJHtHQVRIRVJfSUR9Igpta2RpciAtcCAiJHtBUlRJRkFDVFN9IgoKZWNobyAiR2F0aGVyaW5nIG1hc3RlciBzeXN0ZW1kIHN1bW1hcnkgLi4uIgpMQU5HPVBPU0lYIHN5c3RlbWN0bCBsaXN0LXVuaXRzIC0tc3RhdGU9ZmFpbGVkID4mICIke0FSVElGQUNUU30vZmFpbGVkLXVuaXRzLnR4dCIKCmVjaG8gIkdhdGhlcmluZyBtYXN0ZXIgZmFpbGVkIHN5c3RlbWQgdW5pdCBzdGF0dXMgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzIgpzZWQgLW4gJ3MvXlwqIFwoW14gXSpcKSAuKi9cMS9wJyA8ICIke0FSVElGQUNUU30vZmFpbGVkLXVuaXRzLnR4dCIgfCB3aGlsZSByZWFkIC1yIFVOSVQKZG8KICAgIHN5c3RlbWN0bCBzdGF0dXMgLS1mdWxsICIke1VOSVR9IiA+JiAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzLyR7VU5JVH0udHh0IgogICAgam91cm5hbGN0bCAtdSAiJHtVTklUfSIgPiAiJHtBUlRJRkFDVFN9L3VuaXQtc3RhdHVzLyR7VU5JVH0ubG9nIgpkb25lCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIGpvdXJuYWxzIC4uLiIKbWtkaXIgLXAgIiR7QVJUSUZBQ1RTfS9qb3VybmFscyIKZm9yIHNlcnZpY2UgaW4gY3JpbyBrdWJlbGV0IG1hY2hpbmUtY29uZmlnLWRhZW1vbi1ob3N0IG1hY2hpbmUtY29uZmlnLWRhZW1vbi1maXJzdGJvb3Qgb3BlbnNoaWZ0LWF6dXJlLXJvdXRlcyBvcGVuc2hpZnQtZ2NwLXJvdXRlcyBwaXZvdCBzc3NkCmRvCiAgICBqb3VybmFsY3RsIC0tYm9vdCAtLW5vLXBhZ2VyIC0tb3V0cHV0PXNob3J0IC0tdW5pdD0iJHtzZXJ2aWNlfSIgPiAiJHtBUlRJRkFDVFN9L2pvdXJuYWxzLyR7c2VydmljZX0ubG9nIgpkb25lCgpqb3VybmFsY3RsIC1vIHdpdGgtdW5pdCAtLW5vLXBhZ2VyIHwgZ3ppcCA+ICIke0FSVElGQUNUU30vam91cm5hbHMvam91cm5hbC5sb2cuZ3oiCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIG5ldHdvcmtpbmcgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L25ldHdvcmsiCmlwIGFkZHIgPiYgIiR7QVJUSUZBQ1RTfS9uZXR3b3JrL2lwLWFkZHIudHh0IgppcCByb3V0ZSA+JiAiJHtBUlRJRkFDVFN9L25ldHdvcmsvaXAtcm91dGUudHh0Igpob3N0bmFtZSA+JiAiJHtBUlRJRkFDVFN9L25ldHdvcmsvaG9zdG5hbWUudHh0IgpuZXRzdGF0IC1hbnAgPiYgIiR7QVJUSUZBQ1RTfS9uZXR3b3JrL25ldHN0YXQudHh0IgpjcCAtciAvZXRjL3Jlc29sdi5jb25mICIke0FSVElGQUNUU30vbmV0d29yay8iCgplY2hvICJHYXRoZXJpbmcgbWFzdGVyIGNvbnRhaW5lcnMgLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMiCmZvciBjb250YWluZXIgaW4gJChjcmljdGwgcHMgLS1hbGwgLS1xdWlldCkKZG8KICAgIGNvbnRhaW5lcl9uYW1lPSQoY3JpY3RsIHBzIC1hIC0taWQgIiR7Y29udGFpbmVyfSIgLXYgfCBncmVwIC1vUCAiTmFtZTogXFxLKC4qKSIpCiAgICBjcmljdGwgbG9ncyAiJHtjb250YWluZXJ9IiA+JiAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMvJHtjb250YWluZXJfbmFtZX0tJHtjb250YWluZXJ9LmxvZyIKICAgIGNyaWN0bCBpbnNwZWN0ICIke2NvbnRhaW5lcn0iID4mICIke0FSVElGQUNUU30vY29udGFpbmVycy8ke2NvbnRhaW5lcl9uYW1lfS0ke2NvbnRhaW5lcn0uaW5zcGVjdCIKZG9uZQoKcG9kbWFuIHBzIC0tYWxsIC0tZm9ybWF0ICJ7eyAuSUQgfX0ge3sgLk5hbWVzIH19IiB8IHdoaWxlIHJlYWQgLXIgY29udGFpbmVyX2lkIGNvbnRhaW5lcl9uYW1lCmRvCiAgICBwb2RtYW4gbG9ncyAiJHtjb250YWluZXJfaWR9IiA+JiAiJHtBUlRJRkFDVFN9L2NvbnRhaW5lcnMvJHtjb250YWluZXJfbmFtZX0tJHtjb250YWluZXJfaWR9LmxvZyIKICAgIHBvZG1hbiBpbnNwZWN0ICIke2NvbnRhaW5lcl9pZH0iID4mICIke0FSVElGQUNUU30vY29udGFpbmVycy8ke2NvbnRhaW5lcl9uYW1lfS0ke2NvbnRhaW5lcl9pZH0uaW5zcGVjdCIKZG9uZQoKZWNobyAiR2F0aGVyaW5nIG1hc3RlciBycG0tb3N0cmVlIGluZm8gLi4uIgpta2RpciAtcCAiJHtBUlRJRkFDVFN9L3JwbS1vc3RyZWUiCnN1ZG8gcnBtLW9zdHJlZSBzdGF0dXMgPiYgIiR7QVJUSUZBQ1RTfS9ycG0tb3N0cmVlL3N0YXR1cyIKc3VkbyBycG0tb3N0cmVlIGV4IGhpc3RvcnkgPiYgIiR7QVJUSUZBQ1RTfS9ycG0tb3N0cmVlL2hpc3RvcnkiCgojIENvbGxlY3Qgc3lzdGVtIGluZm9ybWF0aW9uIHNwZWNpZmljIHRvIElCTSBMaW51eCBaIChzMzkweCkgc3lzdGVtcy4gVGhlIGRiZ2luZm8KIyBzY3JpcHQgaXMgYXZhaWxhYmxlIGJ5IGRlZmF1bHQgYXMgcGFydCBvZiB0aGUgczM5MC11dGlscyBycG0gcGFja2FnZQppZiBbICIkYXJjaCIgPT0gInMzOTB4IiBdCnRoZW4KICAgIGVjaG8gIkdhdGhlcmluZyBkYmdpbmZvIGZvciB0aGUgczM5MHggc3lzdGVtIgogICAgbWtkaXIgLXAgIiR7QVJUSUZBQ1RTfS9ub2RlLWRiZ2luZm8iCiAgICAvdXNyL3NiaW4vZGJnaW5mby5zaCAtZCAiJHtBUlRJRkFDVFN9L25vZGUtZGJnaW5mbyIKICAgIGZpbmQgIiR7QVJUSUZBQ1RTfS9ub2RlLWRiZ2luZm8iIC1wcmludDAgfCB4YXJncyAtMCBjaG1vZCBhK3IKZmkKCmVjaG8gIldhaXRpbmcgZm9yIGxvZ3MgLi4uIgp3aGlsZSB3YWl0IC1uOyBkbyBqb2JzOyBkb25lCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/kubelet-pause-image.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1byBwaXBlZmFpbAojIEJlZm9yZSBrdWJlbGV0LnNlcnZpY2UgYW5kIGNyaW8uc2VydmljZSBzdGFydCwgZW5zdXJlCiMgdGhhdCB3ZSdyZSB1c2luZyB0aGUgcGF1c2UgaW1hZ2UgZnJvbSBvdXIgcGF5bG9hZCBqdXN0IGxpa2UgdGhlIHByaW1hcnkgY2x1c3Rlci4KIyBOZWVkIHRvIHNldCB0aGUgLS1wb2QtaW5mcmEtY29udGFpbmVyLWltYWdlIGZsYWcgZm9yIHRoZSBrdWJlbGV0IHRvIHBvaW50IHRvIHRoZSBwYXVzZSBpbWFnZSBmcm9tIHRoZSBwYXlsb2FkCiMgU28gd2UgYWRkIE1BQ0hJTkVfQ09ORklHX0lORlJBX0lNQUdFIHRvIGFuIGVudmlyb25tZW50IGZpbGUgYW5kIHNvdXJjZSB0aGF0IGluIHRoZSBrdWJlbGV0IHNlcnZpY2UKClBSRV9DT01NQU5EPSJrdWJlbGV0LXBhdXNlLWltYWdlIgouIC91c3IvbG9jYWwvYmluL2Jvb3RzdHJhcC1zZXJ2aWNlLXJlY29yZC5zaAoKLiAvdXNyL2xvY2FsL2Jpbi9yZWxlYXNlLWltYWdlLnNoCgplY2hvICJNQUNISU5FX0NPTkZJR19JTkZSQV9JTUFHRT0kKGltYWdlX2ZvciBwb2QpIiA+IC9ldGMva3ViZXJuZXRlcy9rdWJlbGV0LXBhdXNlLWltYWdlLW92ZXJyaWRlCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/kubelet.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIHNvdXJjZT1ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCiAvdXNyL2Jpbi9rdWJlbGV0IFwKICAgIC0tYW5vbnltb3VzLWF1dGg9ZmFsc2UgXAogICAgLS1jb250YWluZXItcnVudGltZS1lbmRwb2ludD0vdmFyL3J1bi9jcmlvL2NyaW8uc29jayBcCiAgICAtLXJ1bnRpbWUtcmVxdWVzdC10aW1lb3V0PSIke0tVQkVMRVRfUlVOVElNRV9SRVFVRVNUX1RJTUVPVVR9IiBcCiAgICAtLXBvZC1tYW5pZmVzdC1wYXRoPS9ldGMva3ViZXJuZXRlcy9tYW5pZmVzdHMgXAogICAgLS1taW5pbXVtLWNvbnRhaW5lci10dGwtZHVyYXRpb249Nm0wcyBcCiAgICAtLWNsdXN0ZXItZG9tYWluPWNsdXN0ZXIubG9jYWwgXAogICAgLS1jZ3JvdXAtZHJpdmVyPXN5c3RlbWQgXAogICAgLS1zZXJpYWxpemUtaW1hZ2UtcHVsbHM9ZmFsc2UgXAogICAgLS12PTIgXAogICAgLS12b2x1bWUtcGx1Z2luLWRpcj0vZXRjL2t1YmVybmV0ZXMva3ViZWxldC1wbHVnaW5zL3ZvbHVtZS9leGVjIFwKICAgIC0tcG9kLWluZnJhLWNvbnRhaW5lci1pbWFnZT0iJHtNQUNISU5FX0NPTkZJR19JTkZSQV9JTUFHRX0iCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/release-image-download.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/release-image.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAojIFRoaXMgbGlicmFyeSBwcm92aWRlcyBhbiBgaW1hZ2VfZm9yYCBoZWxwZXIgZnVuY3Rpb24gd2hpY2ggY2FuIGdldCB0aGUKIyBwdWxsIHNwZWMgZm9yIGEgc3BlY2lmaWMgaW1hZ2UgaW4gYSByZWxlYXNlLgoKIyBDb252ZXJ0IHRoZSByZWxlYXNlIGltYWdlIHB1bGwgc3BlYyB0byBhbiAiYWJzb2x1dGUiIGZvcm0gaWYgYSBkaWdlc3QgaXMgYXZhaWxhYmxlCmlmICEgUkVMRUFTRV9JTUFHRV9ESUdFU1Q9JCggcG9kbWFuIGluc3BlY3QgcXVheS5pby9vcGVuc2hpZnQtcmVsZWFzZS1kZXYvb2NwLXJlbGVhc2VAc2hhMjU2OmZiYWQ5MzFjNzI1YjJlNWI5MzdiMjk1YjU4MzQ1MzM0MzIyYmRhYmIwYjY3ZGExYzgwMGE1MzY4NmQ3Mzk3ZGEgLS1mb3JtYXQgJ3t7IGluZGV4IC5SZXBvRGlnZXN0cyAwIH19JyApIHx8IFtbIC16ICIke1JFTEVBU0VfSU1BR0VfRElHRVNUfSIgXV07IHRoZW4KCWVjaG8gIldhcm5pbmc6IENvdWxkIG5vdCByZXNvbHZlIHJlbGVhc2UgaW1hZ2UgdG8gcHVsbCBieSBkaWdlc3QiIDI+JjEKCVJFTEVBU0VfSU1BR0VfRElHRVNUPSJxdWF5LmlvL29wZW5zaGlmdC1yZWxlYXNlLWRldi9vY3AtcmVsZWFzZUBzaGEyNTY6ZmJhZDkzMWM3MjViMmU1YjkzN2IyOTViNTgzNDUzMzQzMjJiZGFiYjBiNjdkYTFjODAwYTUzNjg2ZDczOTdkYSIKZmkKCmltYWdlX2ZvcigpIHsKICAgIHBvZG1hbiBydW4gLS1xdWlldCAtLXJtIC0tbmV0PW5vbmUgIiR7UkVMRUFTRV9JTUFHRV9ESUdFU1R9IiBpbWFnZSAiJHsxfSIKfQo="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/report-progress.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaAoKIyBzaGVsbGNoZWNrIGRpc2FibGU9U0MxMDkxICAjIHVzaW5nIHBhdGggb24gYm9vdHN0cmFwIG1hY2hpbmUKLiAvdXNyL2xvY2FsL2Jpbi9ib290c3RyYXAtc2VydmljZS1yZWNvcmQuc2gKCiMgc2hlbGxjaGVjayBkaXNhYmxlPVNDMTA5MSAgIyB1c2luZyBwYXRoIG9uIGJvb3RzdHJhcCBtYWNoaW5lCi4gL3Vzci9sb2NhbC9iaW4vd2FpdC1mb3ItaGEtYXBpLnNoCgpLVUJFQ09ORklHPSIkezF9IgoKd2FpdF9mb3JfZXhpc3RlbmNlKCkgewoJd2hpbGUgWyAhIC1lICIkezF9IiBdCglkbwoJCXNsZWVwIDUKCWRvbmUKfQoKcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3RhcnQgIndhaXQtZm9yLWJvb3RzdHJhcC1jb21wbGV0ZSIKZWNobyAiV2FpdGluZyBmb3IgYm9vdHN0cmFwIHRvIGNvbXBsZXRlLi4uIgp3YWl0X2Zvcl9leGlzdGVuY2UgL29wdC9vcGVuc2hpZnQvLmJvb3RrdWJlLmRvbmUKcmVjb3JkX3NlcnZpY2Vfc3RhZ2Vfc3VjY2VzcwoKIyMgd2FpdCBmb3IgQVBJIHRvIGJlIGF2YWlsYWJsZQp3YWl0X2Zvcl9oYV9hcGkKCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N0YXJ0ICJyZXBvcnQtYm9vdHN0cmFwLWNvbXBsZXRlIgplY2hvICJSZXBvcnRpbmcgaW5zdGFsbCBwcm9ncmVzcy4uLiIKd2hpbGUgISBvYyAtLWt1YmVjb25maWc9IiRLVUJFQ09ORklHIiBjcmVhdGUgLWYgLSA8PC1FT0YKCWFwaVZlcnNpb246IHYxCglraW5kOiBDb25maWdNYXAKCW1ldGFkYXRhOgoJICBuYW1lOiBib290c3RyYXAKCSAgbmFtZXNwYWNlOiBrdWJlLXN5c3RlbQoJZGF0YToKCSAgc3RhdHVzOiBjb21wbGV0ZQpFT0YKZG8KCXNsZWVwIDUKZG9uZQpyZWNvcmRfc2VydmljZV9zdGFnZV9zdWNjZXNzCg=="}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/wait-for-ha-api.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-dns-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBETlMKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBjbHVzdGVyCnNwZWM6CiAgYmFzZURvbWFpbjogc25vLmxhYi5sb2NhbAogIHBsYXRmb3JtOgogICAgYXdzOiBudWxsCiAgICB0eXBlOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-infrastructure-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBJbmZyYXN0cnVjdHVyZQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoKICBjbG91ZENvbmZpZzoKICAgIG5hbWU6ICIiCiAgcGxhdGZvcm1TcGVjOgogICAgdHlwZTogTm9uZQpzdGF0dXM6CiAgYXBpU2VydmVySW50ZXJuYWxVUkk6IGh0dHBzOi8vYXBpLWludC5zbm8ubGFiLmxvY2FsOjY0NDMKICBhcGlTZXJ2ZXJVUkw6IGh0dHBzOi8vYXBpLnNuby5sYWIubG9jYWw6NjQ0MwogIGNvbnRyb2xQbGFuZVRvcG9sb2d5OiBTaW5nbGVSZXBsaWNhCiAgY3B1UGFydGl0aW9uaW5nOiBOb25lCiAgZXRjZERpc2NvdmVyeURvbWFpbjogIiIKICBpbmZyYXN0cnVjdHVyZU5hbWU6IHNuby14cHg2cwogIGluZnJhc3RydWN0dXJlVG9wb2xvZ3k6IFNpbmdsZVJlcGxpY2EKICBwbGF0Zm9ybTogTm9uZQogIHBsYXRmb3JtU3RhdHVzOgogICAgdHlwZTogTm9uZQo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-ingress-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBJbmdyZXNzCm1ldGFkYXRhOgogIGNyZWF0aW9uVGltZXN0YW1wOiBudWxsCiAgbmFtZTogY2x1c3RlcgpzcGVjOgogIGRvbWFpbjogYXBwcy5zbm8ubGFiLmxvY2FsCiAgbG9hZEJhbGFuY2VyOgogICAgcGxhdGZvcm06CiAgICAgIHR5cGU6ICIiCnN0YXR1czoKICBkZWZhdWx0UGxhY2VtZW50OiBDb250cm9sUGxhbmUK"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-network-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-proxy-01-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBQcm94eQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoKICB0cnVzdGVkQ0E6CiAgICBuYW1lOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cluster-scheduler-02-config.yml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBTY2hlZHVsZXIKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBjbHVzdGVyCnNwZWM6CiAgbWFzdGVyc1NjaGVkdWxhYmxlOiB0cnVlCiAgcG9saWN5OgogICAgbmFtZTogIiIKICBwcm9maWxlQ3VzdG9taXphdGlvbnM6CiAgICBkeW5hbWljUmVzb3VyY2VBbGxvY2F0aW9uOiAiIgpzdGF0dXM6IHt9Cg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/cvo-overrides.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/kube-cloud-config.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKa2luZDogU2VjcmV0Cm1ldGFkYXRhOgogIG5hbWU6IGt1YmUtY2xvdWQtY2ZnCiAgbmFtZXNwYWNlOiBrdWJlLXN5c3RlbQp0eXBlOiBPcGFxdWUKZGF0YToKICBjb25maWc6ICIiCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/kube-system-configmap-root-ca.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKa2luZDogQ29uZmlnTWFwCm1ldGFkYXRhOgogIG5hbWU6IHJvb3QtY2EKICBuYW1lc3BhY2U6IGt1YmUtc3lzdGVtCmRhdGE6CiAgY2EuY3J0OiB8CiAgICAtLS0tLUJFR0lOIENFUlRJRklDQVRFLS0tLS0KICAgIE1JSURFRENDQWZpZ0F3SUJBZ0lJZWlEaklQTGF5TGt3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKICAgIEN4TUpiM0JsYm5Ob2FXWjBNUkF3RGdZRFZRUURFd2R5YjI5MExXTmhNQjRYRFRJMU1EWXdOakU0TURZeE9Gb1gKICAgIERUTTFNRFl3TkRFNE1EWXhPRm93SmpFU01CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHkKICAgIGIyOTBMV05oTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3cXZhM0tOUzc5bGsKICAgIHArYkZwK3N3SFdLU1hTNVAxZnFIWC9YNGFRL2FWczlFUk13ZE9MeHYrSno1RnFla0hHOUhwalN1U1YyZnNoTTMKICAgIGFFa3lhOG4renJCT1dHQ0NQQ1YyWUhtNFQwTVJ3TTlOUUV2ZjRyNkhIcFBjbThycFpxTVdPSjlsVDgxYmpQaU4KICAgIEt5UVRyZEpxZlVMZHFWZXdUY0VUN2VGSUwwZ0dyVFE4VFpxSE0zZUpWSU1Qdk9aeTFocE40TXUvU2JkbElLQmUKICAgIEJZZ0oxeGZTQk1CTUF1MC9HT3E0SmNoclRocXdFQWVLNExKOUt3T1UwdnAzaWQwRk5PV25nZ2pKQjFpdzZTcngKICAgIHd4Y2U3V0h3UHNyNXEzQllFcWs0RnpwZjVMVlcwQnEvc2VFRzdTUEh6SjdERXJZM1d0T0xReG15aFRmbVlkVSsKICAgIFVrNjh3TUhUbXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRFZSMFRBUUgvQkFVd0F3RUIKICAgIC96QWRCZ05WSFE0RUZnUVVUSzJYZEJEMUI0K0NQQitldXpvZVg3L2NKcWd3RFFZSktvWklodmNOQVFFTEJRQUQKICAgIGdnRUJBTzVLZ2hmcjV3YmVlSGRVeWVwcjFlTE5QeCtIRml3bkJuUUhwQ2lKMHZ1U05RdU9aalUzWCtnemRRM1YKICAgIEtoL2ZZdENqRnlCTEFWdm5yd3hNcnJaNWxiS0F0ckZzTG95b0FDQVh0blk2UVB2aTBlbFVkVjVndEtPbXdteUsKICAgIEVlZzdSOXc4dlg0RUJ1YXdKLzh3L3h5RmtlcWlVWUJCRE83YWt1VlQwS1VUdnZvNnJEM0lhWlNQeXM5SjU1UFoKICAgIFJidFRiS3pRQlozdlpySElVL2YyUnliTDJCeWlUMzFXOWtjRHVIckJqbjdLWjVIdGFWanN1TDVlL29tb21vUUIKICAgIHlxbjJPcnhjZ3NwSEZuOEFwMXd5Ujl2N216SlA5ZXBZUmdHMEl5Wk0zcVlGU1RRSW9TSDczOEFldkp6b2doTXcKICAgIGRjemVoZCtpYkpDd2xVcWJEQUFJdjRPdURqaz0KICAgIC0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0KICAgIAo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/machine-config-server-tls-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/manifests/openshift-config-secret-pull-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_feature-gate.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogY29uZmlnLm9wZW5zaGlmdC5pby92MQpraW5kOiBGZWF0dXJlR2F0ZQptZXRhZGF0YToKICBjcmVhdGlvblRpbWVzdGFtcDogbnVsbAogIG5hbWU6IGNsdXN0ZXIKc3BlYzoge30Kc3RhdHVzOgogIGZlYXR1cmVHYXRlczogbnVsbAo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_kubeadmin-password-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKZGF0YToKICBrdWJlYWRtaW46IEpESmhKREV3SkRKS1ZrVm9VRzUwYVdkdWRHbFpSVXAzVVdWcGVrOVRTbGxrUTBORVdUQmpaRTExT1ZCRWEzRTBiVGxFVUVvMmJFRmpVMEpICmtpbmQ6IFNlY3JldAptZXRhZGF0YToKICBuYW1lc3BhY2U6IGt1YmUtc3lzdGVtCiAgbmFtZToga3ViZWFkbWluCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/openshift-install-manifests.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogdjEKZGF0YToKICBpbnZva2VyOiB1c2VyCiAgdmVyc2lvbjogdjQuMTcuMApraW5kOiBDb25maWdNYXAKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBuYW1lOiBvcGVuc2hpZnQtaW5zdGFsbC1tYW5pZmVzdHMKICBuYW1lc3BhY2U6IG9wZW5zaGlmdC1jb25maWcK"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-cluster-api_master-user-data-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-machineconfig_99-master-ssh.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogbWFjaGluZWNvbmZpZ3VyYXRpb24ub3BlbnNoaWZ0LmlvL3YxCmtpbmQ6IE1hY2hpbmVDb25maWcKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBsYWJlbHM6CiAgICBtYWNoaW5lY29uZmlndXJhdGlvbi5vcGVuc2hpZnQuaW8vcm9sZTogbWFzdGVyCiAgbmFtZTogOTktbWFzdGVyLXNzaApzcGVjOgogIGJhc2VPU0V4dGVuc2lvbnNDb250YWluZXJJbWFnZTogIiIKICBjb25maWc6CiAgICBpZ25pdGlvbjoKICAgICAgdmVyc2lvbjogMy4yLjAKICAgIHBhc3N3ZDoKICAgICAgdXNlcnM6CiAgICAgIC0gbmFtZTogY29yZQogICAgICAgIHNzaEF1dGhvcml6ZWRLZXlzOgogICAgICAgIC0gfAogICAgICAgICAgc3NoLXJzYSBBQUFBQjNOemFDMXljMkVBQUFBREFRQUJBQUFDQVFDdXJnNDQ4c0QrTVpQWm81TE1UeURSUS9DZGxGRFMveDV3UkZxb0ZDZEZhdGNZaGwvMTRWeStXOVdXNit5SG80eVdZQ0l5UVFkUWFqZ2Z4eWJMSlRCM0RxOG93ZCtSNFVwRmphWkdyQXVGUEpxUllwY3BiclI5ekRpdWpnWkVkQURSWDNudVNYSnM1eDdGYk50dTdjZkkrdndTdzBGWU1PWG1PMkhUUWZQVGFMVDcxRXpUQ2NiazZ6UEF0RUtuRTVtVE5RTU5Sc29GdEpzYTJ4cC9heWpYOUZHcThDUjN1dVNnWU9FRDdVZkJ6S1hDU3NIYWlSQ3NZdHZmT0VWUktZZWw4REhFeCtNdm1lbUpqV3dkcTk1WU1vUk1HWllhVVVLNlN6M0JkS0svNHdxbWF5TUZNa0xlWnUrSGdKMzR4dmp1ZXprc1pNcHBmekViRitUL1M0Z3h3d2xlZDQwb1gyZ1cxSWxHNm11dHFuYmd1MWNUZlY5ZXp1NjhjY2ZTdWU5U1F2ZjJEeStJSnZ5ZEhPTnVXZDh4OEgxZXFSekpTeVNZTnh0T3RhLzBjTWR0WmJta0VCUjNtOTR3a3dBUysyemFvMUlXY0JYT3ovQmVpTWVCN2ZuTDdwS3VxeWJ0cHJvYnQwM0E4ZjNzSndrWWNobENmU3E1Y1RYVDJCemMvSTFCR3hucU1Sb1hrSnhLbVR3WklWLzBEdUpPWGpLVjhHOGFQVW92ZlA1UE95UzBQbXNoVkl0MnNyVkxIWjJlSW5yY0ppbFB6Y2t4Vk44WFlNMnN5VXgwcDNlVlVzMzA4RTdEdG5SWmdQRi83SmIrR0tMcnlmS1ZpbjVFZ0FrTEkxNXBPS1BmczRhalhlclYrL0ljdFhuUkZGRWdnemUvMVVSNU9uakdBMUdkWXc9PSBlZHVhcmRvQE1hY0Jvb2stUHJvCiAgZXh0ZW5zaW9uczogbnVsbAogIGZpcHM6IGZhbHNlCiAga2VybmVsQXJndW1lbnRzOiBudWxsCiAga2VybmVsVHlwZTogIiIKICBvc0ltYWdlVVJMOiAiIgo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-cluster-api_worker-user-data-secret.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/openshift/99_openshift-machineconfig_99-worker-ssh.yaml", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,YXBpVmVyc2lvbjogbWFjaGluZWNvbmZpZ3VyYXRpb24ub3BlbnNoaWZ0LmlvL3YxCmtpbmQ6IE1hY2hpbmVDb25maWcKbWV0YWRhdGE6CiAgY3JlYXRpb25UaW1lc3RhbXA6IG51bGwKICBsYWJlbHM6CiAgICBtYWNoaW5lY29uZmlndXJhdGlvbi5vcGVuc2hpZnQuaW8vcm9sZTogd29ya2VyCiAgbmFtZTogOTktd29ya2VyLXNzaApzcGVjOgogIGJhc2VPU0V4dGVuc2lvbnNDb250YWluZXJJbWFnZTogIiIKICBjb25maWc6CiAgICBpZ25pdGlvbjoKICAgICAgdmVyc2lvbjogMy4yLjAKICAgIHBhc3N3ZDoKICAgICAgdXNlcnM6CiAgICAgIC0gbmFtZTogY29yZQogICAgICAgIHNzaEF1dGhvcml6ZWRLZXlzOgogICAgICAgIC0gfAogICAgICAgICAgc3NoLXJzYSBBQUFBQjNOemFDMXljMkVBQUFBREFRQUJBQUFDQVFDdXJnNDQ4c0QrTVpQWm81TE1UeURSUS9DZGxGRFMveDV3UkZxb0ZDZEZhdGNZaGwvMTRWeStXOVdXNit5SG80eVdZQ0l5UVFkUWFqZ2Z4eWJMSlRCM0RxOG93ZCtSNFVwRmphWkdyQXVGUEpxUllwY3BiclI5ekRpdWpnWkVkQURSWDNudVNYSnM1eDdGYk50dTdjZkkrdndTdzBGWU1PWG1PMkhUUWZQVGFMVDcxRXpUQ2NiazZ6UEF0RUtuRTVtVE5RTU5Sc29GdEpzYTJ4cC9heWpYOUZHcThDUjN1dVNnWU9FRDdVZkJ6S1hDU3NIYWlSQ3NZdHZmT0VWUktZZWw4REhFeCtNdm1lbUpqV3dkcTk1WU1vUk1HWllhVVVLNlN6M0JkS0svNHdxbWF5TUZNa0xlWnUrSGdKMzR4dmp1ZXprc1pNcHBmekViRitUL1M0Z3h3d2xlZDQwb1gyZ1cxSWxHNm11dHFuYmd1MWNUZlY5ZXp1NjhjY2ZTdWU5U1F2ZjJEeStJSnZ5ZEhPTnVXZDh4OEgxZXFSekpTeVNZTnh0T3RhLzBjTWR0WmJta0VCUjNtOTR3a3dBUysyemFvMUlXY0JYT3ovQmVpTWVCN2ZuTDdwS3VxeWJ0cHJvYnQwM0E4ZjNzSndrWWNobENmU3E1Y1RYVDJCemMvSTFCR3hucU1Sb1hrSnhLbVR3WklWLzBEdUpPWGpLVjhHOGFQVW92ZlA1UE95UzBQbXNoVkl0MnNyVkxIWjJlSW5yY0ppbFB6Y2t4Vk44WFlNMnN5VXgwcDNlVlVzMzA4RTdEdG5SWmdQRi83SmIrR0tMcnlmS1ZpbjVFZ0FrTEkxNXBPS1BmczRhalhlclYrL0ljdFhuUkZGRWdnemUvMVVSNU9uakdBMUdkWXc9PSBlZHVhcmRvQE1hY0Jvb2stUHJvCiAgZXh0ZW5zaW9uczogbnVsbAogIGZpcHM6IGZhbHNlCiAga2VybmVsQXJndW1lbnRzOiBudWxsCiAga2VybmVsVHlwZTogIiIKICBvc0ltYWdlVVJMOiAiIgo="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/original_cvo_overrides.patch", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,eyJzcGVjIjp7Im92ZXJyaWRlcyI6bnVsbH19"}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig-kubelet", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/auth/kubeconfig-loopback", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/admin-kubeconfig-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNRENDQWhpZ0F3SUJBZ0lJT2krdnBCamxvVVF3RFFZSktvWklodmNOQVFFTEJRQXdOakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TQXdIZ1lEVlFRREV4ZGhaRzFwYmkxcmRXSmxZMjl1Wm1sbkxYTnBaMjVsY2pBZQpGdzB5TlRBMk1EWXhPREEyTVRaYUZ3MHpOVEEyTURReE9EQTJNVFphTURZeEVqQVFCZ05WQkFzVENXOXdaVzV6CmFHbG1kREVnTUI0R0ExVUVBeE1YWVdSdGFXNHRhM1ZpWldOdmJtWnBaeTF6YVdkdVpYSXdnZ0VpTUEwR0NTcUcKU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRHNNSHhEd3FjMXU0bEltMTBmSi9LYTFhTFBLaWp3bHZzVQpDVUNTQUNxb25SY1ovcjVqUnlGTUpxR0tOMmM3b1RPMmJGZS9rMGwzRGVIZFJOcllOVGlSa3BVRlNNVWNkNUcvCndXNWpwZkl4VmxFVmxldDI2Vkkzak01a3UvOTVNZE9Zc3UxWGZNQVk0SktablZSK0FnTWVHdFhnQUpuYytpS2gKTmU3YkFNYkhsMmZ6aWRVYmp3SE5QQlA0MnU2QTBpSkVCY1J0VlhSaURUTCtTVzI0SDl0bnZJTmhHU2pjNXNUagpZOU1xZGRWTkJ2TnV4eGVrY2ZxdnFjem0zRFVzTkRjTWg4K2loMk9lUVcweWY1bEYwUEdGVXlOc1JpTnUrVlVOCks0RVUza0F4MjJXY29za2h5WFIzaU5tSWhrQTJCcGtWak9HcFc3enF3UnpPYS9PSnUxdXhBZ01CQUFHalFqQkEKTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlRuWHF4NwpiLy9BK29XWU5BV05kZTBtMnFQdHV6QU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFVOUg3Kzk1dWxUQ2p0Qlc0CkZONnFPWVF1cEpUTGdEK081RkNWK24xLzJ0aHR3YjJuNU1Ed1NzRGlpWTd5N3cvQnNCTTJqanJOR1JjeGFYVVMKcmdJcThVNE5BL2pBd2cwenNUSzRSM1RwclIwQWNmQW9hdWNERXl2WVBCYklWNWRUTkRnOEhZUFo1ZmJYZkVYTgp0VWRjbjJCWlVxTklKejNkTjBDaS9qU3M3Wi9rT0dxSUFnblVEck8zRlBjdDFWbStucGZmajF1bnluYk0xTUZICm9WVWFvYzU5L3BiOFJMR1dxMksrWTNJc1BpeWFlZGRrWVBnRDU1S2JSek9vNlZtM0FhRjJqbS9LckE4RXJObjQKams5TEkvSWtvY1Y3aGt3K0c1UmlhR245bWJ0QUIxQkl6ck5LdDREc0pUOW42R3kzSzQ2Y3I0dERraGVoaFVwMgowTEVsNGc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURGRENDQWZ5Z0F3SUJBZ0lJRkx2K0pVS3hISU13RFFZSktvWklodmNOQVFFTEJRQXdLREVSTUE4R0ExVUUKQ3hNSVltOXZkR3QxWW1VeEV6QVJCZ05WQkFNVENtRm5aM0psWjJGMGIzSXdIaGNOTWpVd05qQTJNVGd3TmpFNQpXaGNOTWpVd05qQTNNVGd3TmpFNVdqQW9NUkV3RHdZRFZRUUxFd2hpYjI5MGEzVmlaVEVUTUJFR0ExVUVBeE1LCllXZG5jbVZuWVhSdmNqQ0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU44Y3ZCZVEKMnMvaTN1RFRMbWV5MElCRW1ZcUdoQzNjS210Y1dOVUNJUklQNy80UlE2QlRVQ2JFVDZIa2tsT1VtWjd1K3JKTQp4TjViOTM0OXpZZjM4dHY1KzZCWmVISGZnWXlpbGNwZ0NzYUpuQlYxV0Ura3RVVno2RXBvRVVVQ0RteHN5QzJQCldEaWJ4aGdnUGtQcHNDWC9YZFVIb3RnRTlIaDVJdWhhNVovTEJrMlZONkx1bW9SazVxWEpNTGw1ZlE5S3JiOTcKM0p0MlBEMzBHY2YvUmdUZTRwU1VPL091MzNJeUdLRlJYTkZmUTMxc1VBMEJRbUFSekdDeUV5R2IvUU43Z2FvTApqQ3NNcWpaWHExbnU4dFA3RytRQzRUQkJGdkZ1MnFack1vamVxZXo5TE56ZEpFKzRIVW5JaGk0MDdnOE5ScTRRCk9DTVBzN25abzVXbjZwc0NBd0VBQWFOQ01FQXdEZ1lEVlIwUEFRSC9CQVFEQWdLa01BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdIUVlEVlIwT0JCWUVGSkR2ellwNWIxeHFVeC9weTBUVVFVbFFvZy9KTUEwR0NTcUdTSWIzRFFFQgpDd1VBQTRJQkFRRGRTMWRtbmhCaGpvYUtOb0NRU09XTDRoSnovMndxWFlKbkdUTzA2UU51REdUU25SVWpzSzZmCmV6YzVVNnNIYzllMVBvejBRbVZjNEFpTlVTVFMyMEl0WG5wbkM4dy9ZZTRCM2pST2g1U2llQkNWYWRLZFl4RTQKVktIaHhkUUdBbmVueDVnVDBUNHRnVnFleEVzc1M0Zm56WnBvVGlNSkkwNWtjTGNaU1BkN0xwMTVtS0k4dFFqMgo1bVRwR1BBUkpRRFZOQjNLaE81YTlSSVkzazFlU0toRTNEcTVvU0JsOGNGZml1K0duNm0xVnh2R3Joc1ZWNjArCkkyYnhzV0Z5MUpURjJ6Zmpoa2xKWXFIc2hhVmRTS0dFK3BOQk1FV212ZkNWSW1OY1VSRjBHQXI1VlhGY0dqWUsKTFZGTEdjNVJBNzlGaGN5WkhTSEQyMmFZVDJqclVyYzAKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKRENDQWd5Z0F3SUJBZ0lJTEN0QTE5eFpKWGt3RFFZSktvWklodmNOQVFFTEJRQXdNREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Sb3dHQVlEVlFRREV4RmhaMmR5WldkaGRHOXlMWE5wWjI1bGNqQWVGdzB5TlRBMgpNRFl4T0RBMk1UbGFGdzB5TlRBMk1EY3hPREEyTVRsYU1EQXhFakFRQmdOVkJBc1RDVzl3Wlc1emFHbG1kREVhCk1CZ0dBMVVFQXhNUllXZG5jbVZuWVhSdmNpMXphV2R1WlhJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFEdnRRR2RaWGppVnZWTjFnYTZWMk9KNU40ZkdOZy8yMkU1YVFaMHN6cnFtaEkrTmdMQwpiVTlpYXkxbXpRTm1POTRBaVdtVkw2SWx2L3kvdlNNZ2xuRUM5UkF4Q0lqOFZUcFZnR2UxV2ZtejZIcFVXQitNCkRjUnZDSjB0YWVXUmNDTUtlRGUxUkxPWHZ6MWNsZFNLcWlpR3VabG92UWJFd25FMlNrK0ljcWhPZ1cyaWU1VGIKNkxFOWUycmZ3NzJQaXdPM2ttUkhUaFU5dkx1T1RYTXNVN2p1N0UxT1d4R05HSzZ0QTdRQy94azk3eUxWWER1dApXbWFzaHQxU202RjljQ2VmMGtoYXFmbHg0UmV6VDhjNWRFVjhEOWtLdHo1TDZwV2tSd0gveUVBR3JSdDJvY3N3ClRYd2hYWU9ONEZFZDkxRXhmZ21hSUxuOUduTmpKL0VYL28vZkFnTUJBQUdqUWpCQU1BNEdBMVVkRHdFQi93UUUKQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCVENQdDFIYTJla3dzcDlPSWhiNzlaMApHSXdqUkRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWRhS21LMCtzT0xvSURGV2dVamhTSjQ2N2lzc01wUlA2Cjc5SE5oTzE2L0dUVzByaFQxcVhuUHBybi9BQlV6UElCbnZ0dG9tSHhCMnNUL1ExUDVIdDJtODJ5dU5ieFVjNjcKVVMveEpLSDFLNmdyOTJYYTcvRnQ5eG8yK3hoY1lucjNtc1NvenhxVEhhOXJlZVRDYVl5REtlOXJUV1BqR2liRwo5VmZqK280ZlFKcndxd2sxMmdYclBiUlBTMjllQTYxd2tZZklHREE2NzJvLzREVmdFYm04RVoxYjBzTzZqK0h0CkFPb2g5RitMSk4yalFtTnlUVUdSWHZuRXVac1FsNGw3ZFIvSWdGZGFYdEpPcXA4MTZ3ZC9xSFdZbmUyeWhvOWcKOGlwMklqRy9NbGNvWXJPUUg5dHVWNjZSSHppa0JKWG9zQ0V4am1HR3ltN3VJcXpVUjE5SWtnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/aggregator-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKRENDQWd5Z0F3SUJBZ0lJTEN0QTE5eFpKWGt3RFFZSktvWklodmNOQVFFTEJRQXdNREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Sb3dHQVlEVlFRREV4RmhaMmR5WldkaGRHOXlMWE5wWjI1bGNqQWVGdzB5TlRBMgpNRFl4T0RBMk1UbGFGdzB5TlRBMk1EY3hPREEyTVRsYU1EQXhFakFRQmdOVkJBc1RDVzl3Wlc1emFHbG1kREVhCk1CZ0dBMVVFQXhNUllXZG5jbVZuWVhSdmNpMXphV2R1WlhJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFEdnRRR2RaWGppVnZWTjFnYTZWMk9KNU40ZkdOZy8yMkU1YVFaMHN6cnFtaEkrTmdMQwpiVTlpYXkxbXpRTm1POTRBaVdtVkw2SWx2L3kvdlNNZ2xuRUM5UkF4Q0lqOFZUcFZnR2UxV2ZtejZIcFVXQitNCkRjUnZDSjB0YWVXUmNDTUtlRGUxUkxPWHZ6MWNsZFNLcWlpR3VabG92UWJFd25FMlNrK0ljcWhPZ1cyaWU1VGIKNkxFOWUycmZ3NzJQaXdPM2ttUkhUaFU5dkx1T1RYTXNVN2p1N0UxT1d4R05HSzZ0QTdRQy94azk3eUxWWER1dApXbWFzaHQxU202RjljQ2VmMGtoYXFmbHg0UmV6VDhjNWRFVjhEOWtLdHo1TDZwV2tSd0gveUVBR3JSdDJvY3N3ClRYd2hYWU9ONEZFZDkxRXhmZ21hSUxuOUduTmpKL0VYL28vZkFnTUJBQUdqUWpCQU1BNEdBMVVkRHdFQi93UUUKQXdJQ3BEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCVENQdDFIYTJla3dzcDlPSWhiNzlaMApHSXdqUkRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWRhS21LMCtzT0xvSURGV2dVamhTSjQ2N2lzc01wUlA2Cjc5SE5oTzE2L0dUVzByaFQxcVhuUHBybi9BQlV6UElCbnZ0dG9tSHhCMnNUL1ExUDVIdDJtODJ5dU5ieFVjNjcKVVMveEpLSDFLNmdyOTJYYTcvRnQ5eG8yK3hoY1lucjNtc1NvenhxVEhhOXJlZVRDYVl5REtlOXJUV1BqR2liRwo5VmZqK280ZlFKcndxd2sxMmdYclBiUlBTMjllQTYxd2tZZklHREE2NzJvLzREVmdFYm04RVoxYjBzTzZqK0h0CkFPb2g5RitMSk4yalFtTnlUVUdSWHZuRXVac1FsNGw3ZFIvSWdGZGFYdEpPcXA4MTZ3ZC9xSFdZbmUyeWhvOWcKOGlwMklqRy9NbGNvWXJPUUg5dHVWNjZSSHppa0JKWG9zQ0V4am1HR3ltN3VJcXpVUjE5SWtnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/apiserver-proxy.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/apiserver-proxy.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURXekNDQWtPZ0F3SUJBZ0lJTlZkb3VaQWZrM2t3RFFZSktvWklodmNOQVFFTEJRQXdLREVSTUE4R0ExVUUKQ3hNSVltOXZkR3QxWW1VeEV6QVJCZ05WQkFNVENtRm5aM0psWjJGMGIzSXdIaGNOTWpVd05qQTJNVGd3TmpFNQpXaGNOTWpVd05qQTNNVGd3TmpFNVdqQThNUlF3RWdZRFZRUUtFd3RyZFdKbExXMWhjM1JsY2pFa01DSUdBMVVFCkF4TWJjM2x6ZEdWdE9tdDFZbVV0WVhCcGMyVnlkbVZ5TFhCeWIzaDVNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUYKQUFPQ0FROEFNSUlCQ2dLQ0FRRUExSjEwWjZmL1dKN2xVYlBUaklBbk4rdVNDRlluc3ovYno5V1J1ZUZYSU9kNApYY1VFK1NtQ05NVzFWTjdnNXlERisra3lyMWt2cjNxTk50b2dYOTdid2hIcHlucFdwSWRWR05ER1VvdnNMdlBtCmZGMTAzQk9YTDVCbG9abjdHM0plVEcrNHBaNkpXNjVHT0xqYUJZS1o4aFFyMFUvby9qM1VVRUtRT1lKQVY0SFMKN0d3ZHlzVnE3YTIrTkhvUGtKYndpeWxDdk1tWW1selZFek53T3N0bUx1cUJPdXJoU2lYYk5FMG1ma3U1UExzUQo0Z2xQeTBOck0zcnc1QVUwWTBTNCtvMkwwQ1ZWbjdJbkJlNmd4L2Y5ZUFIMTlJcEpGZTJaOStFR3VkMWNjUUpCClRtcUp6dnZranAxVmo5U2NKU1h2cWxHN2czMjBTSEF6NGgxd1dHS3RRd0lEQVFBQm8zVXdjekFPQmdOVkhROEIKQWY4RUJBTUNCYUF3RXdZRFZSMGxCQXd3Q2dZSUt3WUJCUVVIQXdJd0RBWURWUjBUQVFIL0JBSXdBREFkQmdOVgpIUTRFRmdRVWhkVHFjZEZ4c2V4WU1CTzBGcXdIaUtkRHRQTXdId1lEVlIwakJCZ3dGb0FVa08vTmlubHZYR3BUCkgrbkxSTlJCU1ZDaUQ4a3dEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSENQdFJJV3gzZjRoWDg5UXlqMUZzOWEKWjFiRTBZVmt4Z1FZNjluV1lZWUl5MWIyVktKNEtRMVkxTUlsMWRCS3JIclpPaUxQNHFVOWU1YW5jaGZsYzF5ZwpUTTkxOGVrck1xZ2U4NHRFTVV0MXJ5T3RuV3V2K0dlNlovWnRtZTNMVjRJU1RuTnhOc2NuQ0ZsV1B0NUNvck44CnZwaHhxYU0rRXdwV3ppWGoxQ0lSb2pMVlRJNkYyOWEzRytNQXRVb0NNWStrSzFJbFdGYjRLZE1XOG9LUmRzMDIKZC9aNE5sY0JPRUh6NTNpMWJ3LytaVUFqaUkzcXVTTFFUbzAxYUQwOTg1Z0NHUSs0Um9HeU5TTXA1MXU0VnQxWApRNlNjR09DNTdXWTNRM1pENVJ0cUIvd0NRaUt3YzBmVHNTYnJPVlpQWXY0RXhZdVRUTXVkaUZrUFJYNGd1Mk09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNakNDQWhxZ0F3SUJBZ0lJRm5mdFAvbWlKcjR3RFFZSktvWklodmNOQVFFTEJRQXdOekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TRXdId1lEVlFRREV4aHJkV0psTFdGd2FYTmxjblpsY2kxc1lpMXphV2R1WlhJdwpIaGNOTWpVd05qQTJNVGd3TmpFM1doY05NelV3TmpBME1UZ3dOakUzV2pBM01SSXdFQVlEVlFRTEV3bHZjR1Z1CmMyaHBablF4SVRBZkJnTlZCQU1UR0d0MVltVXRZWEJwYzJWeWRtVnlMV3hpTFhOcFoyNWxjakNDQVNJd0RRWUoKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTDdTMTJaSGE0M25mV09nNmU2Mk1PTGQ4cHpXbk93YQppTEpTYzRBQUZkWGt1ZzlFOGZ4OXVseURTVXBhL2FQUXBGcnk4UllQV1V0ajc1OGQ5b2lRdVp5R09TSFFjeFZ5CjdFZWNLTXdKa1FmY1d2QjN5REtKcFdIZTU5aTl5emcrTDh5N2ppbTdUdG53VVZrQjdBR0tCWnpidU04NHcrdzgKTjFKa1QwUHlPOGlzSGFkbXlta3czTlJkSkk1MUVpblV4R2toUkRoLzFJWUtHYmo5S1VtSGp3b3BWall6b3hsbgpab3lGZi9IMGhUY0xDWmpYbXRla24vS0F6Sll3bXlYbnJFTXhMWVRaMlVTZTAzTHEvQ2ROaSthQUhYOGd3SU0zCjltc0x0M2pONDlYcHNxa0FGejhLeEVWOEMvTDBUTVBoYUFJY1dZZDM2MjlKYWYyaURFUmVKOGNDQXdFQUFhTkMKTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkp2MgpuSDVWTWllZUxFaFBKNGNSbEZNWDdGRVNNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUEzNnJucjcwTE1qN1FpCm40blJxbnh3bWdMT2ZJMzFFSXBScU0rdEx3VzlFL3RDUVJwU3NNLzQvaG1FWXpxWHd0UlY5SFVwSU43dDkyT3MKVm53UHdKVmM4eTdjMHZlb3VaSzlmazNlMXd2Ukh3b3lBa3JlSU1iMnl3bjY0WitLMzFxU05wc0xJR1V1cnNIbgpNcDdOVTZrZnhHdm5PSjBXd2gwKzVGanpvdUdVUVNNbkh6M2Z1R2I1N2l1OXNVYWpxdFJXaEg4UG5KakFnWDFiCkpoMHVnUERvdURyUEZ6Unh1TktNSmhBdTJTSVR6RFlPem10amQ3RytJSFBsb2wyREFzaVJOdWRrSFBWbkZJVkcKRUVIZFF1V1pvL001ZjhRRnFRUWRmb1RFRkVBMVg2M3VmNWltVzZUQXg2b2ZWbllFUWZCSTFKZDdSYkpNVFUyRQoraEl6THY0RAotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-internal-lb-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-internal-lb-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-lb-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNakNDQWhxZ0F3SUJBZ0lJRm5mdFAvbWlKcjR3RFFZSktvWklodmNOQVFFTEJRQXdOekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TRXdId1lEVlFRREV4aHJkV0psTFdGd2FYTmxjblpsY2kxc1lpMXphV2R1WlhJdwpIaGNOTWpVd05qQTJNVGd3TmpFM1doY05NelV3TmpBME1UZ3dOakUzV2pBM01SSXdFQVlEVlFRTEV3bHZjR1Z1CmMyaHBablF4SVRBZkJnTlZCQU1UR0d0MVltVXRZWEJwYzJWeWRtVnlMV3hpTFhOcFoyNWxjakNDQVNJd0RRWUoKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTDdTMTJaSGE0M25mV09nNmU2Mk1PTGQ4cHpXbk93YQppTEpTYzRBQUZkWGt1ZzlFOGZ4OXVseURTVXBhL2FQUXBGcnk4UllQV1V0ajc1OGQ5b2lRdVp5R09TSFFjeFZ5CjdFZWNLTXdKa1FmY1d2QjN5REtKcFdIZTU5aTl5emcrTDh5N2ppbTdUdG53VVZrQjdBR0tCWnpidU04NHcrdzgKTjFKa1QwUHlPOGlzSGFkbXlta3czTlJkSkk1MUVpblV4R2toUkRoLzFJWUtHYmo5S1VtSGp3b3BWall6b3hsbgpab3lGZi9IMGhUY0xDWmpYbXRla24vS0F6Sll3bXlYbnJFTXhMWVRaMlVTZTAzTHEvQ2ROaSthQUhYOGd3SU0zCjltc0x0M2pONDlYcHNxa0FGejhLeEVWOEMvTDBUTVBoYUFJY1dZZDM2MjlKYWYyaURFUmVKOGNDQXdFQUFhTkMKTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCL3dRRk1BTUJBZjh3SFFZRFZSME9CQllFRkp2MgpuSDVWTWllZUxFaFBKNGNSbEZNWDdGRVNNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUEzNnJucjcwTE1qN1FpCm40blJxbnh3bWdMT2ZJMzFFSXBScU0rdEx3VzlFL3RDUVJwU3NNLzQvaG1FWXpxWHd0UlY5SFVwSU43dDkyT3MKVm53UHdKVmM4eTdjMHZlb3VaSzlmazNlMXd2Ukh3b3lBa3JlSU1iMnl3bjY0WitLMzFxU05wc0xJR1V1cnNIbgpNcDdOVTZrZnhHdm5PSjBXd2gwKzVGanpvdUdVUVNNbkh6M2Z1R2I1N2l1OXNVYWpxdFJXaEg4UG5KakFnWDFiCkpoMHVnUERvdURyUEZ6Unh1TktNSmhBdTJTSVR6RFlPem10amQ3RytJSFBsb2wyREFzaVJOdWRrSFBWbkZJVkcKRUVIZFF1V1pvL001ZjhRRnFRUWRmb1RFRkVBMVg2M3VmNWltVzZUQXg2b2ZWbllFUWZCSTFKZDdSYkpNVFUyRQoraEl6THY0RAotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lJQnRPNjBVQktKdnd3RFFZSktvWklodmNOQVFFTEJRQXdQakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TZ3dKZ1lEVlFRREV4OXJkV0psTFdGd2FYTmxjblpsY2kxc2IyTmhiR2h2YzNRdApjMmxuYm1WeU1CNFhEVEkxTURZd05qRTRNRFl4TjFvWERUTTFNRFl3TkRFNE1EWXhOMW93UGpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2d3SmdZRFZRUURFeDlyZFdKbExXRndhWE5sY25abGNpMXNiMk5oYkdodmMzUXQKYzJsbmJtVnlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXZXSU5DLzZCc3BLaApza3liQmhBd1ZaSkpQci96SnB4VHVHWE5TbmV4MDQyeVRFQnliTlBSbm94MUJPN1YveXI5RithYkFPVGdqcU1VCkRzdHcxT2IwN2Q3b3NRSk12WEk0U0lhcW91Y01xb3gwVVNqaURzRWZ3T21Vc1V6WllKSFFkRzVld3hKSytYb2cKckp4ZUhtY0VKdHpUZWIrZXE4bHVmNmhZNUg1dy9KZkRzcm4wZUQ0Tkx1NlVQRTVyUWErSGpyY1MyQ3FQUXhpTgp1dmMwbmJwNGpKYkFuVjVvMkVTZm1IcWt3Vi9kYjQwVFp5MS82KzlUNGZwV2lrbG1qZFA4d3Vrd0RDN2R0a1VzCkQxZ3RvNEZBdWNUUG55bEx5cEE1QUIzNnVqTzFjTDVjYXNzZ3pzZFRnQnJUSHFIQXB4aUllaDlONlNnMld6MkIKYXRXa2R6Rjdsd0lEQVFBQm8wSXdRREFPQmdOVkhROEJBZjhFQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVSEtxN29SdkNmTWgxVUI4cmsxeVdTelpRcUVvd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSmZrR2twQ1NKaDJFdE9BQ3lmdktOWmxEU0ttS2pPVmp6dEJNaStlWnIyM09Nck9RWVVFZ21KcDd1bU4KWmd5dWNnOGNJd2FzRHNYSlg4K3ZwalVMcThxUmRRYU1IUWttMDRHQlZjZnp2bG9URjZNNHVOWnpGUERVUTg5KwpQdnZHTnJVRzhFTU1zRWZsQjJSelpBYUlBdzJhQUVoVStxWEZmNmI4SHNSTmg2WSt6R2traURtaUlxQkN0cHZDCkkxMnZPOUVzamRBaCt0NldZUDJoWTdtSmh0RkY0bklTMGRYR1BaZjV6cEw0ejFYZVZRcjBZMjZFcVZWSlhUdy8KL0dxbjM1anJ2M0lsUERwSmJWUlRKM1FlaTdwVld6cGdSZ0RkYWM3SG9QSlN5eUNGMGp0M3FxQVFEUUhPcExUaQpkVTh5bHdWbDhDRjZ2VndOUGZ4U3NvQVpjOFU9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-localhost-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lJQnRPNjBVQktKdnd3RFFZSktvWklodmNOQVFFTEJRQXdQakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TZ3dKZ1lEVlFRREV4OXJkV0psTFdGd2FYTmxjblpsY2kxc2IyTmhiR2h2YzNRdApjMmxuYm1WeU1CNFhEVEkxTURZd05qRTRNRFl4TjFvWERUTTFNRFl3TkRFNE1EWXhOMW93UGpFU01CQUdBMVVFCkN4TUpiM0JsYm5Ob2FXWjBNU2d3SmdZRFZRUURFeDlyZFdKbExXRndhWE5sY25abGNpMXNiMk5oYkdodmMzUXQKYzJsbmJtVnlNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXZXSU5DLzZCc3BLaApza3liQmhBd1ZaSkpQci96SnB4VHVHWE5TbmV4MDQyeVRFQnliTlBSbm94MUJPN1YveXI5RithYkFPVGdqcU1VCkRzdHcxT2IwN2Q3b3NRSk12WEk0U0lhcW91Y01xb3gwVVNqaURzRWZ3T21Vc1V6WllKSFFkRzVld3hKSytYb2cKckp4ZUhtY0VKdHpUZWIrZXE4bHVmNmhZNUg1dy9KZkRzcm4wZUQ0Tkx1NlVQRTVyUWErSGpyY1MyQ3FQUXhpTgp1dmMwbmJwNGpKYkFuVjVvMkVTZm1IcWt3Vi9kYjQwVFp5MS82KzlUNGZwV2lrbG1qZFA4d3Vrd0RDN2R0a1VzCkQxZ3RvNEZBdWNUUG55bEx5cEE1QUIzNnVqTzFjTDVjYXNzZ3pzZFRnQnJUSHFIQXB4aUllaDlONlNnMld6MkIKYXRXa2R6Rjdsd0lEQVFBQm8wSXdRREFPQmdOVkhROEJBZjhFQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVSEtxN29SdkNmTWgxVUI4cmsxeVdTelpRcUVvd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSmZrR2twQ1NKaDJFdE9BQ3lmdktOWmxEU0ttS2pPVmp6dEJNaStlWnIyM09Nck9RWVVFZ21KcDd1bU4KWmd5dWNnOGNJd2FzRHNYSlg4K3ZwalVMcThxUmRRYU1IUWttMDRHQlZjZnp2bG9URjZNNHVOWnpGUERVUTg5KwpQdnZHTnJVRzhFTU1zRWZsQjJSelpBYUlBdzJhQUVoVStxWEZmNmI4SHNSTmg2WSt6R2traURtaUlxQkN0cHZDCkkxMnZPOUVzamRBaCt0NldZUDJoWTdtSmh0RkY0bklTMGRYR1BaZjV6cEw0ejFYZVZRcjBZMjZFcVZWSlhUdy8KL0dxbjM1anJ2M0lsUERwSmJWUlRKM1FlaTdwVld6cGdSZ0RkYWM3SG9QSlN5eUNGMGp0M3FxQVFEUUhPcExUaQpkVTh5bHdWbDhDRjZ2VndOUGZ4U3NvQVpjOFU9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURURENDQWpTZ0F3SUJBZ0lJU2R5UmZRY0E5Wk13RFFZSktvWklodmNOQVFFTEJRQXdSREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjJhV05sTFc1bApkSGR2Y21zdGMybG5ibVZ5TUI0WERUSTFNRFl3TmpFNE1EWXhOMW9YRFRNMU1EWXdOREU0TURZeE4xb3dSREVTCk1CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjIKYVdObExXNWxkSGR2Y21zdGMybG5ibVZ5TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQwpBUUVBdFpMaVpJVDM0V2xyWVJKaVdCb3ErZTRzRFljNDh0RUdrT3hLb0JiQ01oTVc2dG1FZXBDZUk3VFdkZHI1Ckk0WWpkTDRPV0xIQm40UVdxeDdEZkljUzdDMWNVNktNZXJvMGpxVUhrcDJmS3Y0cytFN0RtWHNDMU50dE0xZnEKTExVV2RoU2RXZGRUa3YwNHJkWjQ5clpKS3RYUnJia2JYYmZ0TU53TStRNDJKRWpicW9ESnR6TmdMa0Q0Z0hCOQpjSDNQTHlEMGhPSHhYL1NGNnBNUU42TjE5alNRaWhiK0lqQ3JtSHBWdWJ2STZGa1FxM3kwZFY4UXhkSDBkU0pTCnJoa2NvOEQ2RitMUng2VG02YWhWU0dmZjdNcitEdHVwcEh1TktCaW5UVTNVN2pCVnpZSENUdnlTb240YllxYnkKREQ4SUwrUmFBZ1ZOMlVreDVFTnMrZ3p5bXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRApWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVWx2d1VJVnZ2eTBuZGJlZDE4Z1BZUlVVOEsrZ3dEUVlKCktvWklodmNOQVFFTEJRQURnZ0VCQUd1Yi9EanZQOUVsUWRTd0dQeHhmQ0xCTFl4RExTYWpPaWtsZmIxa282ZjUKWjVVK0ZTOXliWXpVNFpSOVhXUU5DWjRLTHVzZjVuSHhtc1NaV01ZVmVCNG5ka0w2b3pReEJXVG1yT1g0bDhoUwpKMnBpMnVsWU9oSUpMK1p1MXBGaTFBbWJnWDFIZU5DaTVjMmFTdTBtamxhUXNPV2xJMVpDblBqa1pWRzNnNkQzCnlBd2I5eEtFeXBVS1Axbk1mM0FuVWhNM1JpdWh1Z0lQKzhpZG9BWDRRRS8rNnp2VEZ6eFAxa2V5WGIreDFHSC8KNXhFUEtYdWhnMEd5N0NTYUFQUFkyRDRRT0lSUTY1d0k4RzRCK01oMldHSWw1eWVJR2RpNndpUGVWS3hFck1BdgpxWGtEMEEzK1NEaktiMkYyNS9uR2RBRGNSek93VXJEL0FBSU1hTkNjUS9VPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-service-network-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURURENDQWpTZ0F3SUJBZ0lJU2R5UmZRY0E5Wk13RFFZSktvWklodmNOQVFFTEJRQXdSREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjJhV05sTFc1bApkSGR2Y21zdGMybG5ibVZ5TUI0WERUSTFNRFl3TmpFNE1EWXhOMW9YRFRNMU1EWXdOREU0TURZeE4xb3dSREVTCk1CQUdBMVVFQ3hNSmIzQmxibk5vYVdaME1TNHdMQVlEVlFRREV5VnJkV0psTFdGd2FYTmxjblpsY2kxelpYSjIKYVdObExXNWxkSGR2Y21zdGMybG5ibVZ5TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQwpBUUVBdFpMaVpJVDM0V2xyWVJKaVdCb3ErZTRzRFljNDh0RUdrT3hLb0JiQ01oTVc2dG1FZXBDZUk3VFdkZHI1Ckk0WWpkTDRPV0xIQm40UVdxeDdEZkljUzdDMWNVNktNZXJvMGpxVUhrcDJmS3Y0cytFN0RtWHNDMU50dE0xZnEKTExVV2RoU2RXZGRUa3YwNHJkWjQ5clpKS3RYUnJia2JYYmZ0TU53TStRNDJKRWpicW9ESnR6TmdMa0Q0Z0hCOQpjSDNQTHlEMGhPSHhYL1NGNnBNUU42TjE5alNRaWhiK0lqQ3JtSHBWdWJ2STZGa1FxM3kwZFY4UXhkSDBkU0pTCnJoa2NvOEQ2RitMUng2VG02YWhWU0dmZjdNcitEdHVwcEh1TktCaW5UVTNVN2pCVnpZSENUdnlTb240YllxYnkKREQ4SUwrUmFBZ1ZOMlVreDVFTnMrZ3p5bXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRApWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVWx2d1VJVnZ2eTBuZGJlZDE4Z1BZUlVVOEsrZ3dEUVlKCktvWklodmNOQVFFTEJRQURnZ0VCQUd1Yi9EanZQOUVsUWRTd0dQeHhmQ0xCTFl4RExTYWpPaWtsZmIxa282ZjUKWjVVK0ZTOXliWXpVNFpSOVhXUU5DWjRLTHVzZjVuSHhtc1NaV01ZVmVCNG5ka0w2b3pReEJXVG1yT1g0bDhoUwpKMnBpMnVsWU9oSUpMK1p1MXBGaTFBbWJnWDFIZU5DaTVjMmFTdTBtamxhUXNPV2xJMVpDblBqa1pWRzNnNkQzCnlBd2I5eEtFeXBVS1Axbk1mM0FuVWhNM1JpdWh1Z0lQKzhpZG9BWDRRRS8rNnp2VEZ6eFAxa2V5WGIreDFHSC8KNXhFUEtYdWhnMEd5N0NTYUFQUFkyRDRRT0lSUTY1d0k4RzRCK01oMldHSWw1eWVJR2RpNndpUGVWS3hFck1BdgpxWGtEMEEzK1NEaktiMkYyNS9uR2RBRGNSek93VXJEL0FBSU1hTkNjUS9VPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-complete-server-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-complete-client-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRakNDQWlxZ0F3SUJBZ0lJZjF0cnF2V3duVkl3RFFZSktvWklodmNOQVFFTEJRQXdQekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Ta3dKd1lEVlFRREV5QnJkV0psTFdGd2FYTmxjblpsY2kxMGJ5MXJkV0psYkdWMApMWE5wWjI1bGNqQWVGdzB5TlRBMk1EWXhPREEyTWpGYUZ3MHlOakEyTURZeE9EQTJNakZhTUQ4eEVqQVFCZ05WCkJBc1RDVzl3Wlc1emFHbG1kREVwTUNjR0ExVUVBeE1nYTNWaVpTMWhjR2x6WlhKMlpYSXRkRzh0YTNWaVpXeGwKZEMxemFXZHVaWEl3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRQzhZdEZLaW5xZAp6eE0xajhBamhrUExaWTI3VXlJM2hhMzJmM2xrTGJNd3JwVkJsb2xGbTQ4bHJ4bkFuREk3UjNZeTZwRzBlMUk4CkxFWVpnUHJWNm0yOElvZ2JOU2ZSME41S2FYTTBNeCtKTHpPVlZYNDEwdVNkSitqNXNvamxJalFTNHBCQzBISk8Kd016VXFjMUdDNENmS01tMys3aVVmMnh3WW80enVWVDQwN1Zyd1h0Ykp6TmVlLzBSbzNSSHdObFdKa2JZcVpBcAptMWpsYk5laHN4UHVIUGRBMkNMSHFuandQNHZ5N3VjSklPM1YzWVJ6SEdROEZURjV3QlFzUllyM3dLenVhLzM1CjRMMC82WGQ1TmNyQ3VMRjFwS2pBaDM5aVR4aUZBUlUycEZaSlZXaERJT2hXR3UyMjVzV25MM0dYc2gvOE94ZTEKckE2NTVqZWJaeHJMQWdNQkFBR2pRakJBTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBRApBUUgvTUIwR0ExVWREZ1FXQkJSOTgxV0RTNjR2NnRKRUFIVTRaVVBoQ0x1UmVUQU5CZ2txaGtpRzl3MEJBUXNGCkFBT0NBUUVBaVFxaVB5VTJvS3VoQktzdzliS0RVMFl6TzYwTjFhNDhtYUFmODF5NkIrRjlDbFM1YlJxMTRNRUYKYU41YjFYcTBETXhtWXlSRnlIczlDSGRKMnd2dmF4ZllZQVNWRlJyL2hMaGE0ZU5PY1FDV0s2OFNxVDJJa2dxUgo4SXdkZ2ZyVThUMStpM05BNE5CbVB0eklkbEpKalRucjJIY05QSnFZaldXUTV2U0V5bTh5T3RSSDFlbUZhN3J4Cm9FdXdyUUo2NUx2RDVwSi91YS9FUTd6QjN0dWM4SDNjT1gyMlRRRXlRemdSQ1pBNXNPWnZLa2RNOWQyTnlCNUMKZUhlK0plS0FSTWdNcllCYkQ4Z29Ea3MzbzE1Y0xTVTVkQmMzbUtucm5vbjJGVFVZS0duYXVUTEpuamkrQ1hLMgpXWmUyYkp4WE1yWjBZZHFRRklUSm1hOU5kWU9vd1E9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURiRENDQWxTZ0F3SUJBZ0lJZkpXbCtaQWpIeGN3RFFZSktvWklodmNOQVFFTEJRQXdQekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Ta3dKd1lEVlFRREV5QnJkV0psTFdGd2FYTmxjblpsY2kxMGJ5MXJkV0psYkdWMApMWE5wWjI1bGNqQWVGdzB5TlRBMk1EWXhPREEyTWpGYUZ3MHlOakEyTURZeE9EQTJNakZhTURZeEZEQVNCZ05WCkJBb1RDMnQxWW1VdGJXRnpkR1Z5TVI0d0hBWURWUVFERXhWemVYTjBaVzA2YTNWaVpTMWhjR2x6WlhKMlpYSXcKZ2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRENOSHRtNWozcUJxV2VIZFpMdC85YgpGdjVNRXN3UTdJV2dUSzRGNWduamRqRDNXUHpWRjFVV0ZScC8vZVM4M25NZ3hxdmV2azRnTU5KS3lhd1VMZEdKCkNxQ0F4VVdZZVpSTXNxLzJrN1RibkFiMmlzZVRQNEhHejBBS2duMEhpZ3MvNmw4NG93UE0zaWhOVnlLUzd3VUEKV0JpK0RBOXVSSnA1OXRNdWljaDZLRjlCakhuVVVvMURvVXQ5QzlIdDdmSnN0WEJqZHlUUWFIaXQzTnErRlcwZQpvNzFwN2Y0Y2w5ZXZXQmdIL3F2UTBQbXRPVmZCM2VlUG5pTXIzdlIzZ0xxeW1TdTI5SUh6NzhxZDdPays4c3JMCk5jRHJRMm1NWDhNR051dmpyRExkRy9tcTlwOVN4RDhZYzVPK0VvWUFoaHdYZWhiSkdicmg2TWRjMkVvUFBzQWoKQWdNQkFBR2pkVEJ6TUE0R0ExVWREd0VCL3dRRUF3SUZvREFUQmdOVkhTVUVEREFLQmdnckJnRUZCUWNEQWpBTQpCZ05WSFJNQkFmOEVBakFBTUIwR0ExVWREZ1FXQkJUai9kc3pieURTK3E2WXlJQXR1bVVxbjJRVC9EQWZCZ05WCkhTTUVHREFXZ0JSOTgxV0RTNjR2NnRKRUFIVTRaVVBoQ0x1UmVUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUEKVmxTbGk3d2oyb25NSE1lSUQwQkFKT3JKTkxNUG8xZHhETmxwWENqak5mUDJtUlY4MDFZdnJ3TXZXc3lTRjNqcQpNZFJMVkVoRTc3VVp3aURqVWlqa2RsZEJKWUNPbGtCenZXaWt4enBRL3VFWW0yNklBOG5XUVBSRnJBOGswcmFhCmZ0N0VxV3hEdGIyNmVhSlRLWGVWY1VUZGgwcGtTRUZzbTQrb3ZMNjdtYjNUcGVtZEtQM1Fwa2Eyc1F6ZDA2dk0KTmQzbzM1VndsRnVEaHNxNGtLVENvcTd5R3ZDbVRpS0dERUtvY1NZVExKSkkxc1RaWTlCNnVVb3hDQlJvbENyWQpCdndDdkd5MnBxb29aR253SkxuNksyMGxYUlVyb1c3MjRtMUR6QXVnSHF3Q1ZGN1hrOGxlaSs0QklZc3dYeFVQCnJFYlBKOXU4TS9NRGVrZnBkSWJIUHc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-apiserver-to-kubelet-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRakNDQWlxZ0F3SUJBZ0lJZjF0cnF2V3duVkl3RFFZSktvWklodmNOQVFFTEJRQXdQekVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Ta3dKd1lEVlFRREV5QnJkV0psTFdGd2FYTmxjblpsY2kxMGJ5MXJkV0psYkdWMApMWE5wWjI1bGNqQWVGdzB5TlRBMk1EWXhPREEyTWpGYUZ3MHlOakEyTURZeE9EQTJNakZhTUQ4eEVqQVFCZ05WCkJBc1RDVzl3Wlc1emFHbG1kREVwTUNjR0ExVUVBeE1nYTNWaVpTMWhjR2x6WlhKMlpYSXRkRzh0YTNWaVpXeGwKZEMxemFXZHVaWEl3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRQzhZdEZLaW5xZAp6eE0xajhBamhrUExaWTI3VXlJM2hhMzJmM2xrTGJNd3JwVkJsb2xGbTQ4bHJ4bkFuREk3UjNZeTZwRzBlMUk4CkxFWVpnUHJWNm0yOElvZ2JOU2ZSME41S2FYTTBNeCtKTHpPVlZYNDEwdVNkSitqNXNvamxJalFTNHBCQzBISk8Kd016VXFjMUdDNENmS01tMys3aVVmMnh3WW80enVWVDQwN1Zyd1h0Ykp6TmVlLzBSbzNSSHdObFdKa2JZcVpBcAptMWpsYk5laHN4UHVIUGRBMkNMSHFuandQNHZ5N3VjSklPM1YzWVJ6SEdROEZURjV3QlFzUllyM3dLenVhLzM1CjRMMC82WGQ1TmNyQ3VMRjFwS2pBaDM5aVR4aUZBUlUycEZaSlZXaERJT2hXR3UyMjVzV25MM0dYc2gvOE94ZTEKckE2NTVqZWJaeHJMQWdNQkFBR2pRakJBTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBRApBUUgvTUIwR0ExVWREZ1FXQkJSOTgxV0RTNjR2NnRKRUFIVTRaVVBoQ0x1UmVUQU5CZ2txaGtpRzl3MEJBUXNGCkFBT0NBUUVBaVFxaVB5VTJvS3VoQktzdzliS0RVMFl6TzYwTjFhNDhtYUFmODF5NkIrRjlDbFM1YlJxMTRNRUYKYU41YjFYcTBETXhtWXlSRnlIczlDSGRKMnd2dmF4ZllZQVNWRlJyL2hMaGE0ZU5PY1FDV0s2OFNxVDJJa2dxUgo4SXdkZ2ZyVThUMStpM05BNE5CbVB0eklkbEpKalRucjJIY05QSnFZaldXUTV2U0V5bTh5T3RSSDFlbUZhN3J4Cm9FdXdyUUo2NUx2RDVwSi91YS9FUTd6QjN0dWM4SDNjT1gyMlRRRXlRemdSQ1pBNXNPWnZLa2RNOWQyTnlCNUMKZUhlK0plS0FSTWdNcllCYkQ4Z29Ea3MzbzE1Y0xTVTVkQmMzbUtucm5vbjJGVFVZS0duYXVUTEpuamkrQ1hLMgpXWmUyYkp4WE1yWjBZZHFRRklUSm1hOU5kWU9vd1E9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-controller-manager-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-controller-manager-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURYekNDQWtlZ0F3SUJBZ0lJWmd0cTNmdWV3NEF3RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXdOakU0TURZeU1Wb1hEVEkyTURZd05qRTRNRFl5TVZvd01ERVhNQlVHQTFVRUNoTU9jM2x6CmRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41YzNSbGJUcGhaRzFwYmpDQ0FTSXdEUVlKS29aSWh2Y04KQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU1ZUGVoTW9xQm10QmFKOG44cFJHKzg5dktLRm4zWmxhN25UQk1VQwpsWS9YdjZTSGlVbXl2TFhQbEJGVXJyUzRFNnd1L0VzdUtHT2dLMUpTZXNmQWp5dFozKzhlcC9pUTJ4MUZOL200CjBkSENaOEdualZ4NmhPY2FMZ3VqcTE5R3B6M2FaVWtIanQvUU5DM05ITW0wMXdNS1dtZlhRTTd6QTNkNUFsM08Ka25aa215UTFSZlpTQ2UycERSdjhmYUp5T29BN3haRTdML2xxdHYrVFc3WE9wK2RpTE1ObExJdG13QXN2Y2FnQgpVM1dYam9VVjh5UHhNR2tBS0JYaExPMW5HVXpJbDJkTnFDa3FmZkw3Y3EyNkVxaVVsaHZ6OStaeEdSSG1zUXB0CndJejdKdnVzR3BNd0syeDdBTW9xQ1pQdE1lRGswMURsYzNnamJaTnhsL0ZsdDJrQ0F3RUFBYU4xTUhNd0RnWUQKVlIwUEFRSC9CQVFEQWdXZ01CTUdBMVVkSlFRTU1Bb0dDQ3NHQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBdwpIUVlEVlIwT0JCWUVGRmx3RStZSDVDOGErN255bGM0NzVQWGRac0RyTUI4R0ExVWRJd1FZTUJhQUZHcjZORFZ1CktJVEJ2N01hTCtnd3BoS25ZQUtoTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFCa2pVWFk3Mit0Z0lKQ0tLV00KeVlpd050N3pYMmE1Qi9mT1lpU1JRd0JxS2JySCtxb08xVEJnalBDZzFicm5vaFBlTzd3RFlNS1l3UUQ1WVltNgpNWnZNTHNNV0Mwb1hWWkxTMjV2T29telMvbkl5TDFPOU9TU2lOMEt1dUZ4cFBWWWpTMEVFVGFBbDRrQ1Y5alRZCmhwZHFkZzE3Y2FrRzFpeUhIemZYc09CSkMxY3hLZStxWUNFZGxpQ044QlFlbjVKVWdRU0RTRVB0MEJKQVV3eW0KVWVxNG84bjJjZ0lRZ255a2YyVmhlajdBcjBDU00zL1dCWmxQUTZMUjRCTU5QOFQrVHFMOEJTSkVydXhDNU0yTQpWL0xndGNjanF4M2lGSThVNHVVQ3VQTzJHbTJwWXV2b3B6cDNIeFNqQTM2dExDZFcwb080alM2bEVmdHFDaFh5CmYyVm4KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-scheduler-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-kube-scheduler-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURYekNDQWtlZ0F3SUJBZ0lJQ2gxdFF6T0dJbk13RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXdOakU0TURZeU1Wb1hEVEkyTURZd05qRTRNRFl5TWxvd01ERVhNQlVHQTFVRUNoTU9jM2x6CmRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41YzNSbGJUcGhaRzFwYmpDQ0FTSXdEUVlKS29aSWh2Y04KQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU1DYzkranQxSEJ5VVBTSm1nMkgxYm1aVXBJTUZJT0JhNnpObWtMcQo0akJKU1BLQnpXZG9YeGsrcDUyMmd6RFpCcVhKYWNpQVM2WnFNRUZKMXlqZkhIcC9XZzB5Q0xmNlZacnNWUXMrCndYTWRhdUFRSldXUEUyMW01QTZ4RWxsYjZ3QkdSNGsxWFNqSmRZQnkyQTNZY2xSNnZXcVJQaUdIT0hkMmw2UGkKRzlNSnB0Z2sxbXVHS1A2aDJjT0Fja083SHdLaDJYeVhTYTFrYWI3VlpiWVRqdDBuZnJqTTVTa2ttNXp3MzZFUwpsYmVCQm1oSVZoRC9oeHhISFBVYUF6NDhIU0lmZXUyVnhXcHRGa0grYVg3WmZtMDVQaWtUeFU5a1QyUDhyeUJqCi9VODAwM2Q2SnRjR0xrendLNENyYUE4bFFvYkltUDJaU2hRRlZKVXdVS0pnWldzQ0F3RUFBYU4xTUhNd0RnWUQKVlIwUEFRSC9CQVFEQWdXZ01CTUdBMVVkSlFRTU1Bb0dDQ3NHQVFVRkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBdwpIUVlEVlIwT0JCWUVGRVVmeTJnSU1OU0J6YlluNGorbisvUTlxTkxITUI4R0ExVWRJd1FZTUJhQUZHcjZORFZ1CktJVEJ2N01hTCtnd3BoS25ZQUtoTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFBRmNKVzZaNHpXUmU4SDRwNnMKc1BPZnYrR2pKTDJYNXZIUGpoTE90ZW1YV3lOa05HbEM2V3Z6UHdiNEZsNFBKMmx3NlZSQk1OSVY3NzZsbzJ2TwpaYWFQby9SNXZyd3J4RFIybHdObDJ1UysrSUorR1IwUGc0WWxKRDhWV0YxMFZUVWthOS9qK2JFaTJkdkRrSDVwCmxBSEFsZURObS95ang5WUp3cEhUem1BTjd4MmQvMUw2M2VEc2ZNZm9ta1VYaEl6Q3RDR2FtRWxHRTYxczFiUWcKTjlVblZ6MzZjcm5lSEhLNzB3ZWNSZVFDQ1dwemMzY3pVdThUMGxScmQ1S0t6VTI4YmFaUVFuZEZYaFd4Y1c5WgpxOENhTXN5U2JabzJzbmtibTJ0Q2RIbTkxRTVIOHlMQVpJa0pMcHpBZVhOTERaN0E3ZHdjc0F4Tkw3RlcwaVRiCjRBRG8KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kube-control-plane-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURORENDQWh5Z0F3SUJBZ0lJUDlENlBkNmRjWmN3RFFZSktvWklodmNOQVFFTEJRQXdPREVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1TSXdJQVlEVlFRREV4bHJkV0psTFdOdmJuUnliMnd0Y0d4aGJtVXRjMmxuYm1WeQpNQjRYRFRJMU1EWXdOakU0TURZeU1Wb1hEVEkyTURZd05qRTRNRFl5TVZvd09ERVNNQkFHQTFVRUN4TUpiM0JsCmJuTm9hV1owTVNJd0lBWURWUVFERXhscmRXSmxMV052Ym5SeWIyd3RjR3hoYm1VdGMybG5ibVZ5TUlJQklqQU4KQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBdHhXb0pEeWN4R3NSbDBPa2l0WVltM3JFOWpLUgpHRWJxVFBTdTZZcWljQnJHalo3MkZiTDBpbDRUYk5PNno2czZlSjhrMnBlTlNnYURiY1VYdjZiMHdKekk0elZ1ClN6cHB4b2JsZGlyWk5KNlBTNTlvSTh6VzcwS2c1QUIzeXNCRFpZTWJWK2l6K3RZQ0ZnTmREWjA4LzU3bDRaUEkKeHpjV0xJRy9yc0lQWTkwYWRZbis5eDZoSGxoT1lta01Fc092WlhpVzRObGI3aXFQbDVHb3lOVmVLQzVtWVlBdwpCTDZ5elpVb2tjMnlEQk8rODNJU3J5T2lJV3doU3FCK0s5OGVKMzBwWkhOYkZtSG41Nyt3SXJjTllaV2NjbW9yCkVVUzdFK3I3VGtHOEE4T0hsVkhJQ3c0blhXRWZmS1M3Z0NJaHNXb0lxY3RIbEs2TGJib0NuL1N0OFFJREFRQUIKbzBJd1FEQU9CZ05WSFE4QkFmOEVCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVQphdm8wTlc0b2hNRy9zeG92NkRDbUVxZGdBcUV3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUJvZGQwVURYYVkzCkZvUmNNakgycTZxUjJNZlBVNTMzUVpvaDZBdW1GQzZ3KzJ0OEQ0bENGQ0cyT3RwRnE2QWpuME1sckg4ZWVZRG0KZ0lrWkNJclJGUUFKTVJiOHFaai9EK1pmZU9nbGNKUi9UWTM3c1BWTjVUc1RQR3A2TUpZMDNTaVBYQTlpQlFybApScDlrbHN2TVludkpQZkEzSWtWSW5ESW9MY0JiYThJZ2dDRTZwYVloc28yLy9YOGcwdkRBY3YrWXY4YWpXUktDCnp2UVdGaEtMVnVCT0QydVJNR1VxOERRdzVmMkxVcWRyd1VFdk1BTlg1SllvVkZxQ2E1Q2xiTVkxMjdrcWpaMTIKSWl0TTM4RklYNEhXZ3Y4QnAzL3A3TEdiYlFOdjRQMTZFaEZob0Q0eTM3eGtvY0Qza0ZocEVPdmd0YzVndHdUMwpoMmZqQnVkbDJpaz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-bootstrap-kubeconfig-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURTRENDQWpDZ0F3SUJBZ0lJWlY3bk1aaFk2bFV3RFFZSktvWklodmNOQVFFTEJRQXdRakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Td3dLZ1lEVlFRREV5TnJkV0psYkdWMExXSnZiM1J6ZEhKaGNDMXJkV0psWTI5dQpabWxuTFhOcFoyNWxjakFlRncweU5UQTJNRFl4T0RBMk1UaGFGdzB6TlRBMk1EUXhPREEyTVRoYU1FSXhFakFRCkJnTlZCQXNUQ1c5d1pXNXphR2xtZERFc01Db0dBMVVFQXhNamEzVmlaV3hsZEMxaWIyOTBjM1J5WVhBdGEzVmkKWldOdmJtWnBaeTF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFEQQpwU2MwSVJ2bmYzaU9LcnJSYUFCSEYvbmdzRmpzMnJyaG0rTUdadDBxcmlUd25ucHVBSUlDQVRSSzUyZzdGckR3CnVWSDRCY0tjYmFEWmQ5MmhIVHpRd0x4Y2VjT0pwK1JXL2owanQxYm5RdTZBT0VjbElOeENyODhld3NDUHFtTHMKNS9MRFdCMEtFWVhFTjRQSHdYeEJWZ2lUMlBTZjlwSFRLZUNiM2N3a2plNm44bGtJdnJ3SEVUOVVOOXpMeWFSNwpXVk5kU0ZVdXMzSGFGZDVCamJZa3VyNnZUbFg1UkpkaUJ5SXRCNjNFam4zZERQUWU1ZzBFMXRidlgwUlR2cERLCnYySUVJcGZDa242NDkydjlFWGduMzI3eVo4RmcxMm9acmVoNTFzbUJ4eUM5UmNTa0RVU1JtS1RzK2E4bHdXckQKRVVnaUVaRXhMSU1yRkNWKzEwcnZBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVBCZ05WSFJNQgpBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlF5ZG5uejVKQ3dVdXFHSTNJbVZrL1ZXQXVWVnpBTkJna3Foa2lHCjl3MEJBUXNGQUFPQ0FRRUFibFc1UytQM2NEclYzdXlaNDM5eGkwdXkzOFplUm5pbmZBcjlQQksrUUtyL3Zub2cKUTdlaUVFR2xvOWpOUFRxY2dEVFpHSWR3SmZHbzlCVnFrQ1hwMFVLbCtVMU9ZVXJuVGhmaFAzS015ODlvMCtGZQoxM0NzTW9JbkpUaE4rZlVnUEFjTXg4aEc3Z3U4QyswSTBWNUxSR1dIOUo4dks3M2xUZ2FJWVJuNUhwTTVjNjM4ClJFdFhMVXBySU53WTh5d1NUN1JBM1IxMlpLSHdiOFRBbGpLNXc4UWVwKzFjN3VWQ20xM0QrQWhIYXVNcm53U1YKNEgzMHlYc2dIY3V1b3FKK3hTa1d5cUorMXpsYkNtczB6MFlXaEx6S3VoRVhHNG96K3ZoTEtVck9HWDE2U09pNQpJRmJwanJ6L0Nudi9mVXNNeFFzTzJKSCtRRmhRcmw4eElUZ01Cdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJQVpJQ3NiQzZvWnd3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTURZeApPREEyTWpGYUZ3MHlOVEEyTURjeE9EQTJNakZhTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRQ203VlArakFPdklYdFg0a01YSGFIemFmSG1PRXczaVU4cENHWmhQMWo5eVZiK1RLNFNOMnRSYkxOQwp6S2pHNVA1SFFnT2tsZHJIWldSdGVWTFFOeEhrVm5HQUlqMHFjTFg0M1dRWVZUUTdjWVh3MzNFck9Kd3M5Y1NjCkZCenV6eU1yYVdtUDI2eEV1NXVDY0J2S0syWVF5NGZob2dDYVdlbUdmMy96QnpYL3hEUktYS25wTCtJMGRobmUKd2YxRUJLMk5XeXNObVhmU1VtYkE2NVBUTlZ3TWNVZ1dDeEwrMmNpcVFWTVdnSEljenk4MVE5eFZ2UEFGeVRWZApPYzZlL290SjVMdUtWQUZkbkNGcVNsamsvUmhudDZIei9TSkVzVk01ZHdJeHk1bzhSTkFtNW9BdVR5WmZpMS9nCnVmNFhjVUkwcVJtV1U3Wk5TeHFEUWlGL2hQRDFBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlFweFRYRTNQdTl1TVZMRWVFQWJKWjc3Ty9HUVRBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFnc1VjSGd3NEYxMnBsSkhKRXRiaTdUTzVHMW5oeWtUT21RSDA3Qm5CCmhLbFNadlFKeEdVYjQ1WWN4djJ1RlhCYWZmYm9TMHNKMmhLUUZwNGN4WCtJQStpVWlyVjJ1aDNOeWlJWEIxOGMKN3U5ZkYzbGk4VWlXZjk4L1VHdmRTWWFjUWRidDE1cmMzZ3pKd2RqZTdrb2M3TzZrNWdEOFc1VzhSUVYvcEpJUQpzUjRjZytGSWc5OVVtTEZLTHJGa1dRMCtYazZVQVVzSkdNKzVVQUpwaTZSaFAyZ0NFODFsYUdaZUpVQ0RpMWxrCnZ5ZlVoQnBBbWJVWmoxQUNudnFzMzBKemplM1AzTXljSWZxc3ZEV2RPMFlZVnlDMVpMZmZzSG1uVzhEZW85SysKN29icHVIaElsMExpYyt2SExKZlZ2b2VweTIzWDg0RU11MGc2MXR4cWFzbTlIQT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-client.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ4RENDQXRpZ0F3SUJBZ0lJYWwwWDFZcXVRc1l3RFFZSktvWklodmNOQVFFTEJRQXdRakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1Td3dLZ1lEVlFRREV5TnJkV0psYkdWMExXSnZiM1J6ZEhKaGNDMXJkV0psWTI5dQpabWxuTFhOcFoyNWxjakFlRncweU5UQTJNRFl4T0RBMk1UaGFGdzB6TlRBMk1EUXhPREEyTVRsYU1JRzJNV0F3CkhRWURWUVFLRXhaemVYTjBaVzA2YzJWeWRtbGpaV0ZqWTI5MWJuUnpNRDhHQTFVRUNoTTRjM2x6ZEdWdE9uTmwKY25acFkyVmhZMk52ZFc1MGN6cHZjR1Z1YzJocFpuUXRiV0ZqYUdsdVpTMWpiMjVtYVdjdGIzQmxjbUYwYjNJeApVakJRQmdOVkJBTVRTWE41YzNSbGJUcHpaWEoyYVdObFlXTmpiM1Z1ZERwdmNHVnVjMmhwWm5RdGJXRmphR2x1ClpTMWpiMjVtYVdjdGIzQmxjbUYwYjNJNmJtOWtaUzFpYjI5MGMzUnlZWEJ3WlhJd2dnRWlNQTBHQ1NxR1NJYjMKRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFES1VzY2ZDM2t5Y05QZ2p0ZFp0WGtXT1RheUNRV1gyckhRT3U1cQpvQUxDb0tZa2dYMkpLbW1lZisycGFDdXlCYmZmeG81bU5vMGRhNDdxMkRNd1NXWllHazZMd0N4L2FTQUtnNU4zCmhSeVVKemE2dTRFalIzVXc2MU1OZGgrN1lOdWtnWG1meUd4eE9BdU5XZUZNMmRMWG1vNTFmaURpTmkvWXBVb3EKTzhsa1NEcFg1SW1qV1ZJejdOY3F4WHRkVmRENmJPY0hKTVMvZFBqMFFGVkxtbG5ZcWdOMUc3VkRDNGxJQ2gragpOZTBJMHQ2akJTZEQxMDVvVzlQOGh6OFpaZ1E3dC9WaHhaK0k4WnhGVmxDYVRqYklsUUR0a0Q4L1YyRnhpOVY0Ck5UVDhCY2YxYitqaG9Ed0dFQzRlV01UWE5jNFd4dmJLTUVhMVVWOGlvTnlNakRlaEFnTUJBQUdqZFRCek1BNEcKQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBakFNQmdOVkhSTUJBZjhFQWpBQQpNQjBHQTFVZERnUVdCQlMyUTZwN3BVbkRWY0FXS0liWFNoZTNmbUMza0RBZkJnTlZIU01FR0RBV2dCUXlkbm56CjVKQ3dVdXFHSTNJbVZrL1ZXQXVWVnpBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQUI2ditjdHMwK1F5QXorbVoKazl1YXlEZEhWd2FxVk9hMVJPWjQxVEZYenJPUW80UnVOVXZVb3FadEhOQndKR3IwYkF5Tk9MaHE0VExVSUdrWApjRWlBalIvemE1cW0wSVJjQno0ckk3VXRydVdVQ1pYaWg4dkRMMDdDZ1ByemNQbmhOSnVFMVRjckNoK0xXdGxGCldUcVlGSjFOWlowTnY4UWpaeWxQZm1wZlBNTlkzQTJsNFNRMXR5S0tmU2EwTjVMN2hwY1h5Tlk0c0lPQzlyTnAKMG9HNXhUdmpNQk44c3VRVXE3MmxwelNSekk0TFRkQzg1WVo4M3FHUlhHbmhpbTJqVjdYUnJocmtrVzlmeGl3dAo2c1ppZDcxZGgzRDNrbjRZTVVZY290alE0SEwxbFZ2YWtwUEVDZVd2b0hHQzZXKzI3V3k3alJ4RmhQMElMYm94CmVFSE9WUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-signer.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-signer.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJQVpJQ3NiQzZvWnd3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTURZeApPREEyTWpGYUZ3MHlOVEEyTURjeE9EQTJNakZhTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRQ203VlArakFPdklYdFg0a01YSGFIemFmSG1PRXczaVU4cENHWmhQMWo5eVZiK1RLNFNOMnRSYkxOQwp6S2pHNVA1SFFnT2tsZHJIWldSdGVWTFFOeEhrVm5HQUlqMHFjTFg0M1dRWVZUUTdjWVh3MzNFck9Kd3M5Y1NjCkZCenV6eU1yYVdtUDI2eEV1NXVDY0J2S0syWVF5NGZob2dDYVdlbUdmMy96QnpYL3hEUktYS25wTCtJMGRobmUKd2YxRUJLMk5XeXNObVhmU1VtYkE2NVBUTlZ3TWNVZ1dDeEwrMmNpcVFWTVdnSEljenk4MVE5eFZ2UEFGeVRWZApPYzZlL290SjVMdUtWQUZkbkNGcVNsamsvUmhudDZIei9TSkVzVk01ZHdJeHk1bzhSTkFtNW9BdVR5WmZpMS9nCnVmNFhjVUkwcVJtV1U3Wk5TeHFEUWlGL2hQRDFBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlFweFRYRTNQdTl1TVZMRWVFQWJKWjc3Ty9HUVRBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFnc1VjSGd3NEYxMnBsSkhKRXRiaTdUTzVHMW5oeWtUT21RSDA3Qm5CCmhLbFNadlFKeEdVYjQ1WWN4djJ1RlhCYWZmYm9TMHNKMmhLUUZwNGN4WCtJQStpVWlyVjJ1aDNOeWlJWEIxOGMKN3U5ZkYzbGk4VWlXZjk4L1VHdmRTWWFjUWRidDE1cmMzZ3pKd2RqZTdrb2M3TzZrNWdEOFc1VzhSUVYvcEpJUQpzUjRjZytGSWc5OVVtTEZLTHJGa1dRMCtYazZVQVVzSkdNKzVVQUpwaTZSaFAyZ0NFODFsYUdaZUpVQ0RpMWxrCnZ5ZlVoQnBBbWJVWmoxQUNudnFzMzBKemplM1AzTXljSWZxc3ZEV2RPMFlZVnlDMVpMZmZzSG1uVzhEZW85SysKN29icHVIaElsMExpYyt2SExKZlZ2b2VweTIzWDg0RU11MGc2MXR4cWFzbTlIQT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/kubelet-serving-ca-bundle.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIakNDQWdhZ0F3SUJBZ0lJQVpJQ3NiQzZvWnd3RFFZSktvWklodmNOQVFFTEJRQXdMVEVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SY3dGUVlEVlFRREV3NXJkV0psYkdWMExYTnBaMjVsY2pBZUZ3MHlOVEEyTURZeApPREEyTWpGYUZ3MHlOVEEyTURjeE9EQTJNakZhTUMweEVqQVFCZ05WQkFzVENXOXdaVzV6YUdsbWRERVhNQlVHCkExVUVBeE1PYTNWaVpXeGxkQzF6YVdkdVpYSXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRQ203VlArakFPdklYdFg0a01YSGFIemFmSG1PRXczaVU4cENHWmhQMWo5eVZiK1RLNFNOMnRSYkxOQwp6S2pHNVA1SFFnT2tsZHJIWldSdGVWTFFOeEhrVm5HQUlqMHFjTFg0M1dRWVZUUTdjWVh3MzNFck9Kd3M5Y1NjCkZCenV6eU1yYVdtUDI2eEV1NXVDY0J2S0syWVF5NGZob2dDYVdlbUdmMy96QnpYL3hEUktYS25wTCtJMGRobmUKd2YxRUJLMk5XeXNObVhmU1VtYkE2NVBUTlZ3TWNVZ1dDeEwrMmNpcVFWTVdnSEljenk4MVE5eFZ2UEFGeVRWZApPYzZlL290SjVMdUtWQUZkbkNGcVNsamsvUmhudDZIei9TSkVzVk01ZHdJeHk1bzhSTkFtNW9BdVR5WmZpMS9nCnVmNFhjVUkwcVJtV1U3Wk5TeHFEUWlGL2hQRDFBZ01CQUFHalFqQkFNQTRHQTFVZER3RUIvd1FFQXdJQ3BEQVAKQmdOVkhSTUJBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlFweFRYRTNQdTl1TVZMRWVFQWJKWjc3Ty9HUVRBTgpCZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFnc1VjSGd3NEYxMnBsSkhKRXRiaTdUTzVHMW5oeWtUT21RSDA3Qm5CCmhLbFNadlFKeEdVYjQ1WWN4djJ1RlhCYWZmYm9TMHNKMmhLUUZwNGN4WCtJQStpVWlyVjJ1aDNOeWlJWEIxOGMKN3U5ZkYzbGk4VWlXZjk4L1VHdmRTWWFjUWRidDE1cmMzZ3pKd2RqZTdrb2M3TzZrNWdEOFc1VzhSUVYvcEpJUQpzUjRjZytGSWc5OVVtTEZLTHJGa1dRMCtYazZVQVVzSkdNKzVVQUpwaTZSaFAyZ0NFODFsYUdaZUpVQ0RpMWxrCnZ5ZlVoQnBBbWJVWmoxQUNudnFzMzBKemplM1AzTXljSWZxc3ZEV2RPMFlZVnlDMVpMZmZzSG1uVzhEZW85SysKN29icHVIaElsMExpYyt2SExKZlZ2b2VweTIzWDg0RU11MGc2MXR4cWFzbTlIQT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/machine-config-server.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/machine-config-server.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURXRENDQWtDZ0F3SUJBZ0lJWTAwTFRlQXZCaWt3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZd05qRTRNRFl4T0ZvWApEVE0xTURZd05ERTRNRFl4T0Zvd0p6RWxNQ01HQTFVRUF4TWNjM2x6ZEdWdE9tMWhZMmhwYm1VdFkyOXVabWxuCkxYTmxjblpsY2pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBSmpUYUMzQ25scTkKZUUreWNOL1BYaEpnZ3NOTjBDTzhnYlhpLzBjV0Q1aFZsY0pTa2MzaFVmd2k3eTNmZVJKSWZ6U1AzbDk2cmJsVApUeHNQUG1CTkFBeG56VmdXVmt5dGZOekxKRDh6aG5JT3ZFZDRYZTAva01nT3gxVTVxSkNYWjM3WmFwQzlHckFICkxsRUJCaDVjbFJWUVdTQnlKZURWMktJRWlXaDF3WlhGTlF3UmJxSzAzbDcvTTVHT2pNMUJEeGF0WE9NWVNEaVIKOEhIUkwzT3B0ZmU2bVNuYzJEMUtkeDNnbVRUemIrb0hTUlZqRXk2WVlpQUhTM2NkNFl2NTEwZDBnQVAydzREMApITGNEbXBKdTdXcXR0OS83YVFBR3ZMdHY0NE1aSzNJMXIyUENHb21Pb0VvaG0vWG9TeUs3STI4QTdDcklEUEk2CkZQY2VMV1haUzlrQ0F3RUFBYU9CaURDQmhUQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBVEFNQmdOVkhSTUIKQWY4RUFqQUFNQjBHQTFVZERnUVdCQlNvYXB4bVlJdUk0R04yZGVvY1FEYUFlVXZRaVRBZkJnTlZIU01FR0RBVwpnQlJNclpkMEVQVUhqNEk4SDU2N09oNWZ2OXdtcURBZ0JnTlZIUkVFR1RBWGdoVmhjR2t0YVc1MExuTnVieTVzCllXSXViRzlqWVd3d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFIaTduVHdYTW54aHRHOUdGd3JwbGF1cHlqUDMKVHhQNWtuNG90RXFyc25KaWxUMHFsbVkveWZIMWdIUUZmbDBaQlY5KzllT1BtMWFiK0pyY3hTKy9wMzhsZ1NsUQptZ0ZvQWFRdGZJMy9ZY3krbGFua0o4MjJLZndHTVpGNGhjSzNONTN5NERFdlNscDdVeHQxWWJ1dTR6OTVDTHV4CklDTWR4QlJKakVuTkF3eXB5VS85QmM3TDlJUUdEYWRseHR4SUlvZE1ITHptNkZvVmtXd0tpTWFkS3FVb0J3MTcKUGxBaVVDOXl5M2NaQkRna0FDdEttbHc2ZzMyZkp5ZDhUVlQrM2JVOFFWbC9SS0VJTHh0RDdCL2EwRWdBRitYcApiODRXSloxUGlxYno1Y3hUOEc0T2JuczUwS21NSmlKNWVWK0JkZHBpTUVEQ0RlRVY4aFVvMEthcjdlMD0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/service-account.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/service-account.pub", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBSU0EgUFVCTElDIEtFWS0tLS0tCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBbk5IcG4xSVd0S1RwQVExWmZteDIKSUVCYWdqVllhbWwwOWZvaGIyS21SelNYdGhmanRVdisyQlVXOVZnTGVoY0FDcGYrcGhwUHJ6bFpzOThLQnZ4QwpWam5qMlliSC9wWE5ucmhWLzRpc0dPd1NkL3ZEYmwyVDJ4dU1xSlpPYThYd1RlaGsrYUtTRi80c1JobjhNeWFhCkdBL1NJMTM2c3pKeGxkcVp3dXBGNzZUeTBYUE45VG9lNzlmUlFod2Zhc3BGbW9xZzd5UDl1S1F3eW5SN0xWOHIKOHo4NmJXY0Mrc3dLMGQwT0V4L3VSWHEwdDRuR1lkb0o3RXBaUzZqcnZJRzF2VkpCaFVER0dJWituYWtuSHN4WAp0YUJWdTVIY3hEcTY1RWwySWtkbWpIQXlIaUpuUUtESGJCVHVvTHpoMTBlRGM4N2hwbXNaTHZiUDA1MnErUVpQClFRSURBUUFCCi0tLS0tRU5EIFJTQSBQVUJMSUMgS0VZLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/journal-gatewayd.key", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/journal-gatewayd.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURZRENDQWtpZ0F3SUJBZ0lJY2hmVHl2TVk2dXd3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZd05qRTRNRFl4T0ZvWApEVE0xTURZd05ERTRNRFl4T1Zvd09URWNNQm9HQTFVRUNoTVRUM0JsYmxOb2FXWjBJRUp2YjNSemRISmhjREVaCk1CY0dBMVVFQXhNUWFtOTFjbTVoYkMxbllYUmxkMkY1WkRDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVAKQURDQ0FRb0NnZ0VCQUx1ZlhIRVUzOGMzUUhKWFFxbDl1MzVCb05KNWlqKzhqQWZpdCtHOWVWRGNYalA3aE8vMwpXeHdSYVp0OVFiMjlnQ25TaG1vOVJrK0FUcWtNMUdzM05JeFNMdTNqVjZwNk5QakErZVVMVXIycW42dVVTWVhMCjQzL2RpQlhXZWtmcVR0WXN3d2tRN2lJbk5Ea0luYnA3MEtXQlViNTA5ekc5UDUzN3Y3eHAwQlZ2MmRzODd5dXIKbkpsRG1BSnBtcVczTlBsbFM3QzdFcXVMUnZnd0tWVWx1Tjl1dnowWEhYd1U1ZVBCelhvbDhld2xzbUZLa0FXdQo2QmVmWlRKOHo1dU1mdmp1MGZ0SFVCb2x3Y0lsLzVGNnNRQ2hLSzcxMVdKS0drQ2pWSVdTOWhWdkNGdHpFQWhtCmE0RzF1V0l6SE12UTdmS2trMG9tMVl0Y0VKZ1FCMi83cGFjQ0F3RUFBYU4vTUgwd0RnWURWUjBQQVFIL0JBUUQKQWdXZ01CMEdBMVVkSlFRV01CUUdDQ3NHQVFVRkJ3TUJCZ2dyQmdFRkJRY0RBakFNQmdOVkhSTUJBZjhFQWpBQQpNQjBHQTFVZERnUVdCQlEyblV2QTd4czIrSlJhL0dJcWlEOG9UbXVRMXpBZkJnTlZIU01FR0RBV2dCUk1yWmQwCkVQVUhqNEk4SDU2N09oNWZ2OXdtcURBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQXR1VGRmTFY5TEg2VWxtbUcKRnkvelZXY1lYaHhJUkV0OVN0V2RvWmlzaWp1SFhyWmRheVAwSWczalhITHN3T1FMYWxtUUswMTZtOUIxNGVCSQplMmtOSlJNZi82N2VBRVJxKyt6Wm5oNWNldTlrYk1Gcm9ZSkszZkpKNy8wbUZRbG1pcTNwWjEwbDR0dzdwWG1uClR2dS9IRXplZTBBZWFzWXVvbDFvM2tEc05Xd2JuNHY4NzdveFFKRlQyWWhScktxSnhRUlhkN2tBbWdzUDdPcEoKeisvcVZuNWlvZTZzNi94VFVmQmF2bW1FME9ESks5R1VteHlYTEYrTWZjS3h4MVZxYnFCd2NEWkFYanZqSFRMTQpKNzdBSUxWdDQ5Y25idGZDTkF5QWV1V3dYSXMzL2tIbzdTVDJLOCtsRE1HVVJyNTJ0eUczWCthSU5hRjU1cXpjCnd1SDlkdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/tls/root-ca.crt", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURFRENDQWZpZ0F3SUJBZ0lJZWlEaklQTGF5TGt3RFFZSktvWklodmNOQVFFTEJRQXdKakVTTUJBR0ExVUUKQ3hNSmIzQmxibk5vYVdaME1SQXdEZ1lEVlFRREV3ZHliMjkwTFdOaE1CNFhEVEkxTURZd05qRTRNRFl4T0ZvWApEVE0xTURZd05ERTRNRFl4T0Zvd0pqRVNNQkFHQTFVRUN4TUpiM0JsYm5Ob2FXWjBNUkF3RGdZRFZRUURFd2R5CmIyOTBMV05oTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3cXZhM0tOUzc5bGsKcCtiRnArc3dIV0tTWFM1UDFmcUhYL1g0YVEvYVZzOUVSTXdkT0x4ditKejVGcWVrSEc5SHBqU3VTVjJmc2hNMwphRWt5YThuK3pyQk9XR0NDUENWMllIbTRUME1Sd005TlFFdmY0cjZISHBQY204cnBacU1XT0o5bFQ4MWJqUGlOCkt5UVRyZEpxZlVMZHFWZXdUY0VUN2VGSUwwZ0dyVFE4VFpxSE0zZUpWSU1Qdk9aeTFocE40TXUvU2JkbElLQmUKQllnSjF4ZlNCTUJNQXUwL0dPcTRKY2hyVGhxd0VBZUs0TEo5S3dPVTB2cDNpZDBGTk9XbmdnakpCMWl3NlNyeAp3eGNlN1dId1BzcjVxM0JZRXFrNEZ6cGY1TFZXMEJxL3NlRUc3U1BIeko3REVyWTNXdE9MUXhteWhUZm1ZZFUrClVrNjh3TUhUbXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBcVF3RHdZRFZSMFRBUUgvQkFVd0F3RUIKL3pBZEJnTlZIUTRFRmdRVVRLMlhkQkQxQjQrQ1BCK2V1em9lWDcvY0pxZ3dEUVlKS29aSWh2Y05BUUVMQlFBRApnZ0VCQU81S2doZnI1d2JlZUhkVXllcHIxZUxOUHgrSEZpd25CblFIcENpSjB2dVNOUXVPWmpVM1grZ3pkUTNWCktoL2ZZdENqRnlCTEFWdm5yd3hNcnJaNWxiS0F0ckZzTG95b0FDQVh0blk2UVB2aTBlbFVkVjVndEtPbXdteUsKRWVnN1I5dzh2WDRFQnVhd0ovOHcveHlGa2VxaVVZQkJETzdha3VWVDBLVVR2dm82ckQzSWFaU1B5czlKNTVQWgpSYnRUYkt6UUJaM3ZackhJVS9mMlJ5YkwyQnlpVDMxVzlrY0R1SHJCam43S1o1SHRhVmpzdUw1ZS9vbW9tb1FCCnlxbjJPcnhjZ3NwSEZuOEFwMXd5Ujl2N216SlA5ZXBZUmdHMEl5Wk0zcVlGU1RRSW9TSDczOEFldkp6b2doTXcKZGN6ZWhkK2liSkN3bFVxYkRBQUl2NE91RGprPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="}, "mode": 420}, {"overwrite": true, "path": "/opt/openshift/bootstrap-in-place/bootstrap-in-place-post-reboot.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1b0UgcGlwZWZhaWwgIyMgLUUgb3B0aW9uIHdpbGwgY2F1c2UgZnVuY3Rpb25zIHRvIGluaGVyaXQgdHJhcAoKZWNobyAiUnVubmluZyBib290a3ViZSBib290c3RyYXAtaW4tcGxhY2UgcG9zdCByZWJvb3QiCmV4cG9ydCBLVUJFQ09ORklHPS9ldGMva3ViZXJuZXRlcy9ib290c3RyYXAtc2VjcmV0cy9rdWJlY29uZmlnCgpmdW5jdGlvbiB3YWl0X2Zvcl9hcGkgewogIHVudGlsIG9jIGdldCBjc3IgJj4gL2Rldi9udWxsCiAgZG8KICAgIGVjaG8gIldhaXRpbmcgZm9yIGFwaSAuLi4iCiAgICBzbGVlcCA1CiAgZG9uZQp9CgojIFRoaXMgaXMgcmVxdWlyZWQgc2luY2UgdGhlIHByb2dyZXNzIHNlcnZpY2UgKGh0dHBzOi8vZ2l0aHViLmNvbS9vcGVuc2hpZnQvaW5zdGFsbGVyL2Jsb2IvZGQ5MDQ3YzRjMTE5ZTk0MjMzMWY3MDJhNGI3ZGE4NWM2MDA0MmRhNS9kYXRhL2RhdGEvYm9vdHN0cmFwL2ZpbGVzL3Vzci9sb2NhbC9iaW4vcmVwb3J0LXByb2dyZXNzLnNoI0wyMi1MMzMpLAojIHVzdWFsbHkgZGVkaWNhdGVkIHRvIGNyZWF0aW5nIHRoZSBib290c3RyYXAgQ29uZmlnTWFwLCB3aWxsIGZhaWwgdG8gY3JlYXRlIHRoaXMgY29uZmlnbWFwIGluIGNhc2Ugb2YgYm9vdHN0cmFwLWluLXBsYWNlIHNpbmdsZSBub2RlIGRlcGxveW1lbnQsIAojIGR1ZSB0byB0aGUgbGFjayBvZiBhIGNvbnRyb2wgcGxhbmUgd2hlbiBib290a3ViZSBpcyBjb21wbGV0ZQpmdW5jdGlvbiBzaWduYWxfYm9vdHN0cmFwX2NvbXBsZXRlIHsKICB1bnRpbCBvYyBnZXQgY20gYm9vdHN0cmFwIC1uIGt1YmUtc3lzdGVtICY+IC9kZXYvbnVsbAogIGRvCiAgICBlY2hvICJDcmVhdGluZyBib290c3RyYXAgY29uZmlnbWFwIC4uLiIKICAgIG9jIGNyZWF0ZSBjbSBib290c3RyYXAgLW4ga3ViZS1zeXN0ZW0gLS1mcm9tLWxpdGVyYWwgc3RhdHVzPWNvbXBsZXRlIHx8IHRydWUKICAgIHNsZWVwIDUKICBkb25lCn0KCmZ1bmN0aW9uIHJlbGVhc2VfbGVhc2UgewogIGxvY2FsIG5zPSIkMSIKICBsb2NhbCBsZWFzZT0iJDIiCgogIHVudGlsIFsgIiQob2MgZ2V0IGxlYXNlcyAtbiAiJHtuc30iICIke2xlYXNlfSIgfCBncmVwIC1jICIke2xlYXNlfSIpIiAtZXEgMCBdOwogIGRvCiAgICBlY2hvICJEZWxldGluZyAke25zfSAke2xlYXNlfSBsZWFzZSIKICAgIG9jIGRlbGV0ZSBsZWFzZXMgLW4gIiR7bnN9IiAiJHtsZWFzZX0iIHx8IHNsZWVwIDUKICBkb25lCiAgdW50aWwgWyAiJChvYyBnZXQgY20gLW4gIiR7bnN9IiAiJHtsZWFzZX0iIHwgZ3JlcCAtYyAiJHtsZWFzZX0iKSIgLWVxIDAgXTsKICBkbwogICAgZWNobyAiRGVsZXRpbmcgJHtuc30gJHtsZWFzZX0gY20iCiAgICBvYyBkZWxldGUgY20gLW4gIiR7bnN9IiAiJHtsZWFzZX0iIHx8IHNsZWVwIDUKICBkb25lCn0KCmZ1bmN0aW9uIHJlbGVhc2VfY3ZvX2xlYXNlIHsKICBpZiBbICEgLWYgL29wdC9vcGVuc2hpZnQvcmVsZWFzZV9jdm9fbGVhc2UuZG9uZSBdCiAgdGhlbgogICAgcmVsZWFzZV9sZWFzZSBvcGVuc2hpZnQtY2x1c3Rlci12ZXJzaW9uIHZlcnNpb24KICAgIHRvdWNoIC9vcHQvb3BlbnNoaWZ0L3JlbGVhc2VfY3ZvX2xlYXNlLmRvbmUKICBmaQp9CgpmdW5jdGlvbiByZWxlYXNlX2NwY19sZWFzZSB7CiAgaWYgWyAhIC1mIC9vcHQvb3BlbnNoaWZ0L3JlbGVhc2VfY3BjX2xlYXNlLmRvbmUgXQogIHRoZW4KICAgIHJlbGVhc2VfbGVhc2Uga3ViZS1zeXN0ZW0gY2x1c3Rlci1wb2xpY3ktY29udHJvbGxlci1sb2NrCiAgICB0b3VjaCAvb3B0L29wZW5zaGlmdC9yZWxlYXNlX2NwY19sZWFzZS5kb25lCiAgZmkKfQoKZnVuY3Rpb24gcmVzdGFydF9rdWJlbGV0IHsKICBlY2hvICJXYWl0aW5nIGZvciBrdWJlLWFwaXNlcnZlci1vcGVyYXRvciIKICB1bnRpbCBbICIkKG9jIGdldCBwb2QgLW4gb3BlbnNoaWZ0LWt1YmUtYXBpc2VydmVyLW9wZXJhdG9yIC0tc2VsZWN0b3I9J2FwcD1rdWJlLWFwaXNlcnZlci1vcGVyYXRvcicgLW8ganNvbnBhdGg9J3suaXRlbXNbKl0uc3RhdHVzLmNvbmRpdGlvbnNbPyhALnR5cGU9PSJSZWFkeSIpXS5zdGF0dXN9JyB8IGdyZXAgLWMgIlRydWUiKSIgLWVxIDEgXTsKICBkbwogICAgZWNobyAiV2FpdGluZyBmb3Iga3ViZS1hcGlzZXJ2ZXItb3BlcmF0b3IgcmVhZHkgY29uZGl0aW9uIHRvIGJlIFRydWUiCiAgICBvYyBnZXQgLS1yYXc9Jy9yZWFkeXonICY+IC9kZXYvbnVsbCB8fCBlY2hvICJBcGkgaXMgbm90IGF2YWlsYWJsZSIKICAgIHNsZWVwIDEwCiAgZG9uZQoKICBlY2hvICJSZXN0YXJ0aW5nIGt1YmVsZXQiCiAgIyBkYWVtb24tcmVsb2FkIGlzIHJlcXVpcmVkIGJlY2F1c2UgL2V0Yy9zeXN0ZW1kL3N5c3RlbS9rdWJlbGV0LnNlcnZpY2UuZC8yMC1ub2RlbmV0LmNvbmYgaXMgYWRkZWQgYWZ0ZXIga3ViZWxldCBzdGFydGVkCiAgc3lzdGVtY3RsIGRhZW1vbi1yZWxvYWQKICBzeXN0ZW1jdGwgcmVzdGFydCBrdWJlbGV0CgogIHdoaWxlIGdyZXAgYm9vdHN0cmFwLWt1YmUtYXBpc2VydmVyIC9ldGMva3ViZXJuZXRlcy9tYW5pZmVzdHMva3ViZS1hcGlzZXJ2ZXItcG9kLnlhbWw7CiAgZG8KICAgIGVjaG8gIldhaXRpbmcgZm9yIGt1YmUtYXBpc2VydmVyIHRvIGFwcGx5IHRoZSBuZXcgc3RhdGljIHBvZCBjb25maWd1cmF0aW9uIgogICAgc2xlZXAgMTAKICBkb25lCgogIGVjaG8gIlJlc3RhcnRpbmcga3ViZWxldCIKICBzeXN0ZW1jdGwgcmVzdGFydCBrdWJlbGV0Cn0KCmZ1bmN0aW9uIGFwcHJvdmVfY3NyIHsKICBlY2hvICJXYWl0aW5nIGZvciBub2RlIHRvIHJlcG9ydCByZWFkeSBzdGF0dXMiCiAgIyB1c2UgWypdIGFuZCBub3QgWzBdIGluIHRoZSBqc29ucGF0aCBiZWNhdXNlIHRoZSBub2RlIHJlc291cmNlIG1heSBub3QgaGF2ZSBiZWVuIGNyZWF0ZWQgeWV0CiAgdW50aWwgWyAiJChvYyBnZXQgbm9kZXMgLS1zZWxlY3Rvcj0nbm9kZS1yb2xlLmt1YmVybmV0ZXMuaW8vbWFzdGVyJyAtbyBqc29ucGF0aD0ney5pdGVtc1sqXS5zdGF0dXMuY29uZGl0aW9uc1s/KEAudHlwZT09IlJlYWR5IildLnN0YXR1c30nIHwgZ3JlcCAtYyAiVHJ1ZSIpIiAtZXEgMSBdOwogIGRvCiAgICBlY2hvICJBcHByb3ZpbmcgY3NycyAuLi4iCiAgICBvYyBnZXQgY3NyIC1vIGdvLXRlbXBsYXRlPSd7e3JhbmdlIC5pdGVtc319e3tpZiBub3QgLnN0YXR1c319e3subWV0YWRhdGEubmFtZX19e3siXG4ifX17e2VuZH19e3tlbmR9fScgfCB4YXJncyAtLW5vLXJ1bi1pZi1lbXB0eSBvYyBhZG0gY2VydGlmaWNhdGUgYXBwcm92ZSAmPiAvZGV2L251bGwgfHwgdHJ1ZQogICAgc2xlZXAgMTAKICBkb25lCiAgZWNobyAibm9kZSBpcyByZWFkeSIKfQoKZnVuY3Rpb24gd2FpdF9mb3JfY3ZvIHsKICBlY2hvICJXYWl0aW5nIGZvciBjdm8iCiAgdW50aWwgWyAiJChvYyBnZXQgY2x1c3RlcnZlcnNpb24gLW8ganNvbnBhdGg9J3suaXRlbXNbKl0uc3RhdHVzLmNvbmRpdGlvbnNbPyhALnR5cGU9PSJBdmFpbGFibGUiKV0uc3RhdHVzfScpIiA9PSAiVHJ1ZSIgXTsKICBkbwogICAgZWNobyAiU3RpbGwgd2FpdGluZyBmb3IgY3ZvIC4uLiIKICAgICMgcHJpbnQgdGhlIG5vdCByZWFkeSBvcGVyYXRvcnMgbmFtZXMgYW5kIG1lc3NhZ2UKICAgIG9jIGdldCBjbHVzdGVyb3BlcmF0b3IgLW8ganNvbnBhdGg9J3tyYW5nZSAuaXRlbXNbKl19e0AubWV0YWRhdGEubmFtZX06IHtyYW5nZSBALnN0YXR1cy5jb25kaXRpb25zWz8oQC50eXBlPT0iQXZhaWxhYmxlIildfXtALnR5cGV9PXtALnN0YXR1c30ge0AubWVzc2FnZX17IlxuIn17ZW5kfXtlbmR9JyB8IGdyZXAgLXYgIkF2YWlsYWJsZT1UcnVlIiB8fCB0cnVlCiAgICBzbGVlcCAyMAogIGRvbmUKfQoKZnVuY3Rpb24gcmVzdG9yZV9jdm9fb3ZlcnJpZGVzIHsKICAgIGVjaG8gIlJlc3RvcmluZyBDVk8gb3ZlcnJpZGVzIgogICAgdW50aWwgXAogICAgICAgIG9jIHBhdGNoIGNsdXN0ZXJ2ZXJzaW9uLmNvbmZpZy5vcGVuc2hpZnQuaW8gdmVyc2lvbiBcCiAgICAgICAgICAgIC0tdHlwZT1tZXJnZSBcCiAgICAgICAgICAgIC0tcGF0Y2gtZmlsZT0vb3B0L29wZW5zaGlmdC9vcmlnaW5hbF9jdm9fb3ZlcnJpZGVzLnBhdGNoCiAgICBkbwogICAgICAgIGVjaG8gIlRyeWluZyBhZ2FpbiB0byByZXN0b3JlIENWTyBvdmVycmlkZXMgLi4uIgogICAgICAgIHNsZWVwIDEwCiAgICBkb25lCn0KCmZ1bmN0aW9uIGNsZWFuIHsKICBpZiBbIC1kICIvZXRjL2t1YmVybmV0ZXMvYm9vdHN0cmFwLXNlY3JldHMiIF07IHRoZW4KICAgICBybSAtcmYgL2V0Yy9rdWJlcm5ldGVzL2Jvb3RzdHJhcC0qCiAgZmkKCiAgcm0gLXJmIC91c3IvbG9jYWwvYmluL2luc3RhbGxlci1nYXRoZXIuc2gKICBybSAtcmYgL3Vzci9sb2NhbC9iaW4vaW5zdGFsbGVyLW1hc3RlcnMtZ2F0aGVyLnNoCiAgcm0gLXJmIC92YXIvbG9nL2xvZy1idW5kbGUtYm9vdHN0cmFwLnRhci5negogIHJtIC1yZiAvb3B0L29wZW5zaGlmdAoKICBzeXN0ZW1jdGwgZGlzYWJsZSBib290a3ViZS5zZXJ2aWNlCn0KCndhaXRfZm9yX2FwaQpzaWduYWxfYm9vdHN0cmFwX2NvbXBsZXRlCnJlbGVhc2VfY3ZvX2xlYXNlCnJlbGVhc2VfY3BjX2xlYXNlCnJlc3RvcmVfY3ZvX292ZXJyaWRlcwphcHByb3ZlX2NzcgpyZXN0YXJ0X2t1YmVsZXQKd2FpdF9mb3JfY3ZvCmNsZWFuCg=="}, "mode": 384}, {"overwrite": true, "path": "/opt/openshift/bootstrap-in-place/master-update.fcc", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 384}, {"overwrite": true, "path": "/usr/local/bin/bootstrap-in-place.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,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"}, "mode": 365}, {"overwrite": true, "path": "/usr/local/bin/install-to-disk.sh", "user": {"name": "root"}, "contents": {"source": "data:text/plain;charset=utf-8;base64,IyEvdXNyL2Jpbi9lbnYgYmFzaApzZXQgLWV1b0UgcGlwZWZhaWwgIyMgLUUgb3B0aW9uIHdpbGwgY2F1c2UgZnVuY3Rpb25zIHRvIGluaGVyaXQgdHJhcAoKIyBUaGlzIHNjcmlwdCBpcyBleGVjdXRlZCBieSBpbnN0YWxsLXRvLWRpc2sgc2VydmljZSB3aGVuIGluc3RhbGxpbmcgc2luZ2xlIG5vZGUgd2l0aCBib290c3RyYXAgaW4gcGxhY2UKCi4gL3Vzci9sb2NhbC9iaW4vYm9vdHN0cmFwLXNlcnZpY2UtcmVjb3JkLnNoCgpyZWNvcmRfc2VydmljZV9zdGFnZV9zdGFydCAid2FpdC1mb3ItYm9vdGt1YmUiCmVjaG8gIldhaXRpbmcgZm9yIC9vcHQvb3BlbnNoaWZ0Ly5ib290a3ViZS5kb25lIgp1bnRpbCBbIC1mIC9vcHQvb3BlbnNoaWZ0Ly5ib290a3ViZS5kb25lIF07IGRvCiAgc2xlZXAgNQpkb25lCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N1Y2Nlc3MKCmlmIFsgISAtZiBjb3Jlb3MtaW5zdGFsbGVyLmRvbmUgXTsgdGhlbgogIHJlY29yZF9zZXJ2aWNlX3N0YWdlX3N0YXJ0ICJjb3Jlb3MtaW5zdGFsbGVyIgogICMgV3JpdGUgaW1hZ2UgKyBpZ25pdGlvbiB0byBkaXNrCiAgZWNobyAiRXhlY3V0aW5nIGNvcmVvcy1pbnN0YWxsZXIgd2l0aCB0aGUgZm9sbG93aW5nIG9wdGlvbnM6IGluc3RhbGwgLWkgL29wdC9vcGVuc2hpZnQvbWFzdGVyLmlnbiAvZGV2L3ZkYSIKICBjb3Jlb3MtaW5zdGFsbGVyIGluc3RhbGwgLW4gLWkgL29wdC9vcGVuc2hpZnQvbWFzdGVyLmlnbiAvZGV2L3ZkYQoKICB0b3VjaCBjb3Jlb3MtaW5zdGFsbGVyLmRvbmUKICByZWNvcmRfc2VydmljZV9zdGFnZV9zdWNjZXNzCmZpCgpyZWNvcmRfc2VydmljZV9zdGFnZV9zdGFydCAicmVib290IgplY2hvICJHb2luZyB0byByZWJvb3QiCnNodXRkb3duIC1yICsxICJCb290c3RyYXAgY29tcGxldGVkLCBzZXJ2ZXIgaXMgZ29pbmcgdG8gcmVib290LiIKdG91Y2ggL29wdC9vcGVuc2hpZnQvLmluc3RhbGwtdG8tZGlzay5kb25lCmVjaG8gIkRvbmUiCnJlY29yZF9zZXJ2aWNlX3N0YWdlX3N1Y2Nlc3MK"}, "mode": 365}]}, "systemd": {"units": [{"contents": "[Unit]\nDescription=Approve CSRs during bootstrap phase\nWants=bootkube.service\nAfter=bootkube.service\n\n[Service]\nExecStart=/usr/local/bin/approve-csr.sh /opt/openshift/auth/kubeconfig-loopback\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "approve-csr.service"}, {"contents": "[Unit]\nDescription=Bootstrap a Kubernetes cluster\nRequires=crio-configure.service\nWants=kubelet.service\nAfter=kubelet.service crio-configure.service\nConditionPathExists=!/opt/openshift/.bootkube.done\n\n[Service]\nWorkingDirectory=/opt/openshift\nExecStart=/usr/local/bin/bootkube.sh\n\nRestart=on-failure\nRestartSec=5s\n", "name": "bootkube.service"}, {"contents": "[Unit]\nDescription=Configure CRI-O to use the pause image\nAfter=release-image.service\nRequires=release-image.service\nBefore=crio.service\n\n[Service]\nType=oneshot\nExecStart=/usr/local/bin/crio-configure.sh\nRemainAfterExit=true\n\n[Install]\nRequiredBy=crio.service\n", "name": "crio-configure.service"}, {"contents": "[Unit]\nDescription=Kubernetes Kubelet\nWants=rpc-statd.service crio.service release-image.service\nAfter=crio.service release-image.service\n[Service]\nType=notify\nNotifyAccess=all\nExecStartPre=/bin/mkdir --parents /etc/kubernetes/manifests\nExecStartPre=/bin/mkdir --parents /etc/kubernetes/kubelet-plugins/volume/exec\nExecStartPre=/usr/local/bin/kubelet-pause-image.sh\nEnvironment=KUBELET_RUNTIME_REQUEST_TIMEOUT=10m\nEnvironmentFile=-/etc/kubernetes/kubelet-env\nEnvironmentFile=-/etc/kubernetes/kubelet-pause-image-override\n\nExecStart=/usr/local/bin/kubelet.sh\n\nRestart=always\nRestartSec=10\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "kubelet.service"}, {"contents": "[Unit]\nDescription=Report the completion of the cluster bootstrap process\n# Workaround for https://github.com/systemd/systemd/issues/1312\nWants=bootkube.service\nAfter=bootkube.service\n\n[Service]\nExecStart=/usr/local/bin/report-progress.sh /opt/openshift/auth/kubeconfig\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "progress.service"}, {"contents": "", "name": "release-image-pivot.service"}, {"contents": "[Unit]\nDescription=Download the OpenShift Release Image\nWants=network-online.target\nAfter=network-online.target\n\n[Service]\nType=oneshot\nExecStart=/usr/local/bin/release-image-download.sh\nRemainAfterExit=true\n", "name": "release-image.service"}, {"dropins": [{"contents": "[Service]\nExecStart=\nExecStart=/usr/lib/systemd/systemd-journal-gatewayd \\\n  --key=/opt/openshift/tls/journal-gatewayd.key \\\n  --cert=/opt/openshift/tls/journal-gatewayd.crt \\\n  --trust=/opt/openshift/tls/root-ca.crt\n", "name": "certs.conf"}], "name": "systemd-journal-gatewayd.service"}, {"dropins": [{"contents": "[Unit]\nConditionPathExists=/enoent\n", "name": "okd-machine-os-disabled.conf"}], "name": "zincati.service"}, {"contents": "# In RHEL8 the service uses DynamicUser=yes; we need to work both ways, so hence\n# we hack this by adding the user if it doesn't exist and chown the file, rather\n# than doing it in Ignition.\n# https://github.com/openshift/installer/pull/1445\n[Unit]\nDescription=Change ownership of journal-gatewayd.key\nBefore=systemd-journal-gatewayd.service\n\n[Service]\nType=oneshot\nRemainAfterExit=yes\nExecStart=/bin/sh -c \". /usr/local/bin/bootstrap-service-record.sh; if ! getent passwd systemd-journal-gateway &>/dev/null; then useradd -r systemd-journal-gateway; fi && chown systemd-journal-gateway: /opt/openshift/tls/journal-gatewayd.{crt,key}\"\n\n[Install]\nWantedBy=multi-user.target\n", "enabled": true, "name": "chown-gatewayd-key.service"}, {"contents": "", "enabled": true, "name": "systemd-journal-gatewayd.socket"}, {"contents": "[Unit]\nDescription=Install to disk\nRequires=bootkube.service\nWants=bootkube.service\nAfter=bootkube.service\nConditionPathExists=!/opt/openshift/.install-to-disk.done\n\n[Service]\nWorkingDirectory=/opt/openshift\nExecStart=/usr/local/bin/install-to-disk.sh\n\nRestart=on-failure\nRestartSec=5s\n\n[Install]\nWantedBy=multi-user.target", "enabled": true, "name": "install-to-disk.service"}]}}